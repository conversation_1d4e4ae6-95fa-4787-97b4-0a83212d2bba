import {
	req,
	uploadFile
} from '../../utils/req.js';
import siteInfo from '../../siteinfo.js';
export default {
	// 登录
	login(param) {
		return req.post("index/login", param)
	},
	appLogin(param) {
		return req.post("index/appLogin", param)
	},
	// 登录协议
	getLoginProtocol(param) {
		return req.post("index/getLoginProtocol", param)
	},
	// app登录配置
	getConfig(param) {
		// console.log("index/getConfig")
		return req.get("core/config/config", param)
	},
	// 上传图片
	base64ToImg(param) {
		return req.get("massage/app/IndexUser/base64ToImg", param)
	},
	// 获取配置
	getWebConfig(param) {
		return req.get("index/getWebConfig", param)
	},
	// 获取地图定位
	getMapInfo(param) {
		return req.get("massage/app/Index/getMapInfo", param)
	},
	// 解析二维码
	getWxCodeData(param) {
		return req.get("card/app/getWxCodeData", param)
	},
	// 系统配置
	configInfo(param) {
		return req.get("shop/app/Index/configInfo", param)
	},
	// 上传图片
	// uploadFile(param) {
	// 	return uploadFile("admin/app/wx/uploadFile", param)
	// },
	uploadFile(param) {
		return uploadFile("core/file/upload", param)
	},
	// 上传视频
	uploadVideo(param) {
		return uploadFile("admin/app/wx/uploadVideo", param)
	},
	//获取城市
	getCity(param) {
		return req.get("/massage/app/Index/getCity", param)
	},

	// APP登录相关接口 - 返回完整响应（包括header）
	// APP账号密码登录
	appLoginByPass(param) {
		return new Promise((resolve, reject) => {
			// 从store或本地存储获取地理位置信息
			const locationData = this.getLocationData();
			const requestData = {
				...param,
				...locationData
			};
				console.log(requestData)
			uni.request({
				url: `${siteInfo.siteroot}user/login/loginByPass`,
				method: 'POST',
				data: requestData,
				header: {
					'Content-Type': 'application/json'
				},
				success: (res) => {
					// 构造响应对象，包含data和header
					const response = {
						code: res.data.code,
						msg: res.data.msg,
						data: res.data.data,
						header: res.header
					};
					resolve(response);
				},
				fail: (error) => {
					reject(new Error('网络请求失败'));
				}
			});
		});
	},

	// APP短信验证码登录
	appLoginByCode(param) {
		return new Promise((resolve, reject) => {
			// 从store或本地存储获取地理位置信息
			const locationData = this.getLocationData();
			const requestData = {
				...param,
				...locationData
			};
			
			uni.request({
				url: `${siteInfo.siteroot}user/login/loginByCode`,
				method: 'POST',
				data: requestData,
				header: {
					'Content-Type': 'application/json'
				},
				success: (res) => {
					// 构造响应对象，包含data和header
					const response = {
						code: res.data.code,
						msg: res.data.msg,
						data: res.data.data,
						header: res.header
					};
					resolve(response);
				},
				fail: (error) => {
					reject(new Error('网络请求失败'));
				}
			});
		});
	},
	appLoginByWechat(param) {
		return new Promise((resolve, reject) => {
			// 从store或本地存储获取地理位置信息
			const locationData = this.getLocationData();
			const requestData = {
				...param,
				...locationData
			};
			console.log(requestData)
			uni.request({
				url: `${siteInfo.siteroot}user/login/wxLoginByApp`,
				method: 'POST',
				data: requestData,
				header: {
					'Content-Type': 'application/json',
					'isapp': 1
				},
				success: (res) => {
					// 构造响应对象，包含data和header
					const response = {
						code: res.data.code,
						msg: res.data.msg,
						data: res.data.data,
						header: res.header
					};
					resolve(response);
				},
				fail: (error) => {
					reject(new Error('网络请求失败'));
				}
			});
		});
	},
	// APP注册
	appRegister(param) {
		return new Promise((resolve, reject) => {
			// 从store或本地存储获取地理位置信息
			const locationData = this.getLocationData();
			const requestData = {
				...param,
				...locationData
			};
			
			uni.request({
				url: `${siteInfo.siteroot}user/login/register`,
				method: 'POST',
				data: requestData,
				header: {
					'Content-Type': 'application/json'
				},
				success: (res) => {
					resolve(res.data);
				},
				fail: (error) => {
					reject(new Error('网络请求失败'));
				}
			});
		});
	},

	// APP忘记密码
	appForgetPwd(param) {
		return new Promise((resolve, reject) => {
			// 从store或本地存储获取地理位置信息
			const locationData = this.getLocationData();
			const requestData = {
				...param,
				...locationData
			};
			
			uni.request({
				url: `${siteInfo.siteroot}user/login/forgetPwd`,
				method: 'POST',
				data: requestData,
				header: {
					'Content-Type': 'application/json'
				},
				success: (res) => {
					resolve(res.data);
				},
				fail: (error) => {
					reject(new Error('网络请求失败'));
				}
			});
		});
	},

	// 小程序微信登录
	appLoginByWechatMini(param) {
		return new Promise((resolve, reject) => {
			// 从store或本地存储获取地理位置信息
			const locationData = this.getLocationData();
			const requestData = {
				...param,
				...locationData
			};
			
			uni.request({
				url: `${siteInfo.siteroot}user/login/wxLogin`,
				method: 'POST',
				data: requestData,
				header: {
					'Content-Type': 'application/json'
				},
				success: (res) => {
					// 构造响应对象，包含data和header
					const response = {
						code: res.data.code,
						msg: res.data.msg,
						data: res.data.data,
						header: res.header
					};
					resolve(response);
				},
				fail: (error) => {
					reject(new Error('网络请求失败'));
				}
			});
		});
	},

	// 发送短信验证码
	sendSmsCode(param) {
		return new Promise((resolve, reject) => {
			uni.request({
				url: `${siteInfo.siteroot}core/sms/send`,
				method: 'POST',
				data: param,
				header: {
					'Content-Type': 'application/x-www-form-urlencoded'
				},
				success: (res) => {
					resolve(res.data);
				},
				fail: (error) => {
					reject(new Error('网络请求失败'));
				}
			});
		});
	},

	// 获取地理位置数据的方法
	getLocationData() {
		try {
			// 优先从本地存储获取地理位置信息
			const storedLocation = uni.getStorageSync('locationData');
			if (storedLocation && storedLocation.province) {
				return storedLocation;
			}
			
			// 如果本地存储中没有，返回空对象（登录接口会处理空值）
			return {
				province: '',
				city: '',
				county: '',
				address: '',
				lng: '',
				lat: ''
			};
		} catch (error) {
			console.error('获取地理位置数据失败:', error);
			return {
				province: '',
				city: '',
				county: '',
				address: '',
				lng: '',
				lat: ''
			};
		}
	}
}
