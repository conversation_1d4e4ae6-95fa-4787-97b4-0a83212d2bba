<template>
	<view class="page">
		<view v-if="serviceDet && serviceDet.length > 0">
			<view class="site_item" v-for="(item, index) in serviceDet" :key="index">
				<view class="image-container">
					<image :src="item.img || '/static/images/placeholder.png'" mode="scaleToFill"></image>
				</view>
				<view class="content">
					<view class="name">{{item.name}}</view>
					<view class="address">
						<view class="position" @tap="copyAddress(item.address)">
							<text>{{ item.address }}</text>
							<u-icon name="arrow-right" color="#333" size="10"></u-icon>
						</view>
						<view style="display: flex; flex-direction: column; align-items: flex-end; gap: 16rpx;" class="">
							<u-icon name="attach" size="28" @tap="seeImg(item.sunCode)"></u-icon>
							<u-icon
								name="phone-fill"
								color="#599eff"
								size="28"
								@tap="phoneDLD(item.tel)"
							></u-icon>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			serviceDet: [],
			lat: 33.038799,
			lng: 115.277,
			id:'',
			fuwuinfo: []
		}
	},
	onLoad(option) {
		console.log(option)
		this.id=option.id
		this.getfuwu();
	},
	methods: {
		seeImg(url) {
			uni.previewImage({
				urls: [url],
			});
		},
		phoneDLD(info) {
			uni.makePhoneCall({
				phoneNumber: info,
			});
		},
		copyAddress(address) {
			uni.setClipboardData({
				data: address,
				success: () => {
					uni.showToast({
						title: '地址已复制',
						icon: 'success'
					});
				}
			});
		},
		getfuwu() {
			this.$api.service.getagents({
				cityName: "阜阳市",
				latitude: this.lat,
				longitude: this.lng,
				pageNum: 1,
				serviceId:this.id,
				pageSize: 5
			}).then((res) => {
				console.log('fuwu response:', res);
				this.serviceDet = res.list || [];
				this.fuwuinfo = res.list || [];
			}).catch((err) => {
				console.error('getfuwu error:', err);
				this.serviceDet = [];
				this.fuwuinfo = [];
			});
		},
	}
}
</script>

<style scoped lang="scss">
.page {
	padding: 0 30rpx;

	.site_item {
		margin-top: 40rpx;
		min-height: 180rpx; /* Increased to accommodate vertical icons */
		display: flex;
		align-items: flex-start;

		.image-container {
			width: 182rpx;
			height: 136rpx;
			margin-right: 20rpx;
			overflow: hidden;
			border-radius: 10rpx;
			position: relative;

			image {
				width: 100%;
				height: 100%;
				display: block;
				position: absolute;
				top: 0;
				left: 0;
			}
		}

		.content {
			flex: 1;
			min-height: 100%;
			border-bottom: 2rpx solid #E9E9E9;

			.name {
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				white-space: normal;
			}

			.address {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 28rpx;

				.position {
					display: flex;
					align-items: center;

					text {
						font-size: 20rpx;
						font-weight: 400;
						color: #333333;
						white-space: normal;
					}
				}
			}
		}
	}
}
</style>