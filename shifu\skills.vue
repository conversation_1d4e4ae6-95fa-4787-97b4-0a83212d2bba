<template>
  <view class="page">
    <view class="main">
      <!-- 左侧父类分类列表 -->
      <view class="left">
        <scroll-view scroll-y="true" class="scrollL">
          <view v-if="loading" class="loading">
            <text>加载中...</text>
          </view>
          <view v-else-if="error" class="error">
            <text>{{ error }}</text>
          </view>
          <view v-else-if="!categories.length" class="no-content">
            <text>暂无分类数据</text>
          </view>
          <view
            v-else
            class="left_item"
            v-for="category in categories"
            :key="category.id"
            @tap="selectCategory(category.id)"
            :class="{ active: selectedCategoryId === category.id }"
          >
            <view class="category_name">{{ category.name }}</view>
          </view>
        </scroll-view>
      </view>

      <!-- 右侧子类标签列表 -->
      <view class="right">
        <scroll-view scroll-y="true" class="scrollR" :scroll-top="scrollTop" @scroll="scroll">
          <view v-if="loading" class="loading">
            <text>加载中...</text>
          </view>
          <view v-else-if="error" class="error">
            <text>{{ error }}</text>
          </view>
          <view v-else-if="currentCategory && currentCategory.children && currentCategory.children.length">
            <!-- 分类标题和全选功能 -->
            <view class="category_header">
              <view class="category_info">
                <view class="category_title">{{ currentCategory.name }}</view>
                <view class="category_stats">
                  <text class="total_count">共{{ currentCategory.children.length }}项</text>
                  <text class="selected_count">已选{{ getSelectedCount() }}项</text>
                </view>
              </view>
              <view class="select_all_btn" @click="toggleSelectAll()">
                <text class="select_all_text">{{ isAllSelected() ? '取消全选' : '全选' }}</text>
              </view>
            </view>

            <!-- 子类标签列表 - 改为两列布局 -->
            <view class="subcategory_grid">
              <view
                class="subcategory_item"
                v-for="subCategory in currentCategory.children"
                :key="subCategory.id"
                @click="toggleSelect(subCategory)"
                :class="{ active: subCategory.selected }"
              >
                <text class="subcategory_name">{{ subCategory.name }}</text>
                <view v-if="subCategory.selected" class="selected_icon">
                  <text>✓</text>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="no-content">
            <text>暂无子分类</text>
          </view>
        </scroll-view>
      </view>
    </view>
    <view class="footer">
      <button class="save_btn" @click="saveSettings">保存设置</button>
    </view>
  </view>
</template>

<script>
import $api from "@/api/index.js";

export default {
  data() {
    return {
      categories: [],
      selectedCategoryId: null,
      loading: false,
      error: null,
      serviceIds: [], // 存储选中的子类ID数组
      shifuId: '',
      userID: '',
      scrollTop: 0
    };
  },
  computed: {
    currentCategory() {
      return this.categories.find(cat => cat.id === this.selectedCategoryId) || null;
    }
  },
  methods: {
    // 选择父类分类
    selectCategory(id) {
      this.selectedCategoryId = id;
      this.scrollTop = 0; // 切换分类时重置滚动位置
    },

    // 切换子类选中状态
    toggleSelect(subCategory) {
      subCategory.selected = !subCategory.selected;
      
      // 更新serviceIds数组
      if (subCategory.selected) {
        if (!this.serviceIds.includes(subCategory.id)) {
          this.serviceIds.push(subCategory.id);
        }
      } else {
        this.serviceIds = this.serviceIds.filter(id => id !== subCategory.id);
      }
    },

    // 获取当前分类下已选中的数量
    getSelectedCount() {
      if (!this.currentCategory || !this.currentCategory.children) return 0;
      return this.currentCategory.children.filter(item => item.selected).length;
    },

    // 检查是否全部选中
    isAllSelected() {
      if (!this.currentCategory || !this.currentCategory.children) return false;
      return this.currentCategory.children.every(item => item.selected);
    },

    // 全选/取消全选
    toggleSelectAll() {
      if (!this.currentCategory || !this.currentCategory.children) return;
      
      const allSelected = this.isAllSelected();
      
      this.currentCategory.children.forEach(item => {
        item.selected = !allSelected;
        
        // 更新serviceIds数组
        if (!allSelected) {
          if (!this.serviceIds.includes(item.id)) {
            this.serviceIds.push(item.id);
          }
        } else {
          this.serviceIds = this.serviceIds.filter(id => id !== item.id);
        }
      });
    },

    // 保存设置
    async saveSettings() {
      try {
        const params = {
          id: this.shifuId,
          userId: this.userID,
          serviceCates: this.serviceIds
        };
        
        const res = await this.$api.shifu.updataSkill(JSON.stringify(params));
        
        if (res.code === '-1') {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          });
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none',
            duration: 2000,
            success: () => {
              setTimeout(() => {
                uni.navigateBack({ delta: 1 });
              }, 2000);
            }
          });
        }
      } catch (e) {
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
        console.error('保存失败:', e);
      }
    },

    // 获取分类列表
    async getList() {
      this.loading = true;
      this.error = null;
      try {
        const response = await $api.shifu.getNewSkill();
        console.log("API Response:", response);

        let categoriesData = [];
        if (Array.isArray(response)) {
          categoriesData = response;
        } else if (response.data && Array.isArray(response.data)) {
          categoriesData = response.data;
        } else {
          throw new Error("无效或空的数据");
        }
        
        // 初始化数据，包括selected状态和serviceIds
        this.serviceIds = [];
        categoriesData.forEach(category => {
          if (category.children) {
            category.children.forEach(subCategory => {
              // 如果API返回的subCategory.selected为true，则添加到serviceIds
              if (subCategory.selected) {
                this.serviceIds.push(subCategory.id);
              }
            });
          }
        });
        
        this.categories = categoriesData;
        
        if (this.categories.length > 0) {
          this.selectedCategoryId = this.categories[0].id;
        }
      } catch (err) {
        this.error = "数据加载失败: " + err.message;
        uni.showToast({
          title: this.error,
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
getmyGrade(){
	this.$api.shifu.getGrade().then(res=>{
		console.log(res)
	})
},
    // 获取师傅信息
    async getInfoS() {
      try {
        const res = await $api.shifu.getSInfo();
        console.log("getSInfo Response:", res);
        
        this.shifuId = res.data.id;
        this.userID = res.data.userId;
        
        // 获取分类列表
        await this.getList();
      } catch (err) {
        console.error("Error in getInfoS:", err);
        uni.showToast({
          title: "加载服务信息失败",
          icon: "none"
        });
      }
    },
    
    // 滚动事件
    scroll(e) {
      this.scrollTop = e.detail.scrollTop;
    }
  },
  async onLoad() {
	  		this.getmyGrade();
    await this.getInfoS();
  }
};
</script>

<style scoped>
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left {
  width: 200rpx;
  background: #f8f9fa;
  border-right: 1rpx solid #dee2e6;
}

.scrollL {
  height: 100%;
}

.left_item {
  padding: 30rpx 20rpx;
  font-size: 28rpx;
  text-align: center;
  border-left: 4rpx solid transparent;
}

.left_item.active {
  color: #2e80fe;
  background: #e6f0ff;
  border-left-color: #2e80fe;
}

.right {
  flex: 1;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.scrollR {
  flex: 1;
  height: 100%;
}

.category_header {
  padding: 20rpx;
  background: #2e80fe;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category_title {
  font-size: 32rpx;
  font-weight: bold;
}

.category_stats {
  font-size: 24rpx;
  opacity: 0.9;
}

.select_all_btn {
  padding: 10rpx 20rpx;
  background: rgba(255,255,255,0.2);
  border-radius: 30rpx;
}

/* 改为网格布局 */
.subcategory_grid {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx;
}

.subcategory_item {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  padding: 25rpx 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
}

.subcategory_item.active {
  background: #e6f0ff;
  color: #2e80fe;
  border: 1rpx solid #2e80fe;
}

.subcategory_name {
  font-size: 28rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
}

.selected_icon {
  color: #2e80fe;
  font-weight: bold;
}

.footer {
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
}

.save_btn {
  background: #2e80fe;
  color: white;
  border-radius: 50rpx;
}

.loading, .error, .no-content {
  padding: 30rpx;
  text-align: center;
  color: #999;
}
</style>