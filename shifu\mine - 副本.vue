
<template>
	<view class="pages-mine">
		<view class="header">
			<view class="header-content">
				<view class="avatar_view">
					<image mode="aspectFill" class="avatar" :src="userInfo.avatarUrl"></image>
				</view>
				<view class="user-info">
					<view v-if="!isLoggedIn">
						<button @click="showLoginPopup" :disabled="isLoading" :class="{ 'loading': isLoading }">
							{{ isLoading ? '登录中...' : '用户登录' }}
						</button>
					</view>
					<view v-else class="user-info-logged">
						<view class="nickname">
							{{ userInfo.nickName }}
						</view>
						<view class="phone-number" v-if="userInfo.phone">
							{{ userInfo.phone }}
						</view>
						<view class="status-badge" :class="statusBadgeClass" v-if="shifustatus !== undefined">
							{{ statusText }}
						</view>
					</view>
				</view>
				<view @click="navigateTo('../user/userProfile')" class="settings">
					<i class="iconfont icon-xitong text-bold"></i>
				</view>
			</view>
		</view>

		<view class="mine-menu-list box-shadow fill-base box1">
			<view class="menu-title flex-between pl-lg pr-md b-1px-b">
				<view class="f-paragraph c-title text-bold">我的订单</view>
			</view>
			<view @click="dingyue()" class="flex-warp pt-lg pb-lg">
				<view class="order-item" v-for="(item, index) in orderList" :key="index" @tap="navigateTo(item.url)">
					<view class="icon-container">
						<u-icon :name="item.icon" color="#448cfb" size="28"></u-icon>
						<view v-if="item.count > 0" class="number-circle">{{ item.count }}</view>
					</view>
					<view class="mt-sm">{{ item.text }}</view>
				</view>
			</view>
		</view>

		<view @click="dingyue()" class="mine-menu-list box-shadow fill-base">
			<view class="menu-title flex-between pl-lg pr-md b-1px-b">
				<view class="f-paragraph c-title text-bold">常用功能</view>
			</view>
			<view class="flex-warp pt-lg pb-lg">
				<view class="order-item" v-for="(item, index) in orderList3" :key="index"
					@tap="handleNavigate(item.url)">
					<u-icon :name="item.icon" color="#448cfb" size="28"></u-icon>
					<view class="mt-sm">{{ item.text }}</view>
				</view>
			</view>
		</view>

		<view class="mine-menu-list box-shadow fill-base">
			<view class="menu-title flex-between pl-lg pr-md b-1px-b">
				<view class="f-paragraph c-title text-bold">其他功能</view>
			</view>
			<view class="flex-warp pt-lg pb-lg">
				<view class="order-item" @tap="handleNavigate('/shifu/skills')">
					<u-icon name="plus-square-fill" color="#448cfb" size="28"></u-icon>
					<view class="mt-sm">技能标签</view>
				</view>
				<view class="order-item" @tap="handleNavigate('/shifu/Professiona')">
					<u-icon name="order" color="#448cfb" size="28"></u-icon>
					<view class="mt-sm">技能证书</view>
				</view>
				<view class="order-item" @tap="handleNavigate('/user/promotion')">
					<u-icon name="red-packet-fill" color="#E41F19" size="28"></u-icon>
					<view style="color: #E41F19;" class="mt-sm">邀请有礼</view>
				</view>
			</view>
		</view>

		<view class="spacer"></view>

		<view class="mine-tool-grid fill-base">
			<view class="grid-container">
				<view class="grid-item" v-for="(item, index) in toolList2" :key="index" @tap="handleNavigate(item.url)">
					<view class="grid-icon-container">
						<u-icon :name="item.icon" :color="item.iconColor" size="28"></u-icon>
					</view>
					<view class="grid-text">{{ item.text }}</view>
				</view>

				<view class="grid-item" @tap="navigateTo('../pages/service')">
					<view class="grid-icon-container switch-identity">
						<u-icon name="man-add" color="#E41F19" size="28"></u-icon>
					</view>
					<view style="color: #E41F19;" class="grid-text">切换用户版</view>
				</view>

				<view class="grid-item">
					<button class="contact-btn-wrapper" open-type="contact" bindcontact="handleContact"
						session-from="sessionFrom">
						<view class="grid-icon-container switch-identity">
							<u-icon name="server-man" color="#448cfb" size="28"></u-icon>
						</view>
						<view class="grid-text">客服</view>
					</button>
				</view>
			</view>
		</view>

		<view class="floating-contact">
			<view class="contact-container">
				<u-icon name="server-man" color="#576b95" size="24"></u-icon>
				<button class="contact-btn" open-type="contact" bindcontact="handleContact" session-from="sessionFrom">
					客服
				</button>
			</view>
		</view>

		<view v-if="loginPopupVisible" class="login-popup-overlay" @tap="hideLoginPopup">
			<view class="login-popup" @tap.stop>
				<view class="close-btn" @tap="hideLoginPopup">
					<i class="iconfont icon-close"></i>
				</view>

				<view class="popup-content">
					<view class="welcome-title">欢迎登录今师傅</view>
					<view class="welcome-subtitle">登录后即可享受完整服务</view>

					<view class="agreement-section">
						<view class="checkbox-container" @tap="toggleAgreement">
							<view class="checkbox" :class="{ 'checked': agreedToTerms }">
								<i v-if="agreedToTerms" class="iconfont icon-check">✓</i>
							</view>
							<view class="agreement-text">
								我已阅读并同意 <text class="link" @tap.stop="navigateToAgreement('service')">《今师傅服务协议》</text>
								<text class="link" @tap.stop="navigateToAgreement('privacy')">《隐私政策》</text>
							</view>
						</view>
					</view>

					<button class="phone-login-btn" :class="{ 'disabled': !agreedToTerms || isLoading }"
						:disabled="!agreedToTerms || isLoading" open-type="getPhoneNumber"
						@getphonenumber="onGetPhoneNumber">
						{{ isLoading ? '登录中...' : '手机号快捷登录' }}
					</button>
				</view>
			</view>
		</view>

		<tabbar cur="1"></tabbar>
	</view>
</template>

<script>
	import tabbar from "@/components/tabbarsf.vue";
	import {
		mapState,
		mapMutations
	} from "vuex";

	// Utility function for debouncing
	const debounce = (func, wait) => {
		let timeout;
		return function(...args) {
			const context = this;
			clearTimeout(timeout);
			timeout = setTimeout(() => func.apply(context, args), wait);
		};
	};

	export default {
		components: {
			tabbar
		},
		data() {
			return {
				isLoading: false, // Changed to false for initial render
				inviteCode: '',
				tmplIds: [
					'qt5Q70oqYNTB2RaTzl0peEHpjK-iinWr3ZfxGvs4wtg',
					'_2z7Bbw8oq4av-yqP31fZLaI82Z_N52wNM_1ihXQv6I',
					'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
				],
				code: '', // Store wx.login code
				loginPopupVisible: false, // Control login popup visibility
				agreedToTerms: false, // Control agreement checkbox state
				shifustatus: '', // Added shifustatus
				orderList: [{
						icon: 'order',
						text: '全部',
						url: '/shifu/master_my_order?tab=0',
						count: 0
					},
					{
						icon: 'bell',
						text: '待上门',
						url: '/shifu/master_my_order?tab=3',
						count: 0
					},
					{
						icon: 'hourglass-half-fill',
						text: '待服务',
						url: '/shifu/master_my_order?tab=5',
						count: 0
					},
					{
						icon: 'clock',
						text: '服务中',
						url: '/shifu/master_my_order?tab=6',
						count: 0
					},
					{
						icon: 'thumb-up',
						text: '已完成',
						url: '/shifu/master_my_order?tab=7',
						count: 0
					},
					{
						icon: 'chat-fill',
						text: '售后',
						url: '/shifu/master_my_order?tab=8',
						count: 0
					},
				],
				orderList3: [{
						icon: 'red-packet',
						text: '服务收入',
						url: '/shifu/income'
					},
					{
						icon: 'file-text-fill',
						text: '报价列表',
						url: '/shifu/master_bao_list'
					},
					{
						icon: 'rmb-circle',
						text: '保证金',
						url: '/shifu/Margin'
					}
				],
				toolList2: [{
						icon: 'plus-people-fill',
						text: '师傅入驻',
						url: '/shifu/Settle',
						iconColor: '#448cfb'
					},
					{
						icon: 'edit-pen',
						text: '编辑师傅资料',
						url: '/shifu/master_Info',
						iconColor: '#448cfb'
					},
				]
			};
		},
		computed: {
			...mapState({
				storeUserInfo: state => state.user.userInfo || {},
				token: state => state.user.autograph || '',
				erweima: state => state.user.erweima || '',
				regeocode: (state) => state.service.regeocode,
			}),
			isLoggedIn() {
				return !!this.token && !!this.storeUserInfo.phone;
			},
			userInfo() {
				// Prioritize storeUserInfo, then local storage, then defaults
				return {
					phone: this.isLoggedIn ? this.storeUserInfo.phone : '',
					avatarUrl: this.isLoggedIn ? (this.storeUserInfo.avatarUrl || '/static/mine/default_user.png') :
						'/static/mine/default_user.png',
					nickName: this.isLoggedIn ? (this.storeUserInfo.nickName || '微信用户') : '微信用户',
					userId: this.isLoggedIn ? this.storeUserInfo.userId : '',
					pid: this.isLoggedIn ? this.storeUserInfo.pid : ''
				};
			},
			statusText() {
				switch (this.shifustatus) {
					case -1:
						return '未入驻师傅';
					case 1:
						return '审核中';
					case 2:
						return '已认证';
					case 4:
						return '审核驳回';
					default:
						return '';
				}
			},
			statusBadgeClass() {
				return {
					'status-not-registered': this.shifustatus === -1,
					'status-pending': this.shifustatus === 1,
					'status-approved': this.shifustatus === 2,
					'status-rejected': this.shifustatus === 4
				};
			}
		},
		onLoad(options) {
		
			// Get current location
			this.getNowPosition();
			// Handle invite code from options
			if (options.inviteCode) {
				console.log('Received inviteCode:', options.inviteCode);
				this.inviteCode = options.inviteCode;
				uni.setStorageSync('receivedInviteCode', options.inviteCode);
			}
			// Handle erweima from Vuex or storage
			if (this.erweima) {
				console.log('erweima from Vuex:', this.erweima);
				this.inviteCode = this.erweima;
				uni.setStorageSync('receivedInviteCode', this.erweima);
			} else {
				const erweima = uni.getStorageSync('erweima');
				if (erweima) {
					console.log('erweima from storage:', erweima);
					this.$store.commit('setErweima', erweima);
					this.inviteCode = erweima;
					uni.setStorageSync('receivedInviteCode', erweima);
				}
			}
			// Perform WeChat login
			uni.login({
				provider: 'weixin',
				success: res => {
					if (res.code) {
						this.code = res.code;
						console.log('Initial wx.login code:', this.code);
					}
				},
				fail: err => {
					console.error('wx.login failed:', err);
				}
			});
			// Fetch activity config (not used in this version, kept for reference if needed)
			// this.gethuodongconfig();
			// Initialize user data
			this.initUserData();
			// Fetch highlight if logged in
			if (this.isLoggedIn) {
				this.debounceGetHighlight();
			}

			// Call fetchShifuInfo and getHighlight
			this.fetchShifuInfo();
			this.getHighlight();
		},
		onShow() {
			// Fetch user info and highlight if logged in and token is present
			if (this.isLoggedIn && this.token) {
				this.fetchUserInfo();
				this.debounceGetHighlight();
			} else {
				// Ensure UI reflects logged-out state
				this.handleInvalidSession();
			}
			this.fetchShifuInfo();
			this.getHighlight();
		},
		onPullDownRefresh() {
			// Handle pull-down refresh
			if (this.isLoggedIn && this.token) {
				Promise.all([
					this.fetchUserInfo(),
					this.getHighlight(),
					this.fetchShifuInfo() // Added fetchShifuInfo to pull-down refresh
				]).then(() => {
					uni.stopPullDownRefresh();
					// this.showToast('刷新成功', 'success');
				}).catch(err => {
					console.error('Pull-down refresh failed:', err);
					uni.stopPullDownRefresh();
					// this.showToast('刷新失败，请稍后重试');
				});
			} else {
				// If not logged in, reset UI and stop refresh
				this.handleInvalidSession();
				uni.stopPullDownRefresh();
				this.showToast('请先登录');
			}
		},
		methods: {
			getmylogin() {
				uni.login({
					provider: 'weixin',
					success: res => {
						if (res.code) {
							this.code = res.code;
							console.log('Initial wx.login code:', this.code);
						}
					}
				});
			},
			// Removed gethuodongconfig as it's not present in the target template
			// gethuodongconfig() {
			// 	this.$api.service.huodongselectActivityConfig().then(res => {
			// 		if (res.code === "200") {
			// 			// Add activity item if not already present
			// 			if (!this.toolList.some(item => item.text === this.activityItem.text)) {
			// 				this.toolList = [...this.toolList, this.activityItem];
			// 			}
			// 		} else {
			// 			// Remove activity item if present
			// 			this.toolList = this.toolList.filter(item => item.text !== this.activityItem.text);
			// 		}
			// 		console.log('huodongselectActivityConfig response:', res);
			// 	}).catch(err => {
			// 		console.error('huodongselectActivityConfig failed:', err);
			// 		// Ensure activity item is removed on error
			// 		this.toolList = this.toolList.filter(item => item.text !== this.activityItem.text);
			// 	});
			// },
			getNowPosition() {
				return new Promise((resolve) => {
					uni.getLocation({
						type: "gcj02",
						isHighAccuracy: true,
						accuracy: "best",
						success: (res) => {
							uni.setStorageSync("lat", res.latitude);
							uni.setStorageSync("lng", res.longitude);
							uni.request({
								url: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,
								success: (res1) => {
									console.log(res1)
									const province = res1.data.regeocode.addressComponent.province;
									this.position = typeof res1.data.regeocode.addressComponent.city === "string" ?
										res1.data.regeocode.addressComponent.city : province;
									this.$store.dispatch('setRegeocode', {
									                                regeocode: res1.data.regeocode,
									                                lat: res.latitude,
									                                lng: res.longitude
									                            });
									
									uni.setStorageSync("city", {
										city_id: res1.data.regeocode.addressComponent.adcode,
										position: this.position
									});
									resolve();
								},
								fail: (err) => {
									console.error("逆地理编码失败:", err);
									resolve();
								}
							});
						},
						fail: (err) => {
							console.error("获取定位失败:", err);
							resolve();
						}
					});
				});
			},
			getshifuinfo() {

				this.$api.shifu.getshifstutas({
					userId:uni.getStorageSync('userId')
				}).then(res => {
					console.log(res)
					console.log(this.regeocode)
					if (res.data === -1) {
						// 师傅入驻
							let userinster={
							userId:uni.getStorageSync('userId'),
							mobile:uni.getStorageSync('phone'),
							address:this.regeocode.regeocode.formatted_address,
							cityId:'1046,1127,1135',
							labelId:25,
							lng:this.regeocode.lng,
							lat:this.regeocode.lat,
						}
						console.log(userinster)
						this.$api.shifu.masterEnter(userinster).then(res=>{
							if(res.code==="200"){
								console.log('ok')
							}
						})
					}
				});
			},
			// Debounced getHighlight to prevent multiple rapid calls
			debounceGetHighlight: debounce(function() {
				this.getHighlight();
			}, 300),
			getHighlight() {
				const userId = uni.getStorageSync('userId');
				if (!userId) {
					console.log('No userId, skipping getHighlight');
					return Promise.resolve();
				}
				this.isLoading = true;
				return this.$api.service.getHighlight({
					userId: userId,
					role: 1
				}).then(res => {
					console.log('getHighlight response:', res);
					// Create a new array to ensure reactivity
					const updatedOrderList = this.orderList.map((item, index) => ({
						...item,
						count: index === 0 ? (res && res.countOrder ? res.countOrder : 0) :
							index === 1 ? (res && res.shiFuBaoJia ? res.shiFuBaoJia : 0) :
							index === 2 ? (res && res.daiZhiFu ? res.daiZhiFu : 0) :
							index === 3 ? (res && res.daiFuWu ? res.daiFuWu : 0) :
							index === 4 ? (res && res.fuWuZhong ? res.fuWuZhong : 0) :
							index === 5 ? (res && res.yiWanCheng ? res.yiWanCheng : 0) : 0
					}));
					// Update orderList reactively
					this.$set(this, 'orderList', updatedOrderList);
				}).finally(() => {
					this.isLoading = false;
				});
			},
			handleContact(e) {
				console.log(e.detail.path);
				console.log(e.detail.query);
			},
			...mapMutations(['updateUserItem']),
			showLoginPopup() {
				this.loginPopupVisible = true;
			},
			// dingyue() {
			// 	const panduan = uni.getStorageSync('userId');
			// 	console.log(panduan);
			// 	if (panduan) {
			// 		console.log('dingyue called');
			// 		const allTmplIds = this.tmplIds;
			// 		if (allTmplIds.length < 3) {
			// 			console.error("Not enough template IDs available:", allTmplIds);
			// 			return;
			// 		}
			// 		const shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());
			// 		const selectedTmplIds = shuffled.slice(0, 3);
			// 		console.log("Selected template IDs:", selectedTmplIds);
			// 		const templateData = selectedTmplIds.map((id, index) => ({
			// 			templateId: id,
			// 			templateCategoryId: index === 0 ? 10 : 5
			// 		}));
			// 		uni.requestSubscribeMessage({
			// 			tmplIds: selectedTmplIds,
			// 			success: (res) => {
			// 				console.log('requestSubscribeMessage success:', res, 'with tmplIds:', this.tmplIds);
			// 				// Check if any of the template IDs were rejected
			// 				const hasRejection = this.tmplIds.some(tmplId => res[tmplId] === 'reject');
			// 				const hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');
			// 				if (hasRejection && !hasShownModal) {
			// 					uni.showModal({
			// 						title: '提示',
			// 						content: '您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收师傅的报价通知。',
			// 						cancelText: '取消',
			// 						confirmText: '去开启',
			// 						confirmColor: '#007AFF',
			// 						success: (modalRes) => {
			// 							uni.setStorageSync('hasShownSubscriptionModal', true);
			// 							if (modalRes.confirm) {
			// 								uni.openSetting({
			// 									withSubscriptions: true
			// 								});
			// 							} else if (modalRes.cancel) {
			// 								uni.setStorageSync('hasCanceledSubscription', true);
			// 							}
			// 						}
			// 					});
			// 				}
			// 				this.templateCategoryIds = [];
			// 				selectedTmplIds.forEach((templId, index) => {
			// 					console.log(`Template ${templId} status: ${res[templId]}`);
			// 					if (res[templId] === 'accept') {
			// 						const templateCategoryId = templateData[index].templateCategoryId;
			// 						if (templateCategoryId === 10) {
			// 							for (let i = 0; i < 15; i++) {
			// 								this.templateCategoryIds.push(templateCategoryId);
			// 							}
			// 						} else {
			// 							this.templateCategoryIds.push(templateCategoryId);
			// 						}
			// 						console.log('Accepted message push for template:', templId);
			// 					}
			// 				});
			// 				console.log('Updated templateCategoryIds:', this.templateCategoryIds);
			// 			},
			// 			fail: (err) => {
			// 				console.error('requestSubscribeMessage failed:', err);
			// 			}
			// 		});
			// 	}
			// },
			hideLoginPopup() {
				this.loginPopupVisible = false;
				this.agreedToTerms = false;
			},
			toggleAgreement() {
				this.agreedToTerms = !this.agreedToTerms;
			},

			navigateToAgreement(type) {
				let url = '../user/configuser';
				if (type === 'service') {
					url += '?type=service';
				} else if (type === 'privacy') {
					url += '?type=privacy';
				}
				uni.navigateTo({
					url: url
				});
			},
			initUserData() {
				if (this.token && !this.storeUserInfo.phone) {
					const userInfo = {
						phone: uni.getStorageSync('phone') || '',
						avatarUrl: uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
						nickName: uni.getStorageSync('nickName') || '微信用户',
						userId: uni.getStorageSync('userId') || '',
						pid: uni.getStorageSync('pid') || ''
					};
					if (userInfo.phone) {
						this.updateUserItem({
							key: 'userInfo',
							val: userInfo
						});
					} else {
						this.handleInvalidSession();
					}
				}
			},
			navigateTo(url) {
				if (!url) return;
				const requiresLogin = [
					'../user/coupon',
					'../user/repair_record',
					'../user/order_list',
					'../user/address',
					'../user/Settle',
					'../user/agent_apply',
					'../user/promotion',
					'../user/bankCard',
					'../shifu/Settle',
					'../shifu/Receiving',
					'../shifu/mine'
				];
				if (requiresLogin.some(path => url.startsWith(path)) && !this.isLoggedIn) {
					return this.showToast('请先登录');
				}
				uni.navigateTo({
					url
				});
			},
			fetchUserInfo() {
				if (this.isLoading || !this.token) {
					console.log('Skipping fetchUserInfo: no token or already loading');
					return Promise.resolve();
				}
				this.isLoading = true;
				return this.$api.user.userInfo()
					.then(response => {
						if (!response || typeof response !== 'object') {
							throw new Error('获取用户信息失败: 响应数据无效');
						}
						const userInfo = {
							phone: response.phone || '',
							avatarUrl: response.avatarUrl || '/static/mine/default_user.png',
							nickName: response.nickName || '微信用户',
							userId: response.id || '',
							createTime: response.createTime || '',
							pid: response.pid || '',
							inviteCode: response.inviteCode || ''
						};
						this.updateUserItem({
							key: 'userInfo',
							val: userInfo
						});
						this.saveUserInfoToStorage(userInfo);
					})
					.catch(error => {
						console.error('获取用户信息失败:', error);
						if (error.message && error.message.includes('响应数据无效')) {
							this.handleInvalidSession();
						} else {
							if (this.token) {
								this.showToast('获取用户信息失败，请稍后重试');
							}
						}
					})
					.finally(() => {
						this.isLoading = false;
					});
			},
			saveUserInfoToStorage(userInfo) {
				uni.setStorageSync('phone', userInfo.phone);
				uni.setStorageSync('avatarUrl', userInfo.avatarUrl);
				uni.setStorageSync('nickName', userInfo.nickName);
				uni.setStorageSync('userId', userInfo.userId);
				uni.setStorageSync('pid', userInfo.pid);
			},
			handleInvalidSession() {
				['token', 'phone', 'avatarUrl', 'nickName', 'userId', 'pid'].forEach(key => {
					uni.removeStorageSync(key);
				});
				this.updateUserItem({
					key: 'userInfo',
					val: {}
				});
				this.updateUserItem({
					key: 'autograph',
					val: ''
				});
				this.isLoading = false;
				// Reset orderList counts to 0 when session is invalid
				this.$set(this, 'orderList', this.orderList.map(item => ({
					...item,
					count: 0
				})));
			},
			onGetPhoneNumber(e) {
				// #ifdef APP-PLUS
				uni.navigateTo({
					url: '/pages/login'
				});
				return;
				// #endif
				if (e.detail.errMsg !== 'getPhoneNumber:ok') {
					return this.showToast('授权失败，请重试');
				}
				this.getmylogin();
				this.isLoading = true;
				uni.showLoading({
					mask: true,
					title: '登录中...'
				});
				const {
					encryptedData,
					iv
				} = e.detail;
				uni.checkSession({
					success: () => {
						this.loginWithWeixin({
							code: this.code,
							encryptedData,
							iv,
							pid: this.inviteCode
						});
					},
					fail: () => {
						uni.login({
							provider: 'weixin',
							success: res => {
								if (res.code) {
									this.code = res.code;
									console.log('Refreshed wx.login code:', this.code);
									this.loginWithWeixin({
										code: this.code,
										encryptedData,
										iv,
										pid: this.inviteCode
									});
								} else {
									this.isLoading = false;
									uni.hideLoading();
									this.showToast('获取登录凭证失败');
								}
							},
							fail: () => {
								this.isLoading = false;
								uni.hideLoading();
								this.showToast('微信登录失败，请重试');
							}
						});
					}
				});
			},
			loginWithWeixin(params) {
				this.$api.user.loginuserInfo({
						code: params.code,
						encryptedData: params.encryptedData,
						iv: params.iv,
						pid: this.inviteCode
					})
					.then(response => {
						if (!response || !response.token) {
							throw new Error('请重新登录');
						}
						uni.setStorageSync('token', response.token);
						this.updateUserItem({
							key: 'autograph',
							val: response.token
						});
						return this.$api.user.userInfo();
					})
					.then(userInfoRes => {
						if (!userInfoRes || typeof userInfoRes !== 'object') {
							throw new Error('获取用户信息失败');
						}
						const userInfo = {
							phone: userInfoRes.phone || '',
							avatarUrl: userInfoRes.avatarUrl || '/static/mine/default_user.png',
							nickName: userInfoRes.nickName || '微信用户',
							userId: userInfoRes.id || '',
							createTime: userInfoRes.createTime || '',
							pid: userInfoRes.pid || ''
						};
						this.updateUserItem({
							key: 'userInfo',
							val: userInfo
						});
						this.saveUserInfoToStorage(userInfo);
						this.showToast('登录成功', 'success');
							this.getshifuinfo()
						this.hideLoginPopup();
						this.debounceGetHighlight();
						// this.getshifuinfo();
					})
					.catch(error => {
						console.error('Login error:', error);
						this.showToast(error.message || '登录失败，请稍后重试');
						this.handleInvalidSession();
					})
					.finally(() => {
						this.isLoading = false;
						uni.hideLoading();
					});
			},
			showToast(title, icon = 'none') {
				uni.showToast({
					title,
					icon,
					duration: 2000
				});
			},
			async fetchShifuInfo() {
				try {
					this.isLoading = true;
					const shiInfoResponse = await this.$api.shifu.getMaster();
					console.log(shiInfoResponse);
					if (!shiInfoResponse || typeof shiInfoResponse !== 'object') {
						throw new Error('获取师傅状态失败: 响应数据无效');
					}

					// Directly use the response for status, no need for shiInfoResponse state
					this.shifustatus = Number(shiInfoResponse.status) !== undefined ? Number(shiInfoResponse.status) : -1;

					const userInfo = {
						mobile: shiInfoResponse.mobile || '',
						avatarUrl: shiInfoResponse.avatarUrl || this.storeUserInfo.avatarUrl || uni.getStorageSync(
							'avatarUrl') || '/static/mine/default_user.png',
						coachName: shiInfoResponse.coachName || this.storeUserInfo.nickName || uni.getStorageSync(
							'nickName') || '微信用户',
						id: shiInfoResponse.id || this.storeUserInfo.userId || uni.getStorageSync('userId') || '',
						pid: this.storeUserInfo.pid || uni.getStorageSync('pid') || '',
						status: Number(shiInfoResponse.status) || -1,
						messagePush: Number(shiInfoResponse.messagePush) || -1
					};

					uni.setStorageSync('shiInfo', JSON.stringify(userInfo));
					// this.updateUserItem({
					// 	key: 'shiInfo',
					// 	val: userInfo
					// });

					const modalShownKey = `certificationModalShown_${userInfo.id}_${userInfo.status}`;
					const hasShownModal = uni.getStorageSync(modalShownKey);

					if (!hasShownModal && (userInfo.status === -1 || userInfo.status === 4)) {
						this.showCertificationPopup();
						uni.setStorageSync(modalShownKey, 'true');
					}
				} catch (error) {
					console.error('fetchShifuInfo error:', error);
					// Set default status to -1 on error
					this.shifustatus = -1;
					const defaultUserInfo = {
						mobile: '',
						avatarUrl: this.storeUserInfo.avatarUrl || uni.getStorageSync('avatarUrl') ||
							'/static/mine/default_user.png',
						coachName: this.storeUserInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
						id: this.storeUserInfo.userId || uni.getStorageSync('userId') || '',
						pid: this.storeUserInfo.pid || uni.getStorageSync('pid') || '',
						status: -1
					};

					uni.setStorageSync('shiInfo', JSON.stringify(defaultUserInfo));
					// this.updateUserItem({
					// 	key: 'shiInfo',
					// 	val: defaultUserInfo
					// });

					const modalShownKey = `certificationModalShown_${defaultUserInfo.id}_${defaultUserInfo.status}`;
					const hasShownModal = uni.getStorageSync(modalShownKey);

					if (!hasShownModal && defaultUserInfo.status === -1) {
						this.showCertificationPopup();
						uni.setStorageSync(modalShownKey, 'true');
					}
				} finally {
					this.isLoading = false;
				}
			},
			getshifustatus() {
				// This method now primarily just updates the local shifustatus from userInfo,
				// as fetchShifuInfo is responsible for getting the actual status.
				const shiInfo = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};
				this.shifustatus = shiInfo.status;
				console.log('getshifustatus:', this.shifustatus);
			},
			showCertificationPopup() {
				console.log('showCertificationPopup called, current shifustatus:', this.shifustatus);
				if (this.shifustatus === -1 || this.shifustatus === 4) {
					uni.showModal({
						title: '提示',
						content: this.shifustatus === -1 ? '您尚未成为师傅，是否前往认证？' : '您的师傅认证被驳回，是否重新认证？',
						confirmText: '去认证',
						cancelText: '再想想',
						cancelable: true,
						success: (res) => {
							if (res.confirm) {
								const targetUrl = '/shifu/Settle'; // Both -1 and 4 go to Settle
								uni.navigateTo({
									url: targetUrl,
									fail(err) {
										console.error('Navigation to certification failed:', err);
										uni.showToast({
											title: '跳转认证页面失败',
											icon: 'none'
										});
									}
								});
							}
						},
						fail: (err) => {
							console.error('Modal failed:', err);
						}
					});
				}
			},
			handleNavigate(url) {
				// Special handling for these URLs, allowing navigation regardless of shifustatus
				if (['/shifu/Settle', '/user/promotion', '/shifu/master_Info'].includes(url)) {
					this.navigateTo(url);
					return;
				}

				if (this.shifustatus === -1 || this.shifustatus === 4) {
					uni.showToast({
						title: '你还不是师傅',
						icon: 'none'
					});
					this.showCertificationPopup(); // Prompt for certification
				} else if (this.shifustatus === 1) {
					uni.showToast({
						title: '师傅状态在审核中',
						icon: 'none'
					});
				} else if (this.shifustatus === 2) {
					this.navigateTo(url);
				}
			},
			handleCallKf() {
				if (this.shifustatus === -1 || this.shifustatus === 4) {
					uni.showToast({
						title: '你还不是师傅',
						icon: 'none'
					});
					this.showCertificationPopup(); // Prompt for certification
				} else if (this.shifustatus === 1) {
					uni.showToast({
						title: '师傅状态在审核中',
						icon: 'none'
					});
				} else if (this.shifustatus === 2) {
					this.callkf();
				}
			},
			callkf() {
				uni.showToast({
					title: '联系客服功能待实现',
					icon: 'none'
				});
			}
		}
	};
</script>

<style lang="scss">
	/* Login Popup Styles */
	.login-popup-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 2000;
		display: flex;
		align-items: flex-end;
		justify-content: center;
	}

	.login-popup {
		background-color: #fff;
		width: 100%;
		border-radius: 40rpx 40rpx 0 0;
		position: relative;
		max-height: 60vh;
		padding-bottom: 10rpx;
		animation: slideUp 0.3s ease-out;
	}

	@keyframes slideUp {
		from {
			transform: translateY(100%);
		}

		to {
			transform: translateY(0);
		}
	}

	.close-btn {
		position: absolute;
		top: 30rpx;
		right: 30rpx;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #999;
		font-size: 40rpx;
		z-index: 10;
	}

	.popup-content {
		padding: 80rpx 60rpx 40rpx;
		text-align: center;
	}

	.welcome-title {
		font-size: 48rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}

	.welcome-subtitle {
		font-size: 32rpx;
		color: #666;
		margin-bottom: 80rpx;
	}

	.agreement-section {
		margin-bottom: 60rpx;
		display: flex;
		justify-content: center;
	}

	.checkbox-container {
		display: flex;
		align-items: flex-start;
		text-align: left;
		max-width: 560rpx;
	}

	.checkbox {
		width: 36rpx;
		height: 36rpx;
		border: 2rpx solid #ddd;
		border-radius: 6rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
		margin-top: 4rpx;
		flex-shrink: 0;
		background-color: #fff;
		transition: all 0.2s;

		&.checked {
			background-color: #00C853;
			border-color: #00C853;
			color: #fff;
		}

		.iconfont {
			font-size: 24rpx;
		}
	}

	.agreement-text {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;

		.link {
			color: #00C853;
		}
	}

	.phone-login-btn {
		width: 100%;
		height: 100rpx;
		background: linear-gradient(135deg, #00C853, #4CAF50);
		border: none;
		border-radius: 50rpx;
		color: #fff;
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 20rpx rgba(0, 200, 83, 0.3);
		transition: all 0.2s;

		&:active:not(.disabled) {
			transform: translateY(2rpx);
			box-shadow: 0 4rpx 12rpx rgba(0, 200, 83, 0.3);
		}

		&.disabled {
			background: #ccc;
			box-shadow: none;
			opacity: 0.6;
		}

		&::after {
			border: none;
		}
	}

	.alternative-login {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;

		.divider-line {
			flex: 1;
			height: 1rpx;
			background-color: #eee;
		}

		.divider-text {
			font-size: 26rpx;
			color: #999;
			margin: 0 30rpx;
		}
	}

	.sms-login-btn {
		width: 100%;
		height: 88rpx;
		background-color: #fff;
		border: 2rpx solid #ddd;
		border-radius: 44rpx;
		color: #666;
		font-size: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.2s;

		&:active {
			background-color: #f8f8f8;
			border-color: #00C853;
		}

		&::after {
			border: none;
		}

		.iconfont {
			margin-right: 16rpx;
			font-size: 36rpx;
		}
	}

	/* Floating Contact Button Styles */
	.floating-contact {
		position: fixed;
		bottom: 150rpx;
		right: 30rpx;
		z-index: 1000;
		background-color: #fff;
		border-radius: 50rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
		padding: 10rpx 20rpx;
		display: flex;
		align-items: center;
	}

	.contact-container {
		display: flex;
		align-items: center;
	}

	.contact-btn {
		background: none;
		border: none;
		color: #576b95;
		font-size: 30rpx;
		line-height: 1.5;
		padding: 10rpx 20rpx;
		display: flex;
		align-items: center;
	}

	.contact-btn:active {
		background-color: #ededee;
	}

	/* Existing Styles */
	.pages-mine {
		background-color: #f8f8f8;
		min-height: 100vh;
		padding-bottom: 120rpx;

		.header {
			height: 292rpx;
			background-color: #599EFF;
			position: relative;

			.header-content {
				display: flex;
				align-items: center;
				padding: 40rpx 30rpx 0;

				.avatar_view {
					width: 120rpx;
					height: 120rpx;
					border-radius: 50%;
					overflow: hidden;
					box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

					.avatar {
						width: 100%;
						height: 100%;
						border-radius: 50%;
					}
				}

				.user-info {
					flex: 1;
					margin-left: 20rpx;
					color: #fff;

					.user-info-logged {
						display: flex;
						flex-direction: column;
						gap: 10rpx;
					}

					.nickname {
						font-size: 36rpx;
						font-weight: bold;
					}

					.phone-number {
						font-size: 28rpx;
						opacity: 0.9;
					}

					button {
						background: none;
						border: 2rpx solid rgba(255, 255, 255, 0.5);
						border-radius: 32rpx;
						color: #fff;
						font-size: 32rpx;
						line-height: 1.5;
						padding: 10rpx 30rpx;

						&.loading {
							opacity: 0.7;
						}

						&::after {
							border: none;
						}
					}

					.status-badge {
						display: inline-block;
						padding: 8rpx 20rpx;
						font-size: 24rpx;
						line-height: 1.2;
						border-radius: 20rpx;
						color: #fff;
						text-align: center;
						margin-top: 10rpx;
						width: fit-content;
						box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
					}

					.status-not-registered {
						background-color: #b0b0b0;
					}

					.status-pending {
						background-color: #f4b400;
					}

					.status-approved {
						background-color: #f5a623;
					}

					.status-rejected {
						background-color: #f44336;
					}
				}

				.settings {
					padding: 10rpx;

					.icon-xitong {
						font-size: 40rpx;
						color: #fff;
					}
				}
			}
		}

		.box1 {
			margin-top: -20rpx;
			border-radius: 36rpx 36rpx 0 0;
			position: relative;
			z-index: 10;
		}

		.mine-menu-list {
			background-color: #fff;
			margin: 0 20rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
			margin-bottom: 20rpx; // Added margin-bottom

			.menu-title {
				height: 90rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 30rpx 0 40rpx;
				border-bottom: 1px solid #f0f0f0;

				.f-paragraph {
					font-size: 32rpx;
					color: #333;
					font-weight: bold;
				}
			}

			.flex-warp {
				display: flex;
				flex-wrap: wrap;
				padding: 30rpx 0;

				.order-item {
					display: flex;
					flex-direction: column;
					align-items: center;
					width: 25%; // Changed from 33.3% to 25% for orderList
					font-size: 25rpx;
					margin-top: 20rpx;
					color: #666;
					transition: transform 0.2s;

					&:active {
						transform: scale(0.95);
					}

					.icon-container {
						position: relative;
						display: flex;
						align-items: center;
						justify-content: center;
					}

					.number-circle {
						position: absolute;
						top: -10rpx;
						right: -5rpx;
						width: 30rpx;
						height: 30rpx;
						background-color: #ff4d4f;
						color: #fff;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 20rpx;
						font-weight: bold;
					}
				}

				.mt-sm {
					margin-top: 16rpx;
				}
			}
		}

		.spacer {
			height: 20rpx;
			background-color: transparent;
		}

		.mine-tool-grid {
			background-color: #fff;
			margin: 0 20rpx 30rpx;
			border-radius: 12rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
			padding: 30rpx;

			.grid-container {
				display: flex;
				flex-wrap: wrap;
				justify-content: flex-start;
				gap: 20rpx;
			}

			.grid-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: calc(33.33% - 20rpx);
				min-width: 140rpx;
				transition: transform 0.2s ease;

				&:active {
					transform: scale(0.95);
				}

				.grid-icon-container {
					width: 80rpx;
					height: 80rpx;
					border-radius: 20rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;

					&.switch-identity {
						/* Specific styling for switch-identity icon */
					}
				}

				.grid-text {
					font-size: 25rpx;
					color: #333;
					font-weight: 500;
					text-align: center;
					line-height: 1.2;
					margin-bottom: 8rpx;
				}

				.contact-btn-wrapper {
					background: none;
					border: none;
					padding: 0;
					margin: 0;
					display: flex;
					flex-direction: column;
					align-items: center;
					line-height: 1; // Ensure proper alignment
					&::after {
						border: none;
					}
				}
			}
		}

		.flex-between {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
	}
</style>
```