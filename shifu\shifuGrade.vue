<template>
  <view class="container">
    <view class="current-grade">
      <text class="current-label">当前等级:</text>
      <text class="current-value">{{info.labelName}}</text>
      <!-- <text class="current-id">(ID: {{info.labelId}})</text> -->
    </view>
    
    <view class="upgrade-title">可升级选项:</view>
    
    <view class="grade-list">
      <view 
        class="grade-item" 
        v-for="(item, index) in upgradeOptions" 
        :key="item.id"
        :class="{'current-grade-item': item.id === info.labelId}"
      >
        <view class="grade-header">
          <text class="grade-name">{{item.labelName}}</text>
          <!-- <text class="grade-id">ID: {{item.id}}</text> -->
        </view>
        
        <view class="grade-details">
          <view class="detail-item">
            <text class="detail-label">保证金:</text>
            <text class="detail-value">¥{{item.earnestMoney}}</text>
          </view>
         <!-- <view class="detail-item">
            <text class="detail-label">延迟时间:</text>
            <text class="detail-value">{{item.delayTime}}天</text>
          </view> -->
          <view v-if="item.id !== info.labelId" class="detail-item">
            <text class="detail-label">升级需支付:</text>
            <text class="detail-value upgrade-price">¥{{item.upgradePrice.toFixed(2)}}</text>
          </view>
          <view v-else class="detail-item">
            <text class="detail-label">当前等级</text>
            <text class="detail-value current-tag">✓</text>
          </view>
        </view>
        
        <button 
          v-if="item.id !== info.labelId" 
          class="upgrade-btn" 
          @click="handleUpgrade(item)"
          :disabled="item.upgradeDisabled"
        >
          {{item.upgradeDisabled ? '不可升级' : '立即升级'}}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      labellist: [],
      info: {},
      currentGrade: null
    }
  },
  computed: {
    // 计算可升级的选项
    upgradeOptions() {
      if (!this.currentGrade || !this.labellist.length) return [];
      
      // 按保证金从低到高排序，用于计算升级路径
      const sortedGrades = [...this.labellist].sort((a, b) => a.earnestMoney - b.earnestMoney);
      
      return this.labellist.map(item => {
        // 计算升级价格：累加从当前等级到目标等级之间的所有等级保证金
        const upgradePrice = this.calculateUpgradePrice(this.currentGrade, item, sortedGrades);
        
        return {
          ...item,
          upgradePrice: upgradePrice > 0 ? upgradePrice : 0,
          upgradeDisabled: upgradePrice <= 0 // 如果价格<=0则不可升级（降级或同级）
        };
      }).sort((a, b) => b.earnestMoney - a.earnestMoney); // 按保证金从高到低排序显示
    }
  },
  methods: {
	  getCurrentPlatform() {
	  	// #ifdef APP-PLUS
	  	return 'app-plus';
	  	// #endif
	  	// #ifdef MP-WEIXIN
	  	return 'mp-weixin';
	  	// #endif
	  	// #ifdef H5
	  	return 'h5';
	  	// #endif
	  	return 'unknown';
	  },
	  // APP微信支付处理
	  handleAppWechatPay(obj) {
	  	console.log(111)
	  	uni.requestPayment({
	  		"provider": "wxpay",
	  		    orderInfo: 'orderInfo',
	  		orderInfo: {
	  			appid: obj.appId,
	  			noncestr: obj.nonceStr,
	  			package: 'Sign=WXPay',
	  			partnerid: obj.partnerId,
	  			prepayid: obj.prepayId,
	  			timestamp: String(obj.timestamp),
	  			sign: obj.sign
	  		},
	  		success: (res) => {
	  			console.log('APP微信支付成功', res);
	  			uni.showToast({
	  				title: '支付成功',
	  				icon: 'success'
	  			})
	  			// this.dingyue()
	  			setTimeout(() => {
	  				uni.redirectTo({
	  					url: '/shifu/Margin'
	  				})
	  			}, 1000)
	  		},
	  		fail: (err) => {
	  			console.error('APP微信支付失败:', err);
	  			if (err.errMsg && err.errMsg.includes('cancel')) {
	  				uni.showToast({
	  					title: '您已取消支付',
	  					icon: 'none'
	  				});
	  			} else {
	  				uni.showToast({
	  					title: '支付失败，请稍后重试',
	  					icon: 'none'
	  				});
	  			}
	  		}
	  	});
	  },
	  
	  // 微信小程序支付处理（保持原有逻辑）
	  handleMiniProgramPay(obj) {
	  	const paymentParams = {
	  		timeStamp: String(obj.timestamp), // 一定要是 string
	  		nonceStr: obj.nonceStr,
	  		package: "prepay_id=" + obj.prepayId,
	  		signType: 'MD5',
	  		paySign: obj.sign
	  	};
	  	console.log(JSON.stringify(paymentParams));
	  	uni.requestPayment({
	  		"provider": 'wxpay',
	  		timeStamp: String(obj.timestamp),
	  		nonceStr: obj.nonceStr,
	  		package: "prepay_id=" + obj.prepayId,
	  		partnerid: obj.partnerId,
	  		signType: "MD5",
	  		paySign: obj.sign,
	  		appId: obj.appId,
	  		success: (res1) => {
	  			// 支付成功回调
	  			console.log('支付成功', res1);
	  			uni.showToast({
	  				title: '支付成功',
	  				icon: 'success'
	  			})
	  			this.dingyue()
	  			setTimeout(() => {
	  				uni.redirectTo({
	  					url: '/shifu/Margin'
	  				})
	  			}, 1000)
	  		},
	  		fail: (err) => {
	  			// 支付失败回调
	  			console.error('requestPayment fail object:', err);
	  			console.error('requestPayment fail JSON:', JSON.stringify(err));
	  			if (err.errMsg.includes('fail cancel')) {
	  				uni.showToast({
	  					title: '您已取消支付',
	  					icon: 'none'
	  				});
	  			} else {
	  				uni.showToast({
	  					title: '支付失败，请稍后重试',
	  					icon: 'none'
	  				});
	  			}
	  			console.error('支付失败', err);
	  			uni.showToast({
	  				title: '支付失败请检查网络',
	  				icon: 'error'
	  			})
	  		},
	  	})
	  },
	  
	  
	  
	  
	  
	  
	  
	  
	  
	  
	  
	  
	  
	  
	  
	  
	  
	  
    // 计算升级价格：累加从当前等级到目标等级之间的所有等级保证金
    calculateUpgradePrice(currentGrade, targetGrade, sortedGrades) {
      if (!currentGrade || !targetGrade) return 0;
      
      // 如果目标等级保证金小于等于当前等级，不能升级
      if (targetGrade.earnestMoney <= currentGrade.earnestMoney) {
        return 0;
      }
      
      // 找到当前等级和目标等级在排序数组中的位置
      const currentIndex = sortedGrades.findIndex(grade => grade.id === currentGrade.id);
      const targetIndex = sortedGrades.findIndex(grade => grade.id === targetGrade.id);
      
      if (currentIndex === -1 || targetIndex === -1 || targetIndex <= currentIndex) {
        return 0;
      }
      
      // 累加从当前等级的下一级到目标等级的所有保证金
      let totalPrice = 0;
      for (let i = currentIndex + 1; i <= targetIndex; i++) {
        totalPrice += sortedGrades[i].earnestMoney;
      }
      
      return totalPrice;
    },
    
    getmyGradeList() {
      this.$api.shifu.getGradeList().then(res => {
        console.log('等级列表:', res)
        this.labellist = res.data
        console.log('labellist:', this.labellist)
        // 如果已有当前等级信息，则查找对应的完整信息
        if (this.info.labelId && this.labellist.length) {
          this.currentGrade = this.labellist.find(item => item.id === this.info.labelId);
          console.log('当前等级完整信息:', this.currentGrade)
        }
      }).catch(err => {
        console.error('获取等级列表失败:', err)
        uni.showToast({
          title: '获取等级列表失败',
          icon: 'error'
        });
      })
    },
    getmyGrade() {
      this.$api.shifu.getGrade().then(res => {
        console.log('当前等级:', res)
        this.info = res.data
        console.log('info:', this.info)
        // 如果已有等级列表，则查找对应的完整信息
        if (this.labellist.length) {
          this.currentGrade = this.labellist.find(item => item.id === this.info.labelId);
          console.log('当前等级完整信息:', this.currentGrade)
        }
      }).catch(err => {
        console.error('获取当前等级失败:', err)
        uni.showToast({
          title: '获取当前等级失败',
          icon: 'error'
        });
      })
    },
    handleUpgrade(item) {
      console.log('升级选择:', item)
      console.log('当前等级:', this.currentGrade)
      
      // 验证升级条件
      if (item.upgradeDisabled) {
        uni.showToast({
          title: '该等级不可升级',
          icon: 'error'
        });
        return;
      }
      
      if (item.upgradePrice <= 0) {
        uni.showToast({
          title: '不能降级或升级同级',
          icon: 'error'
        });
        return;
      }
      
      // 计算升级路径说明
      const sortedGrades = [...this.labellist].sort((a, b) => a.earnestMoney - b.earnestMoney);
      const upgradePathText = this.getUpgradePathText(this.currentGrade, item, sortedGrades);
      // let  that =this
      uni.showModal({
        title: '确认升级',
        content: `确定要升级到${item.labelName}吗？\n${upgradePathText}\n总计需要支付¥${item.upgradePrice.toFixed(2)}`,
        success: (res) => {
          if (res.confirm) {
            // 这里可以取消注释来实现真正的升级API调用
            // uni.showToast({
            //   title: '升级成功',
            //   icon: 'success'
            // });
            
            // 模拟升级成功后刷新数据
            // setTimeout(() => {
            //   this.getmyGrade();
            // }, 1000);
            
            
            // 真正的升级API调用
            this.$api.shifu.upgradeGrade({ 
              labelId: item.id,
			  price:item.upgradePrice.toFixed(2)
            }).then(res => {
     //       if(res.code==='200'){
			   
			  //  console.log(res)
			  //  let obj = res.data
			  //  let packageStr = "prepay_id=" + obj.prepayId;
			  //  console.log(String(packageStr))
			  //  console.log(obj.nonceStr)
			  //  console.log(packageStr)
			  //  console.log(obj.nonceStr)
			  //  console.log(String(obj.timestamp))
			  //  console.log(obj.sign)
			  //  const paymentParams = {
			  //  	timeStamp: String(obj.timestamp), // 一定要是 string 
			  //  	nonceStr: obj.nonceStr,
			  //  	package: "prepay_id=" + obj.prepayId,
			  //  	signType: 'MD5',
			  //  	paySign: obj.sign
			  //  };
			  //  console.log(JSON.stringify(paymentParams));
			  //  uni.requestPayment({
			  //  	"provider": 'wxpay',
			  //  	timeStamp: String(obj.timestamp),
			  //  	nonceStr: obj.nonceStr,
			  //  	package: "prepay_id=" + obj.prepayId,
			  //  	partnerid: obj.partnerId,
			  //  	signType: "MD5",
			  //  	paySign: obj.sign,
			  //  	appId: obj.appId,
			  //  	success: (res1) => {
			  //  		// 支付成功回调
			  //  		console.log('支付成功', res1);
			  //  		uni.showToast({
			  //  			title:res1.msg,
			  //  			icon: 'success'
			  //  		})
			   		
			   		
			  //  		this.getmyGrade(); // 刷新当前等级
			  //  	},
			  //  	fail: (err) => {
			  //  		// 支付失败回调
			  //  		console.error('requestPayment fail object:', err);
			  //  		console.error('requestPayment fail JSON:', JSON.stringify(err));
			  //  		if (err.errMsg.includes('fail cancel')) {
			  //  			wx.showToast({
			  //  				title: '您已取消支付',
			  //  				icon: 'none'
			  //  			});
			  //  		} else {
			  //  			wx.showToast({
			  //  				title: '支付失败，请稍后重试',
			  //  				icon: 'none'
			  //  			});
			  //  		}
			  //  		console.error('支付失败', err);
			  //  		uni.showToast({
			  //  			title: '支付失败请检查网络',
			  //  			icon: 'error'
			  //  		})
			  //  	},
			  //  })
			   
			   
			   
			   
		   // }else{
			  //  uni.showToast({
			  //  	title: res.msg,
			  //  	icon: 'none'
			  //  })
		   // }
			  
	if(res.data === -1) {
	    uni.showModal({
	        title: '提示',
	        content: '确定要跳转到保证金页面吗？',
	        success: (confirmRes) => {
	            if(confirmRes.confirm) {
	                uni.navigateTo({
	                    url: '/shifu/Margin'
	                });
	            }
	        }
	    });
	} else if(res.code === '200') {
	    console.log(res)
	    let obj = res.data
	    let packageStr = "prepay_id=" + obj.prepayId;
	    console.log(String(packageStr))
	    console.log(obj.nonceStr)
	    console.log(packageStr)
	    console.log(obj.nonceStr)
	    console.log(String(obj.timestamp))
	    console.log(obj.sign)
	    const paymentParams = {
	        timeStamp: String(obj.timestamp), // 一定要是 string 
	        nonceStr: obj.nonceStr,
	        package: "prepay_id=" + obj.prepayId,
	        signType: 'MD5',
	        paySign: obj.sign
	    };
		if (platform === 'app-plus') {
				// APP环境使用微信支付
				console.log('APP环境，使用微信支付');
				this.handleAppWechatPay(paymentParams);
			} else if (platform === 'mp-weixin') {
				// 微信小程序环境保持原有逻辑
				console.log('微信小程序环境，使用小程序支付');
				this.handleMiniProgramPay(paymentParams);
			} else {
				// 其他环境（H5等）
				console.log('其他环境，使用默认支付方式');
				this.handleMiniProgramPay(paymentParams);
			}
			
		}
	    console.log(JSON.stringify(paymentParams));
	   
			  
			  
			  
			  
			  
			  
			  
			  
			  
			  
			  

            }).catch(err => {
              console.error('升级失败:', err);
              uni.showToast({
                title: '升级失败',
                icon: 'error'
              });
            });
           
          }
        }
      });
    },
    
    // 获取升级路径说明文本
    getUpgradePathText(currentGrade, targetGrade, sortedGrades) {
      const currentIndex = sortedGrades.findIndex(grade => grade.id === currentGrade.id);
      const targetIndex = sortedGrades.findIndex(grade => grade.id === targetGrade.id);
      
      if (currentIndex === -1 || targetIndex === -1 || targetIndex <= currentIndex) {
        return '';
      }
      
      const pathParts = [];
      for (let i = currentIndex + 1; i <= targetIndex; i++) {
        pathParts.push(`${sortedGrades[i].labelName}(¥${sortedGrades[i].earnestMoney})`);
      }
      
      return `升级路径：${pathParts.join(' → ')}`;
    },
    formatDate(dateString) {
      if (!dateString) return ''
      // 简单格式化日期，去掉时间部分
      return dateString.split(' ')[0]
    }
  },
  onLoad() {
    // 先获取等级列表，再获取当前等级
    this.getmyGradeList()
    this.getmyGrade()
  }
}
</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.current-grade {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.current-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.current-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #2d8cf0;
  margin-right: 10rpx;
}

.current-id {
  font-size: 24rpx;
  color: #999;
}

.upgrade-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin: 20rpx 0;
  padding-left: 10rpx;
}

.grade-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.grade-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.current-grade-item {
  border: 2rpx solid #2d8cf0;
  background-color: #f0f9ff;
}

.grade-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.grade-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.grade-id {
  font-size: 24rpx;
  color: #999;
}

.grade-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.upgrade-price {
  color: #f56c6c;
  font-weight: bold;
}

.current-tag {
  color: #67c23a;
  font-weight: bold;
}

.upgrade-btn {
  background-color: #2d8cf0;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 0; /* 减少上下 padding，降低高度 */
  font-size: 28rpx;
  line-height: 1.2; /* 调整行高，避免文字上下间距过大 */
  margin-top: 10rpx;
  width: 100%;
  height: auto; /* 确保高度自适应 */
  min-height: 60rpx; /* 设置最小高度（可选） */
}

.upgrade-btn[disabled] {
  background-color: #c8c9cc;
  color: #909399;
}

/* 根据不同的等级添加不同的颜色标识 */
.grade-item:nth-child(1) .grade-name {
  color: #ff6b81; /* 钻石师傅 - 粉色 */
}
.grade-item:nth-child(2) .grade-name {
  color: #ffa502; /* 金牌师傅 - 金色 */
}
.grade-item:nth-child(3) .grade-name {
  color: #a4b0be; /* 银牌师傅 - 银色 */
}
.grade-item:nth-child(4) .grade-name {
  color: #cd7f32; /* 铜牌师傅 - 铜色 */
}
.grade-item:nth-child(5) .grade-name {
  color: #57606f; /* 普通师傅 - 灰色 */
}
</style>