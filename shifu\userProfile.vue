<template>
  <view class="container">
    <view class="header-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>

    <view class="user-info">
      <view class="status-section">
        <view class="status-container">
          <view class="status-info">
            <text class="status-label">开启接单</text>
            <text class="status-desc">关闭后不可接收订单消息通知</text>
          </view>
          <u-switch v-model="messagePush" @change="change" active-color="#599eff"></u-switch>
        </view>
      </view>

      <view class="button-group">
        <button class="action-button secondary-button" @click="set">
          <text class="button-icon">️</text>
          <text class="button-text">系统设置</text>
        </button>
      </view>
    </view>

    <u-modal
      :show="showSubscribeModal"
      title="提示"
      content="您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收师傅的报价通知。"
      showCancelButton
      cancelText="取消"
      confirmText="去开启"
      @confirm="goToSubscriptionSettings"
      @cancel="showSubscribeModal = false"
    ></u-modal>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {
        avatarUrl: '', // Store the temporary or uploaded avatar URL
      },
      localNickName: '微信用户', // Store the temporary nickname
      originalUserInfo: {
        avatarUrl: '',
        nickName: '',
      },
      shifuid: '',
      messagePush: false,
      value: false, // 师傅状态开关 (Note: this seems to be a duplicate of messagePush's purpose, consider unifying)
      showSubscribeModal: false, // 控制订阅消息弹窗显示
    };
  },
  onLoad() {


    this.loadUserInfo();
    // No need to call getSInfo here for initial switch state, onShow will handle it
  },
  onShow() {
    // When the page is shown or returns from another page (like settings), re-check the status
    this.checkMessagePushStatus();
  },
  methods: {
    async getSInfo() {
      // This method fetches the initial state from your backend
      try {
        const res = await this.$api.shifu.getSInfo();
        if (res.messagePush === -1) {
          this.messagePush = false;
        } else {
          this.messagePush = true;
        }
        console.log("Backend messagePush status:", this.messagePush);
        // Also update 'value' if it's meant to sync with messagePush
        this.value = this.messagePush;
      } catch (error) {
        console.error('Failed to get shifu info from backend:', error);
      }
    },
    async checkMessagePushStatus() {
      // This method checks both backend status AND WeChat subscription status
      await this.getSInfo(); // First, get the state from your backend

      if (this.messagePush) { // If backend says it's enabled, double-check WeChat settings
        try {
          const settingRes = await wx.getSetting({ withSubscriptions: true });
          let hasAcceptedSubscription = false;

          if (settingRes.subscriptionsSetting.itemSettings) {
            const obj = settingRes.subscriptionsSetting.itemSettings;
            // Check if *any* subscription is 'accept' or 'acceptWithForcePush'
            hasAcceptedSubscription = Object.keys(obj).some((key) => obj[key] === 'accept' || obj[key] === 'acceptWithForcePush');
          }

          if (!hasAcceptedSubscription) {
            // If backend says true, but WeChat settings are not, then set messagePush to false locally
            // This is crucial for reflecting the actual user permission status on the UI
            this.messagePush = false;
            this.value = false; // Keep 'value' in sync if used
            console.log("Subscription not accepted, forcing messagePush to false.");
            // Optionally, show a toast or modal here to explain why it was turned off
            // uni.showToast({ title: '请开启订阅消息权限', icon: 'none' });
          }
        } catch (error) {
          console.error('Error checking subscription settings onShow:', error);
          // If there's an error checking settings, it's safer to assume it's off
          this.messagePush = false;
          this.value = false;
        }
      }
    },
    set() {
      uni.openSetting({
        success(res) {
          console.log('openSetting result (System Settings):', res);
        },
        fail(err) {
          console.error('openSetting fail (System Settings):', err);
          uni.showToast({ title: '打开系统设置失败', icon: 'error' });
        }
      });
    },
    async change(val) {
      console.log('师傅状态变更:', val);
      this.value = val; // Keep 'value' in sync with the switch's current state

      if (val) {
        // If enabling, check subscription settings
        try {
          const settingRes = await wx.getSetting({ withSubscriptions: true });
          let hasAcceptedSubscription = false;

          if (settingRes.subscriptionsSetting.itemSettings) {
            const obj = settingRes.subscriptionsSetting.itemSettings;
            // Check if *any* subscription is 'accept' or 'acceptWithForcePush'
            hasAcceptedSubscription = Object.keys(obj).some((key) => obj[key] === 'accept' || obj[key] === 'acceptWithForcePush');
          }

          if (!hasAcceptedSubscription) {
            this.showSubscribeModal = true;
            this.messagePush = false; // Revert switch if no subscription is accepted
            this.value = false; // Revert 'value' as well
            console.log("Subscription not accepted, showing modal and reverting switch.");
            return; // Stop here, wait for user action on modal
          }
        } catch (error) {
          console.error('Error getting subscription settings during switch change:', error);
          uni.showToast({ title: '获取订阅设置失败', icon: 'error' });
          this.messagePush = false; // Revert switch on error
          this.value = false;
          return;
        }
      }

      // If we reach here, it means either:
      // 1. The user is turning it off.
      // 2. The user is turning it on AND they have accepted at least one subscription.
      let messagePushStatus = this.value ? 0 : -1; // Use this.value as it reflects the current switch state
      try {
        const res = await this.$api.shifu.updateMessagePush({
          id: this.shifuid,
          messagePush: messagePushStatus,
        });
        if (res.code === '200') {
          uni.showToast({ title: res.data, icon: 'success' });
          // After successful update, ensure messagePush also reflects this.value
          this.messagePush = this.value;
        } else {
          uni.showToast({ title: '请稍后重试', icon: 'error' });
          // If API call fails, revert the switch to its previous state
          this.messagePush = !this.value; // Revert the UI switch
          this.value = !this.value; // Revert the data property
        }
        console.log("Update messagePush API response:", res);
      } catch (error) {
        console.error('Failed to update message push status:', error);
        uni.showToast({ title: '更新失败，请稍后重试', icon: 'error' });
        // If API call fails, revert the switch to its previous state
        this.messagePush = !this.value; // Revert the UI switch
        this.value = !this.value; // Revert the data property
      }
    },
    goToSubscriptionSettings() {
      this.showSubscribeModal = false;
      uni.openSetting({
        withSubscriptions: true, // This is crucial for navigating to the subscription settings
        success(res) {
          console.log('openSetting success (Subscription Settings):', res);
          // After returning from settings, onShow will trigger and re-check
        },
        fail(err) {
          console.error('openSetting fail (Subscription Settings):', err);
          uni.showToast({ title: '跳转设置失败', icon: 'error' });
        },
      });
    },
    loadUserInfo() {
      const cachedUserInfos = uni.getStorageSync('shiInfo') || '{}'; // Ensure it defaults to empty object string
      const cachedUserInfo = JSON.parse(cachedUserInfos);
      console.log('Cached User Info:', cachedUserInfo);
      this.shifuid = cachedUserInfo.id || '';
      this.userInfo.avatarUrl = cachedUserInfo.avatarUrl || '';
      this.localNickName = cachedUserInfo.coachName || '微信用户';
      this.originalUserInfo.avatarUrl = this.userInfo.avatarUrl;
      this.originalUserInfo.nickName = this.localNickName;
      console.log('Loaded user info from cache:', cachedUserInfo);
    },
  },
};
</script>

<style lang="scss">
/* Your existing styles remain unchanged */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  padding-top: 40rpx;
  position: relative;
  overflow: hidden;
}

.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  pointer-events: none;

  .decoration-circle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;

    &.circle-1 {
      width: 120rpx;
      height: 120rpx;
      top: -60rpx;
      right: 100rpx;
      animation: float 6s ease-in-out infinite;
    }

    &.circle-2 {
      width: 80rpx;
      height: 80rpx;
      top: 50rpx;
      right: 300rpx;
      animation: float 4s ease-in-out infinite reverse;
    }

    &.circle-3 {
      width: 60rpx;
      height: 60rpx;
      top: 20rpx;
      left: 80rpx;
      animation: float 5s ease-in-out infinite;
    }
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20rpx); }
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  backdrop-filter: blur(10px);
  width: 90%;
  padding: 60rpx 40rpx;
  border-radius: 30rpx;
  margin-top: 80rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    border-radius: 30rpx 30rpx 0 0;
  }
}

.status-section {
  width: 100%;
  margin-bottom: 60rpx;

  .status-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f3ff 100%);
    border-radius: 20rpx;
    border: 2rpx solid rgba(89, 158, 255, 0.1);

    .status-info {
      display: flex;
      flex-direction: column;

      .status-label {
        font-size: 32rpx;
        color: #333;
        font-weight: 600;
        margin-bottom: 8rpx;
      }

      .status-desc {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

.button-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;

  .action-button {
    width: 100%;
    height: 100rpx;
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: none;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transition: all 0.3s ease;
      transform: translate(-50%, -50%);
    }

    &:active::before {
      width: 200%;
      height: 200%;
    }

    .button-icon {
      margin-right: 15rpx;
      font-size: 28rpx;
    }

    .button-text {
      position: relative;
      z-index: 1;
    }

    &.secondary-button {
      background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
      color: #599eff;
      border: 2rpx solid rgba(89, 158, 255, 0.2);
      box-shadow: 0 5rpx 15rpx rgba(89, 158, 255, 0.1);

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 8rpx rgba(89, 158, 255, 0.1);
      }
    }
  }
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .user-info {
    width: 95%;
    padding: 40rpx 30rpx;
  }
}
</style>