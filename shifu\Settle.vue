<template>
  <view class="page">
    <u-picker
      v-if="flag"
      :show="showCity"
      ref="uPicker"
      :loading="loading"
      :columns="columnsCity"
      @change="changeHandler"
      keyName="title"
      @cancel="showCity = false"
      @confirm="confirmCity"
    ></u-picker>
    <u-modal
      :show="show"
      :title="title"
      :showCancelButton="true"
      confirmText="同意"
      cancelText="不同意"
      @confirm="confirmModel"
      @cancel="cancelModel"
    >
      <view class="slot-content">
        <rich-text :nodes="configInfos.entryNotice?configInfos.entryNotice:getentryNotices"></rich-text>
      </view>
    </u-modal>
    <u-modal
      v-if="shInfo.status == 4"
      :show="showSh"
      title="驳回原因"
      confirmText="确定"
      @confirm="showSh = false"
      :content="shInfo.shText"
    ></u-modal>
    <u-modal
      :show="showSubmitConfirm"
      title="提交确认"
      :showCancelButton="true"
      confirmText="确认"
      cancelText="取消"
      @confirm="confirmSubmit"
      @cancel="cancelSubmit"
    >
      <view class="slot-content">
        <text>确定要提交信息吗？</text>
      </view>
    </u-modal>
    <view
      class="header"
      v-if="shInfo.status !== -1"
      :style="'color:' + arr[shInfo.status - 1].color"
      @click="shDetail"
    >
      {{ arr[shInfo.status - 1].text }}
    </view>

    <view class="main">
      <view class="main_item">
        <view class="title"><span>*</span>姓名</view>
        <input type="text" v-model="form.coachName" placeholder="请输入姓名" :disabled="isFormDisabled" />
      </view>
     <view class="main_item">
       <view class="title"><span>*</span>手机号</view>
       <input type="text" v-model="form.mobile" placeholder="请输入手机号" :disabled="isFormDisabled" />
     </view>

      <view class="main_item">
        <view class="title"><span>*</span>选择服务</view>
        <input type="text" v-model="serviceInfoName?serviceInfoName:form.serviceInfo" placeholder="请选择服务" disabled :class="{ 'input-disabled': !isFormDisabled }" @click="!isFormDisabled && navigateToSkills()">
      </view>
     <view class="main_item">
        <view class="title"><span>*</span>选择区域</view>
        <input
          type="text"
          v-model="form.city"
          placeholder="请选择代理区域"
          disabled
          :class="{ 'input-disabled': !isFormDisabled }"
          @click="!isFormDisabled && (showCity = true)"
        />
      </view>

      <view @tap="goMap" class="main_item">
        <view class="title"><span>*</span>详细地址</view>
        <view class="address">
			 <input v-model="form.address" placeholder="请点击选取地址" :disabled="isFormDisabled" />
        </view>
      </view>
    </view>
    <view class="footer" v-if="shInfo.status === -1 || shInfo.status === 4">
      <view class="btn" @click="showConfirmPopup">立即提交</view>
    </view>
  </view>
</template>

<script>
import Upload from '@/components/upload.vue';
import { mapState } from 'vuex';
export default {
  components: {
    Upload,
    ...mapState({
      configInfo: (state) => state.config.configInfo,
    })
  },
  data() {
    return {
      flag: false,
      showSh: false,
      shInfo: { status: -1 },
      xuzhi: '',
      title: '入驻须知',
      show: false,
      getentryNotices: '',
      showSubmitConfirm: false,
      arr: [
        { text: '信息审核中，请稍作等待', color: '#FE921B' },
        { text: '审核成功', color: '#07C160' },
        {},
        { text: '审核失败>点击查看', color: '#E72427' },
      ],
      serviceInfoName: '',
      configInfos: {
        entryNotice: ''
      },
      form: {
        coachName: '',
        sex: 0,
        mobile: this.userInfophone,
        workTime: '',
        city: '',
        cityId: [],
        address: '',
        idCode: '',
        userInfophone: '',
        text: '',
        id_card1: [],
        id_card2: [],
        selfImg: [],
      },
      showCity: false,
      loading: false,
      columnsCity: [[], [], []],
    };
  },
  computed: {
    isFormDisabled() {
      return !(this.shInfo.status === -1 || this.shInfo.status === 4);
    }
  },
  methods: {
    goMap() {
      let that = this
      // #ifdef MP-WEIXIN
      uni.authorize({
        scope: 'scope.userLocation',
        success(res) {
          uni.chooseLocation({
            success: function(res) {
              console.log(res)
              that.form.address = res.name
              that.form.addressInfo = res.address
              that.form.lng = res.longitude
              that.form.lat = res.latitude
            }
          });
        },
        fail(err) {
          console.error(err)
        }
      })
      // #endif
      // #ifdef APP
      uni.chooseLocation({
        success: function(res) {
          console.log(res)
          that.form.address = res.name
          that.form.addressInfo = res.address
          that.form.lng = res.longitude
          that.form.lat = res.latitude
        }
      });
      // #endif
    },
    navigateToSkills() {
      uni.navigateTo({
        url: '/shifu/skillsIndex'
      });
    },
    confirmCity(Array) {
      this.form.city = Array.value
        .map((item, index) => {
          if (item == undefined) {
            return this.columnsCity[index][0].title;
          } else {
            return item.title;
          }
        })
        .join('-');
      this.form.cityId = Array.value.map((e, j) => {
        if (e == undefined) {
          return this.columnsCity[j][0].id;
        } else {
          return e.id;
        }
      });
      this.showCity = false;
    },
    changeHandler(e) {
      const {
        columnIndex,
        index,
        picker = this.$refs.uPicker,
      } = e;
      if (columnIndex === 0) {
        this.$api.shifu.getCity(this.columnsCity[0][index].id).then((res) => {
          picker.setColumnValues(1, res);
          this.columnsCity[1] = res;
          this.$api.service.getCity(res[0].id).then((res1) => {
            picker.setColumnValues(2, res1);
            this.columnsCity[2] = res1;
          });
        });
      } else if (columnIndex === 1) {
        this.$api.shifu.getCity(this.columnsCity[1][index].id).then((res) => {
          picker.setColumnValues(2, res);
          this.columnsCity[2] = res;
        });
      }
    },
    shDetail() {
      if (this.shInfo.status != 4) return;
      this.showSh = true;
    },
    imgUpload(e) {
      console.log('imgUpload event:', e);
      const { imagelist, imgtype } = e;
      this.$set(this.form, imgtype, imagelist);
    },
    showConfirmPopup() {
      this.showSubmitConfirm = true;
    },
    confirmSubmit() {
      this.showSubmitConfirm = false;
      this.submit();
    },
    cancelSubmit() {
      this.showSubmitConfirm = false;
    },
    submit() {
      const requiredFields = ['coachName', 'mobile', 'city', 'cityId', 'address'];
      console.log(requiredFields)
      for (let key of requiredFields) {
        if (this.form[key] === '' || (Array.isArray(this.form[key]) && this.form[key].length === 0)) {
          uni.showToast({
            icon: 'none',
            title: '请填写必填项',
          });
          return;
        }
      }

      let phoneReg = /^1[3456789]\d{9}$/;
      if (!phoneReg.test(this.form.mobile)) {
        uni.showToast({
          icon: 'none',
          title: '请填写正确的手机号',
          duration: 1000,
        });
        return;
      }

      let obj = {
        id: this.form.id || 0,
        coachName: this.form.coachName,
        mobile: this.form.mobile,
        address: this.form.address,
        city: this.form.city,
        cityId: Array.isArray(this.form.cityId) ? this.form.cityId.join(',') : this.form.cityId,
        userId: uni.getStorageSync('userId') || '',
        idCode: this.form.idCode,
        serviceIds: uni.getStorageSync('selectedServices') || this.form.serviceIds || '',
        idCard: [
          this.form.id_card1 && this.form.id_card1[0] && this.form.id_card1[0].path
            ? this.form.id_card1[0].path
            : '',
          this.form.id_card2 && this.form.id_card2[0] && this.form.id_card2[0].path
            ? this.form.id_card2[0].path
            : ''
        ],
        selfImg: Array.isArray(this.form.selfImg)
          ? this.form.selfImg.map(img => img.path || '')
          : [],
        sex: this.form.sex,
        text: this.form.text,
        workTime: parseInt(this.form.workTime),
        lat: this.form.lat ? this.form.lat : String(uni.getStorageSync('lat')) || '33.06457',
        lng: this.form.lng? this.form.lng : String(uni.getStorageSync('lng')) || '115.25811',
        labelId: 0
      };
	console.log(obj)
      this.$api.shifu.masterEnter(obj).then(res => {
        if (res.data === "ok") {
          uni.showToast({
            icon: 'success',
            title: '提交成功，等待审核',
          });
          setTimeout(() => {
            uni.redirectTo({
              url: '/shifu/mine'
            });
          }, 2000);
          uni.removeStorageSync('selectedServiceNames');
          uni.removeStorageSync('selectedServices');
        } else {
          uni.showToast({
            icon: 'none',
            title: res.msg || '提交失败',
          });
          setTimeout(() => {
            uni.redirectTo({
              url: '/shifu/mine'
            });
          }, 2000);
          uni.removeStorageSync('selectedServiceNames');
          uni.removeStorageSync('selectedServices');
        }
      }).catch(err => {
        uni.showToast({
          icon: 'none',
          title: err.msg || '网络错误'
        });
        uni.removeStorageSync('selectedServiceNames');
        uni.removeStorageSync('selectedServices');
        setTimeout(() => {
          uni.redirectTo({
            url: '/shifu/mine'
          });
        }, 2000);
      });
    },
    cancelModel() {
      uni.navigateBack();
    },
    confirmModel() {
      this.show = false;
    },
    getcity(e) {
      this.$api.shifu.getCity(e).then((res) => {
        this.columnsCity[0] = res;
        this.$api.service.getCity(res[0].id).then((res1) => {
          this.columnsCity[1] = res1;
          this.$api.service.getCity(res1[0].id).then((res2) => {
            this.columnsCity[2] = res2;
            this.flag = true;
          });
        });
      });
    },
    seeInfo() {
      this.$api.shifu.getMaster().then((res) => {
        if (res && Object.keys(res).length !== 0) {
          console.log(res);
          this.shInfo = res;
          this.form = {
            ...this.form,
            ...res,
            id_card1: res.idCard && res.idCard[0] ? [{ path: res.idCard[0] }] : [],
            id_card2: res.idCard && res.idCard[1] ? [{ path: res.idCard[1] }] : [],
            selfImg: res.selfImg ? res.selfImg.map((item) => ({ path: item })) : [],
          };
        } else {
          this.show = true;
        }
      });
    },
  },
  onLoad() {
    this.$api.base.getConfig().then(res => {
      console.log(res);
      this.getentryNotices = res.entryNotice;
      console.log(this.getentryNotices);
    });
    this.serviceInfoName = uni.getStorageSync("selectedServiceNames");
    let userphone = uni.getStorageSync("userInfo");
    console.log(userphone)
    this.form.mobile = userphone.phone
    const configInfo = uni.getStorageSync('configInfo');
    if (configInfo) {
      this.configInfos = configInfo;
    }
    console.log(this.configInfos);
    this.getcity(0);
    this.seeInfo();
  },
  onShow() {
    this.serviceInfoName = uni.getStorageSync("selectedServiceNames") || '';
    uni.$on('getShInfo', (data) => {
      if (data) {
        this.seeInfo();
      }
    });
  },
  onUnload() {
    uni.$off('getShInfo');
    uni.removeStorageSync('selectedServiceNames');
    uni.removeStorageSync('selectedServices');
  },

};
</script>

<style scoped lang="scss">
.page {
  padding-bottom: 200rpx;

  .header {
    width: 750rpx;
    height: 58rpx;
    background: #fff7f1;
    line-height: 58rpx;
    text-align: center;
    font-size: 28rpx;
    font-weight: 400;
  }

  .main {
    padding: 40rpx 30rpx;

    .main_item {
      margin-bottom: 20rpx;

      .title {
        margin-bottom: 20rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;

        span {
          color: #e72427;
        }
      }

      input {
        width: 690rpx;
        height: 110rpx;
        background: #f8f8f8;
        font-size: 28rpx;
        font-weight: 400;
        line-height: 110rpx;
        padding: 0 40rpx;
        box-sizing: border-box;

        &:disabled {
          background: #f0f0f0;
          color: #999;
          cursor: not-allowed;
        }
      }

      .input-disabled {
        background: #f8f8f8 !important;
        color: #333 !important;
        cursor: default !important;
      }

      .card {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .card_item {
          width: 332rpx;
          height: 332rpx;
          background: #f2fafe;
          border-radius: 16rpx;
          overflow: hidden;

          .top {
            height: 266rpx;
            width: 100%;
            padding-top: 40rpx;

            .das {
              margin: 0 auto;
              width: 266rpx;
              height: 180rpx;
              border: 2rpx dashed #2e80fe;
              padding-top: 28rpx;

              .up {
                margin: 0 auto;
                width: 210rpx;
                height: 130rpx;
              }
            }
          }

          .bottom {
            height: 66rpx;
            width: 100%;
            background-color: #2e80fe;
            font-size: 28rpx;
            font-weight: 400;
            color: #ffffff;
            text-align: center;
            line-height: 66rpx;
          }
        }
      }

      .disabled-upload {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
        border-radius: 8rpx;

        .image-preview {
          width: 100%;
          height: 100%;

          image {
            width: 100%;
            height: 100%;
            border-radius: 8rpx;
          }
        }

        .upload-placeholder {
          color: #999;
          font-size: 24rpx;
          text-align: center;
        }
      }

      .disabled-upload-list {
        display: flex;
        justify-content: center;
        width: 100%;

        .image-list {
          display: flex;
          flex-wrap: wrap;
          gap: 20rpx;
          justify-content: center;

          .image-item {
            width: 200rpx;
            height: 200rpx;
            border-radius: 8rpx;
            overflow: hidden;

            image {
              width: 100%;
              height: 100%;
            }
          }
        }

        .upload-placeholder {
          width: 200rpx;
          height: 200rpx;
          background: #f5f5f5;
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #999;
          font-size: 24rpx;
        }
      }
    }
  }

  .footer {
    padding: 52rpx 30rpx;
    width: 750rpx;
    background: #ffffff;
    border-top: 1rpx solid #e8e8e8;
    position: fixed;
    bottom: 0;
    z-index: 999;

    .btn {
      width: 690rpx;
      height: 98rpx;
      background: #2e80fe;
      border-radius: 50rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #ffffff;
      line-height: 98rpx;
      text-align: center;
    }
  }
}
</style>