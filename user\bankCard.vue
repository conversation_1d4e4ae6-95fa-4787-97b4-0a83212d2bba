<template>
	<view class="page">
		<u-modal :show="show" title="删除银行卡" content='确认要删除该银行卡吗？' @cancel="show=false" showCancelButton @confirm="confirm"></u-modal>
		<view class="card_item" v-for="(item,index) in list" :key="index" @tap="clickItem(item)">
			<view class="title">{{item.bankName}}</view>
			<view class="num">{{item.cardNo}}</view>
			<view class="trash" @tap.stop="trashOne(item)">
				<uni-icons type="trash-filled" size="30" color="#fff"></uni-icons>
			</view>
		</view>
		<view class="add" @click="goUrl('../user/addcard')">
			<view class="left">
				<image src="../static/images/9582.png" mode=""></image>
				<text>添加银行卡</text>
			</view>
			<view class="right"><u-icon name="arrow-right-double" color="#999999" size="18"></u-icon></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list:[],
				show:false,
				id:''
			}
		},
		methods: {
			clickItem(item){
				let pages = getCurrentPages()
				let route = pages[pages.length-2].route
				if(route == "pages/cashOut" || route == "pages/cashCoachOut"){
					uni.$emit('chooseCard',item)
					uni.navigateBack()
				}
			},
			confirm(){
				this.show = false
				this.$api.service.delcardlist(this.id).then(res=>{
					uni.showToast({
						icon:'success',
						title:'删除成功'
					})
					
					this.getList()
				})
			},
			goUrl(e){
				uni.navigateTo({
					url:e
				})
			},
			trashOne(item){
				this.show = true
				this.id = item.id
			},
			getList(){
				this.$api.service.getuserCardlist().then(res=>{
					console.log(res.list)
					this.list=res.list
					// res.forEach(item=>{
					// 	let length = item.cardNo.length
					// 	if(length == 16){
					// 		let str1 = item.cardNo.slice(0,12)
					// 		item.cardNo = item.cardNo.replace(str1,'****************')
					// 	}else{
					// 		let str1 = item.cardNo.slice(0,15)
					// 		item.cardNo = item.cardNo.replace(str1,'*******************')
					// 	}
					// })
					// this.list = res
				})
			}
		},
		onShow() {
			this.getList()
		}
	}
</script>

<style scoped lang="scss">
.page{
	padding: 40rpx 32rpx;
	min-height: 100vh;
	overflow: auto;
	background: #F8F8F8;
	.card_item{
		width: 686rpx;
		height: 250rpx;
		background: linear-gradient(270deg, #34538D 0%, #4E89B7 100%);
		border-radius: 12rpx 12rpx 12rpx 12rpx;
		margin-bottom: 20rpx;
		padding: 0 40rpx;
		padding-top: 56rpx;
		position: relative;
		
		.title{
			font-size: 36rpx;
			font-weight: 500;
			color: #FFFFFF;
		}
		.num{
			font-size: 36rpx;
			font-weight: 500;
			color: #FFFFFF;
			margin-top: 62rpx;
		}
		.trash{
			position: absolute;
			top: 30rpx;
			right: 30rpx;
		}
	}
	.add{
		width: 686rpx;
		height: 102rpx;
		background: #FFFFFF;
		border-radius: 12rpx 12rpx 12rpx 12rpx;
		opacity: 1;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 40rpx;
		.left{
			display: flex;
			align-items: center;
			image{
				width: 41rpx;
				height: 41rpx;
			}
			text{
				margin-left: 24rpx;
				font-size: 28rpx;
				font-weight: 500;
				color: #333333;
			}
		}
	}
}
</style>
