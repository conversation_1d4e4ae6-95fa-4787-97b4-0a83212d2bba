import $api from "@/api/index.js"
export default {
	state: {
		pageActive: false,
		activeIndex: 0,
		tabList: [{
			title: '全部技师',
			id: 0
		}, {
			title: '可服务',
			id: 1,
		}, {
			title: '服务中',
			id: 2
		}],
		cityId: 0,
		cityIndex: -1,
		cityList: [],
		param: {
			page: 1,
			ser_id: 0,
			coach_name: ''
		},
		list: {
			data: [],
			last_page: 1,
			current_page: 1
		}
	},
	mutations: {
		async updateTechnicianItem(state, item) {
			let {
				key,
				val
			} = item
			state[key] = val
		}
	},
	actions: {
		async getCityList({
			commit,
			state
		}, param) {
			let d = await $api.base.getCity(param);
			let ind = d.findIndex(v => {
				return v.is_select
			})
			commit('updateTechnicianItem', {
				key: 'cityList',
				val: d
			})
			commit('updateTechnicianItem', {
				key: 'cityIndex',
				val: ind === -1 ? 0 : ind
			})
		},
		async getServiceCoachList({
			commit,
			state
		}, param) {
			let d = await $api.service.serviceCoachList(param)
			let oldList = state.list;
			let newList = d;
			let list = {}
			if (param.page == 1) {
				list = newList;
			} else {
				newList.data = oldList.data.concat(newList.data)
				list = newList;
			}
			commit('updateTechnicianItem', {
				key: 'list',
				val: list
			})
		}
	},
}
