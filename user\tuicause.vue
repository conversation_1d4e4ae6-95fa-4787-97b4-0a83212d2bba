<template>
	<view class="page">
		<view class="title">退款原因</view>
		<view class="inp">
			<textarea placeholder="请输入内容(50字以内)" v-model="text"></textarea>
		</view>
		<view class="btn" @click="submit">提交</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				order_id: '',
				text: ''
			}
		},
		methods: {
			submit(){
					const params = {
					  orderId: this.order_id,
					  text: this.text
					};
					const jsonString = JSON.stringify(params);
				this.$api.service.applyTui(jsonString).then(res=>{
					if(res.code==="-1"){
						uni.showToast({
							icon:'none',
							title:res.msg
						})
					}else{
						uni.showToast({
							icon:'none',
							title:'提交成功，请耐心等待审核'
						})
						setTimeout(()=>{
							uni.$emit('cancelOr')
							uni.navigateBack()
						},1000)
					}
				})
			}
		},
		onLoad(options) {
			this.order_id = options.order_id
		}
	}
</script>

<style scoped lang="scss">
	.page {
		padding: 40rpx 30rpx;
		background: #f8f8f8;
		height: 100vh;

		.title {
			font-size: 32rpx;
			font-weight: 400;
			color: #323232;
		}

		.inp {
			margin-top: 30rpx;

			textarea {
				box-sizing: border-box;
				width: 690rpx;
				height: 280rpx;
				background: #FFFFFF;
				padding: 36rpx 40rpx;
			}
		}
		.btn{
			position: fixed;
			left: 30rpx;
			bottom: 68rpx;
			width: 690rpx;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 50rpx 50rpx 50rpx 50rpx;
			line-height: 98rpx;
			text-align: center;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
		}
	}
</style>