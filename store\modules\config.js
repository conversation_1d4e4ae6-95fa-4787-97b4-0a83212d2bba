import $api from "@/api/index.js";

export default {
  state: {
    // Theme configuration
    configInfo: {
      id: 0,
      navBarHeight: uni.getSystemInfoSync().statusBarHeight * 1 + 44,
      isIos: uni.getSystemInfoSync().system.includes("iOS"),
      canIUseGetUserProfile: uni.getUserProfile ? true : false,
      refund_img: "https://lbqny.migugu.com/paotui/errand.png",
      tabBar: [
        {
          name: "首页",
          icon: "home-fill",
          value: 0,
        },
        {
          name: "分类",
          icon: "grid-fill",
          value: 1,
        },
        {
          name: "购物车",
          icon: "shopping-cart-fill",
          value: 2,
        },
        {
          name: "我的",
          icon: "account-fill",
          value: 3,
        },
      ],
      curSysHeight: "",
      tabbarHeight: "",
    },
    audioBg: {},
    playBg: false,
    isHaveAudio: false,
  },
  mutations: {
    // Switch tab bar configuration
    changeTabBar(state, data) {
      if (data == 0) {
        state.configInfo.tabBar = [
          {
            name: "首页",
            icon: "home-fill",
            value: 0,
          },
          {
            name: "分类",
            icon: "grid-fill",
            value: 1,
          },
          {
            name: "购物车",
            icon: "shopping-cart-fill",
            value: 2,
          },
          {
            name: "我的",
            icon: "account-fill",
            value: 3,
          },
        ];
      } else {
        state.configInfo.tabBar = [
          {
            name: "接单大厅",
            icon: "fingerprint",
            value: 4,
          },
          {
            name: "我的",
            icon: "account-fill",
            value: 3,
          },
        ];
      }
    },
    // Update configuration item
    updateConfigItem(state, item) {
      let { key, val } = item;
      state[key] = val;
      if (key !== "configInfo") return;
      uni.setStorageSync("configInfo", val);
      if (state.isHaveAudio) return;
      state.audioBg = uni.createInnerAudioContext();
      state.isHaveAudio = true;
      let { countdown_voice } = val;
      state.audioBg.src = countdown_voice;
      // #ifndef APP-PLUS
      state.audioBg.obeyMuteSwitch = false;
      // #endif
      let play_method = [
        {
          method: "onPlay",
          msg: "开始播放",
          status: true,
        },
        {
          method: "onStop",
          msg: "结束播放",
          status: false,
        },
        {
          method: "onError",
          msg: "报错Error",
          status: false,
        },
        {
          method: "onEnded",
          msg: "自然结束播放",
          status: false,
        },
      ];
      play_method.map((item) => {
        state.audioBg[item.method](() => {
          console.log("bg=>", item.msg);
          state.playBg = item.status;
        });
      });
    },
  },
  actions: {
    // Fetch configuration info
    async getConfigInfo({ commit, state }, param) {
      try {
        const config = await $api.base.configInfo();
        // Handle undefined or null config
        if (!config) {
          console.warn("API returned undefined or null config, using defaults");
          // Define default config values
          const defaultConfig = {
            primaryColor: "#A40035",
            subColor: "#F1C06B",
            user_image: "https://lbqny.migugu.com/admin/anmo/mine/bg.png",
            coach_image: "https://lbqny.migugu.com/admin/anmo/mine/bg.png",
            user_font_color: "#ffffff",
            coach_font_color: "#ffffff",
          };
          const data = Object.assign({}, state.configInfo, defaultConfig);
          commit("updateConfigItem", { key: "configInfo", val: data });
          return;
        }

        // Apply defaults for missing properties
        const mergedConfig = {
          primaryColor: config.primaryColor || "#A40035",
          subColor: config.subColor || "#F1C06B",
          user_image:
            config.user_image ||
            "https://lbqny.migugu.com/admin/anmo/mine/bg.png",
          coach_image:
            config.coach_image ||
            "https://lbqny.migugu.com/admin/anmo/mine/bg.png",
          user_font_color: config.user_font_color || "#ffffff",
          coach_font_color: config.coach_font_color || "#ffffff",
          ...config,
        };

        // Merge with existing state.configInfo
        const data = Object.assign({}, state.configInfo, mergedConfig);
        commit("updateConfigItem", { key: "configInfo", val: data });
      } catch (error) {
        console.error("Error fetching configInfo:", error);
        // Fallback to default config
        const defaultConfig = {
          primaryColor: "#A40035",
          subColor: "#F1C06B",
          user_image: "https://lbqny.migugu.com/admin/anmo/mine/bg.png",
          coach_image: "https://lbqny.migugu.com/admin/anmo/mine/bg.png",
          user_font_color: "#ffffff",
          coach_font_color: "#ffffff",
        };
        const data = Object.assign({}, state.configInfo, defaultConfig);
        commit("updateConfigItem", { key: "configInfo", val: data });
      }
    },
    // Play audio
    toPlayAudio({ commit, state }, param) {
      if (state.playBg) {
        state.audioBg.stop();
      }
      state.audioBg.play();
    },
  },
};