<template>
	<view class="page">
		<view class="notice_item" v-for="(item,index) in List" :key="index">
			<view class="left">
				<u-icon name="volume-fill" color="#fff" size="20"></u-icon>
			</view>
			<view class="right">
				<view class="title">系统消息</view>
				<view class="ctx">{{item.content}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				List:[]
			}
		},
		methods: {
			async getList(){
				const res = await this.$api.service.messageList()
				console.log(res)
				this.List = res
			}
		},
		mounted() {
			this.getList()
		}
	}
</script>

<style scoped lang="scss">
.page{
	padding: 0 30rpx;
	.notice_item{
		height: 166rpx;
		display: flex;
		align-items: center;
		border-bottom: 2rpx solid #E9E9E9;
		padding: 40rpx 0;
		.left{
			width: 84rpx;
			height: 84rpx;
			background: #AAB3CC;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			
		}
		.right{
			margin-left: 24rpx;
			.title{
				max-width: 580rpx;
				font-size: 28rpx;
				font-weight: 500;
				color: #171717;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			.ctx{
				max-width: 580rpx;
				margin-top: 12rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: #999999;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}
	}
}
</style>
