/**
 * Cookie 解析工具
 * 用于从 Set-Cookie 响应头中提取 autograph 相关的 token
 */

/**
 * 从 Set-Cookie 字符串中提取 autograph token
 * @param {string} setCookieValue - Set-Cookie 响应头的值
 * @returns {string|null} - 提取到的 token 或 null
 */
export function extractAutographToken(setCookieValue) {
  if (!setCookieValue) {
    console.warn('Set-Cookie 值为空');
    return null;
  }

  console.log('开始解析Set-Cookie:', setCookieValue);
  
  // 解析Set-Cookie，提取autograph相关的cookie
  const cookies = setCookieValue.split(';');
  console.log('分割后的cookies:', cookies);

  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim();
    console.log(`检查cookie[${i}]:`, cookie);

    // 查找以autograph开头的cookie（包括autograph-test等变体）
    if (cookie.includes('autograph')) {
      console.log('找到包含autograph的cookie:', cookie);
      // 匹配 autograph=xxx 或 autograph-test=xxx 等格式
      const match = cookie.match(/autograph[^=]*=([^;]+)/);
      if (match) {
        const token = match[1];
        console.log('提取到token:', token);
        return token;
      }
    }
  }

  console.warn('未找到autograph相关的cookie');
  return null;
}

/**
 * 从响应头中查找并提取 autograph token
 * @param {Object} headers - 响应头对象
 * @returns {string|null} - 提取到的 token 或 null
 */
export function extractTokenFromHeaders(headers) {
  if (!headers) {
    console.warn('响应头为空');
    return null;
  }

  console.log('响应头:', headers);

  // 查找Set-Cookie字段（不区分大小写）
  let setCookieValue = '';
  for (const key in headers) {
    if (key.toLowerCase() === 'set-cookie') {
      setCookieValue = headers[key];
      console.log('找到Set-Cookie:', setCookieValue);
      break;
    }
  }

  if (!setCookieValue) {
    console.warn('未找到Set-Cookie字段');
    return null;
  }

  return extractAutographToken(setCookieValue);
}

/**
 * 测试函数 - 用于验证 cookie 解析功能
 */
export function testCookieParser() {
  console.log('=== Cookie 解析器测试 ===');
  
  // 测试用例1: autograph-test cookie
  const testCookie1 = 'autograph-test=Gj0agTgmSFlMn5E2tozcyPwfENpySPtFHKwbD4R4FO8Qz4fujQeU3fvGOsd84Dis; Max-Age=25920; Expires=Wed, 30 Jul 2025 23:16:40 +0800; Path=/';
  const token1 = extractAutographToken(testCookie1);
  console.log('测试1结果:', token1);
  console.log('预期:', 'Gj0agTgmSFlMn5E2tozcyPwfENpySPtFHKwbD4R4FO8Qz4fujQeU3fvGOsd84Dis');
  console.log('测试1通过:', token1 === 'Gj0agTgmSFlMn5E2tozcyPwfENpySPtFHKwbD4R4FO8Qz4fujQeU3fvGOsd84Dis');
  
  // 测试用例2: 普通 autograph cookie
  const testCookie2 = 'autograph=abc123def456; Path=/; HttpOnly';
  const token2 = extractAutographToken(testCookie2);
  console.log('测试2结果:', token2);
  console.log('预期:', 'abc123def456');
  console.log('测试2通过:', token2 === 'abc123def456');
  
  // 测试用例3: 多个cookie
  const testCookie3 = 'sessionid=xyz789; autograph-prod=prod123token; other=value';
  const token3 = extractAutographToken(testCookie3);
  console.log('测试3结果:', token3);
  console.log('预期:', 'prod123token');
  console.log('测试3通过:', token3 === 'prod123token');
  
  // 测试用例4: 响应头测试
  const testHeaders = {
    'Content-Type': 'application/json',
    'Set-Cookie': 'autograph-test=testtoken123; Max-Age=3600',
    'Date': 'Wed, 30 Jul 2025 08:04:40 GMT'
  };
  const token4 = extractTokenFromHeaders(testHeaders);
  console.log('测试4结果:', token4);
  console.log('预期:', 'testtoken123');
  console.log('测试4通过:', token4 === 'testtoken123');
  
  console.log('=== 测试完成 ===');
}
