<template>
	<view class="page">
		<view class="main_item" v-for="(item,index) in list" :key="index">
			<view class="top">
				<!-- 满减 -->
				<view class="box1" v-if="item.type == 0"><span>满</span>{{item.full}}<span>减</span>{{item.discount}}
				</view>
				<!-- 无门槛 -->
				<view class="box1" v-else><span>￥</span>{{item.discount}}</view>
				<view class="box2">
					<text>{{item.title}}</text>
					<span v-if="item.startTime == 0">有效期：自领券日起{{item.day}}天</span>
					<span v-else>有效期：{{item.startTime}}至{{item.endTime}}</span>
				</view>
				<view class="box3" @click="receive(item.id,item.haveGet)"
					:style="item.haveGet == 1?'background-color:#ADADAD;':''">{{item.haveGet == 1?'已领取':'领取'}}</view>
			</view>
			<view class="bottom">
				{{item.rule}}
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			list: [],
			isReceiving: false // Flag to prevent multiple rapid clicks
		}
	},
	methods: {
		async getList() {
			let userId = uni.getStorageSync('userId');
			console.log(userId);
			try {
				let res = await this.$api.service.getWelfareList({
					userId: userId
				});
				console.log(res);
				this.list = res.list;
				console.log(this.list);
			} catch (err) {
				console.error("获取优惠券列表失败:", err);
				uni.showToast({
					icon: 'error',
					title: '加载失败',
					duration: 1000
				});
			}
		},
		receive: debounce(async function(id, get) { // Debounced receive function
			if (this.isReceiving) return; // Prevent execution if already processing
			this.isReceiving = true;
			if (get == 1) {
				uni.showToast({
					icon: 'none',
					title: '已领取过了',
					duration: 1000
				});
				this.isReceiving = false;
				return;
			}
			try {
				let res = await this.$api.service.getWelfare({
					couponId: [id]
				});
				if (res.code==="200") {
					uni.showToast({
						icon: 'success',
						title: '领取成功',
						duration: 1000
					});
					// Update local list to reflect claimed status
					this.list = this.list.map(item => {
						if (item.id === id) {
							return { ...item, haveGet: 1 };
						}
						return item;
					});
					// Refresh coupon list from server
					await this.getList();
				} else {
					uni.showToast({
						icon: 'error',
						title: '领取失败',
						duration: 1000
					});
				}
			} catch (err) {
				console.error("领取优惠券失败:", err);
				uni.showToast({
					icon: 'error',
					title: err,
					duration: 1000
				});
			} finally {
				this.isReceiving = false; // Reset flag after processing
			}
		}, 1000), // 1000ms debounce delay
	},
	mounted() {
		this.getList();
	}
}

// Debounce function to limit rapid executions
function debounce(func, wait) {
	let timeout;
	return function(...args) {
		const context = this;
		clearTimeout(timeout);
		timeout = setTimeout(() => func.apply(context, args), wait);
	};
}
</script>

<style scoped lang="scss">
	.page {
		overflow: auto;
		background-color: #F8F8F8;
		height: 100vh;
		padding-top: 40rpx;

		.main_item {
			margin: 0 auto;
			width: 690rpx;
			height: 202rpx;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			margin-bottom: 20rpx;

			.top {
				height: 150rpx;
				display: flex;
				align-items: center;
				padding-top: 26rpx;
				padding-left: 24rpx;
				padding-right: 14rpx;
				position: relative;
				border-bottom: 2rpx solid #E9E9E9;

				.box1 {
					text-align: center;
					width: 180rpx;
					font-size: 40rpx;
					font-weight: 500;
					color: #E72427;

					span {
						font-size: 20rpx;
					}
				}

				.box2 {
					margin-left: 28rpx;

					text {
						display: block;
						font-size: 32rpx;
						font-weight: 500;
						color: #171717;
						max-width: 450rpx;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}

					span {
						margin-top: 10rpx;
						font-size: 24rpx;
						font-weight: 400;
						color: #B2B2B2;
					}
				}

				.box3 {
					position: absolute;
					right: 24rpx;
					top: 24rpx;
					width: 100rpx;
					height: 42rpx;
					background: #2E80FE;
					border-radius: 22rpx 22rpx 22rpx 22rpx;
					font-size: 20rpx;
					font-weight: 500;
					color: #FFFFFF;
					line-height: 42rpx;
					text-align: center;
				}
			}

			.bottom {
				padding: 0 24rpx;
				max-width: 500rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				line-height: 50rpx;
				font-size: 20rpx;
				font-weight: 400;
				color: #B2B2B2;
			}
		}
	}
</style>