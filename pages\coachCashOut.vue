<template>
	<view class="page">
		<view class="header" @tap="goCard">
			<view class="left">提现至</view>
			<view class="right">{{cardname}}
			<u-icon name="arrow-right" color="#333" size="16"></u-icon>
			</view>
		</view>
		<view class="mid">
			<view class="title">提现金额</view>
			<view class="top">
				<view class="t_left">
					<u--input
					    placeholder="请输入提现金额"
						type="number"
					    border="none"
					    v-model="money"
					    @change="change"
						 prefixIcon="rmb"
					  ></u--input>
				</view>
				<view class="r_left" @tap="goAll">全部提现</view>
			</view>
			<view class="bottom">
				可提现金额￥{{allmoney}}
			</view>
		</view>
		<view class="btn" @tap="confirmTx">确认提现</view>
		<text class="tips">温馨提示：提现申请发起后，预计3个工作日内到账。</text>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				money:'',
				allmoney:'',
				cardname:'暂无银行卡',
				
			}
		},
		methods: {
			confirmTx(){
				if(this.money*1>this.allmoney*1){
					uni.showToast({
						title:'超过可提现金额',
						icon:'none'
					})
					return
				}
				this.$api.technician.applyWallet({apply_price:this.money,text:'',type:2}).then(res=>{
					// console.log(res);
					if(res == 1){
						uni.showToast({
							icon:'none',
							title:'申请成功'
						})
						// uni.$emit('refss')
						setTimeout(()=>{
							uni.navigateBack()
						},1000)
					}
				})
			},
			goCard(){
				uni.navigateTo({
					url:'/pages/bankCard'
				})
			},
			goAll(){
				this.money = this.allmoney
			},
			change(e){
				
			},
			getMoney(){
				this.$api.service.masterInfo().then(res=>{
					this.allmoney = res.service_price
					this.money = this.allmoney
				})
			},
			getCard(){
				this.$api.service.getbankcardlist().then(res=>{
					if(res.length > 0){
						this.cardname = res[0].bank_name+"(" +res[0].card_no.substr(-4)+")"
					}
					// console.log(res);
				})
			}
		},
		onLoad() {
			this.getMoney()
			this.getCard()
		},
		onShow(){
			let that = this
			uni.$on('chooseCard',(res)=>{
				that.cardname = res.bank_name+"(" +res.card_no.substr(-4)+")"
			})
		}
	}
</script>

<style scoped lang="scss">
.page{
	background-color: #f8f8f8;
	min-height: 100vh;
	padding: 20rpx 0;
	.header{
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 28rpx;
		font-weight: 500;
		color: #3B3B3B;
		padding: 0 30rpx;
		width: 750rpx;
		height: 118rpx;
		background: #FFFFFF;
		.right{
			display: flex;
			align-items: center;
		}
	}
	.mid{
		margin-top: 20rpx;
		width: 750rpx;
		height: 276rpx;
		background: #FFFFFF;
		padding: 0 30rpx;
		padding-top: 40rpx;
		.title{
			font-size: 28rpx;
			font-weight: 500;
			color: #3B3B3B;
		}
		.top{
			display: flex;
			align-items: flex-end;
			justify-content: space-between;
			padding-top: 28rpx;
			padding-bottom: 20rpx;
			border-bottom: 2rpx solid #F2F3F6;
			.r_left{
				font-size: 28rpx;
				font-weight: 500;
				color: #E51837;
			}
		}
		.bottom{
			padding-top: 20rpx;
			font-size: 24rpx;
			font-weight: 500;
			color: #999999;
			
		}
	}
	.btn{
		margin: 0 auto;
		margin-top: 60rpx;
		width: 690rpx;
		height: 98rpx;
		background: #2E80FE;
		border-radius: 50rpx 50rpx 50rpx 50rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #FFFFFF;
		line-height:98rpx;
		text-align: center;
	}
	.tips{
		display: block;
		font-size: 24rpx;
		font-weight: 500;
		color: #999999;
		text-align: center;
		margin-top: 20rpx;
	}
}
</style>
