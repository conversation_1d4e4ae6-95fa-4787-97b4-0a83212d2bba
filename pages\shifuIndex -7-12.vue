To add the provided `view` block displaying quotation counts, you should place it directly after the main filter container `filter-container` and before the `check_box` view.

Here's the complete updated Vue component code:

```vue
<template>
	
	<view class="page">
	
		<tabbar :cur="0"></tabbar>
		<view class="img">
			<u-swiper :list="list1" height="108"></u-swiper>
		</view>
		<view class="location-bar">
			<view class="location-info">
				<view>当前接单位置：{{ province + city + district || '定位中...' }}</view>
			</view>
		</view>
		<view class="tabs-container">
			<view class="custom-tabs">
				<view 
					class="tab-item" 
					:class="{ 'active': currentTab === index }"
					v-for="(tab, index) in tabsList" 
					:key="index" 
					@click="switchTab(index)"
				>
					<text class="tab-text">{{ tab.name }}</text>
				</view>
			</view>
		</view>
		<view class="filter-container">
			<view class="filter-bar">
				<view class="filter-item-container">
					<view class="filter-item" @click.stop="toggleFilter('price')" :class="{'active-filter-item': isPriceFilterActive}">
						<text>{{ priceFilterText }}</text>
						<text class="arrow" :class="{ 'rotate': showFilter === 'price' }">▼</text>
					</view>
				</view>
				
				<view class="filter-item-container">
					<view class="filter-item" @click.stop="toggleFilter('distance')" :class="{'active-filter-item': isDistanceFilterActive}">
						<text>订单距离 {{ distance }}公里</text>
						<text class="arrow" :class="{ 'rotate': showFilter === 'distance' }">▼</text>
					</view>
				</view>
				
				<view class="filter-item-container">
					<view class="filter-item" @click.stop="toggleFilter('category')" :class="{'active-filter-item': isCategoryFilterActive}">
						<text>{{ currentCateName || '分类筛选' }}</text>
						<text class="arrow" :class="{ 'rotate': showFilter === 'category' }">▼</text>
					</view>
				</view>
			</view>
			
			<view class="filter-dropdown" v-if="showFilter === 'price'" @click.stop>
				<view class="dropdown-content">
					<view class="filter-section">
						<view class="section-title">价格范围</view>
						<view class="option-list">
							<view 
								class="option-item" 
								:class="{ active: priceRange.min === 0 && priceRange.max === 100 && priceRange.type === 'predefined' }"
								@click="selectPriceRange(0, 100, 'predefined')"
							>0 - 100</view>
							<view 
								class="option-item" 
								:class="{ active: priceRange.min === 100 && priceRange.max === 200 && priceRange.type === 'predefined' }"
								@click="selectPriceRange(100, 200, 'predefined')"
							>100 - 200</view>
							<view 
								class="option-item" 
								:class="{ active: priceRange.min === 200 && priceRange.max === 500 && priceRange.type === 'predefined' }"
								@click="selectPriceRange(200, 500, 'predefined')"
							>200 - 500</view>
							<view 
								class="option-item" 
								:class="{ active: priceRange.min === 500 && priceRange.max === null && priceRange.type === 'predefined' }"
								@click="selectPriceRange(500, null, 'predefined')"
							>500以上</view>
						</view>
					</view>
					
					<view class="filter-section">
						<view class="section-title">自定义价格</view>
						<view class="custom-price-inputs">
							<input 
								type="digit" 
								v-model="priceRange.customMin" 
								placeholder="最低价" 
								@blur="validatePriceInput('min')"
							/>
							<text>-</text>
							<input 
								type="digit" 
								v-model="priceRange.customMax" 
								placeholder="最高价" 
								@blur="validatePriceInput('max')"
							/>
						</view>
					</view>
					
					<view class="filter-actions">
						<view class="filter-btn reset" @click="resetPriceFilter">重置</view>
						<view class="filter-btn confirm" @click="applyPriceFilter">确定</view>
					</view>
				</view>
			</view>

			<view class="filter-dropdown" v-if="showFilter === 'distance'" @click.stop>
				<view class="dropdown-content">
					<view class="filter-section">
						<view class="section-title">距离范围</view>
						<view class="distance-slider">
							<text>当前距离: {{distance}}公里</text>
							<slider 
								:min="1" 
								:max="100" 
								:value="distance" 
								:show-value="false" 
								@change="onDistanceChange"
							/>
							<view class="distance-labels">
								<text>1公里</text>
								<text>100公里</text>
							</view>
						</view>
					</view>
					
					<view class="filter-actions">
						<view class="filter-btn reset" @click="resetDistanceFilter">重置</view>
						<view class="filter-btn confirm" @click="applyDistanceFilter">确定</view>
					</view>
				</view>
			</view>
			
			<view class="filter-dropdown" v-if="showFilter === 'category'" @click.stop>
				<view class="dropdown-content">
					<view class="filter-section">
						<view class="section-title">服务品类</view>
						<view class="option-list">
							<view class="option-item" :class="{ active: currentCateId === '' }" @click="selectClick({ id: '', name: '全部' })">全部</view>
							<view 
								class="option-item" 
								v-for="(cate, index) in cateList" 
								:key="index" 
								:class="{ active: currentCateId === cate.id }"
								@click="selectClick(cate)"
							>
								{{ cate.name }}
							</view>
						</view>
					</view>
					
					<view class="filter-actions">
						<view class="filter-btn reset" @click="resetCategoryFilter">重置</view>
						<view class="filter-btn confirm" @click="applyCategoryFilter">确定</view>
					</view>
				</view>
			</view>
		</view>
		<view class="quotation-counts">
			当前等级比价订单接单数量: {{QuotationCounts.comparisonOrder}}
			<br/>
			当前剩余比价订单接单数量: {{QuotationCounts.nowComparisonOrder}}
			<br/>
			当前等级一口价订单接单数量: {{QuotationCounts.fixedPrice}}
			<br/>
		
			当前剩余一口价订单接单数量: {{QuotationCounts.nowFixedPrice}}
		</view>
		<view class="check_box" v-if="false">
		</view>
		<u-empty mode="order" icon="http://cdn.uviewuni.com/uview/empty/order.png" v-if="list.length == 0"></u-empty>
		<view class="re_item" v-for="(item, index) in list" :key="index" @click="seeDetail(item)">
			<view class="top">
				<image :src="item.goodsCover" style="width: 160rpx;height: 160rpx;border-radius: 10rpx;"></image>
				<view class="order">
					<div class="title">{{ item.goodsName }}<span v-if="item.type != 0"
							style="font-size: 24rpx;color:#999;margin-left: 10rpx;">(报价0.00元起)</span></div>
					<div class="price">{{ item.type == 0 ? `￥${item.payPrice}` : '待报价' }}</div>
				</view>
			</view>
			<view @click="dingyue()" class="info">
				<view class="address">
					<view class="left">
						<u-icon name="map-fill" color="#2979ff" size="22"></u-icon>
					</view>
					<view class="right">
						<view class="address_name">{{ item.address }}</view>
						<view class="address_Info">{{ item.addressInfo }}</view>
					</view>
				</view>
				<view class="tel">
					<view class="left">
						<u-icon name="phone-fill" color="#2979ff" size="22"></u-icon>
					</view>
					<view class="right">{{ item.mobile.slice(0, 3) + '********' }}</view>
				</view>
			</view>
			<view class="notes" v-if="item.text != ''">
				<view style="color:#999999;">备注内容:</view>
				{{ item.text }}
			</view>
			<view class="btn" :style="item.type == 1 ? '' : 'background-color:#2E80FE;color:#fff;'"
				@click.stop="seeDetail(item)">
				{{ item.type == 1 ? '立即报价' : '立即接单' }}
			</view>
		</view>
		<u-modal :show="confirmshow" :content="content" showCancelButton @confirm="confirmRe"
			@cancel="confirmshow = false"></u-modal>

		<u-modal :show="masterModalShow" content="您还不是师傅,请去入驻" showCancelButton @confirm="goToSettle"
			@cancel="masterModalShow = false"></u-modal>

		<u-modal :show="detailModalShow" title="服务承诺" showCancelButton cancelText="不同意" confirmText="同意"
			@confirm="confirmDetail" @cancel="detailModalShow = false" v-if="shifustutus.data !== -2 && shifustutus.data !== -1">
			<view class="modal-content">
				<rich-text
					:nodes="getconfigs?getconfigs:configInfo.shifuQualityCommitment"></rich-text>
			</view>
		</u-modal>

		<view class="loadmore" v-if="list.length >= 10">
			<u-loadmore :status="status" />
		</view>
	</view>
</template>

<script>
	import tabbar from "@/components/tabbarsf.vue";
	import {
		mapState,
		mapActions
	} from 'vuex';
	export default {
		components: {
			tabbar
		},
		data() {
			return {
				orderData: '',
				showDingyue: false,
				showCate: false, // This seems redundant with the new filter dropdown, consider removal
				infodata: '',
				tmplIds: [
					'qt5Q70oqYNTB2RaTzl0peEHpjK-iinWr3ZfxGvs4wtg',
					'_2z7Bbw8oq4av-yqP31fZLaI82Z_N52wNM_1ihXQv6I',
					'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
				],
				status: 'loadmore',
				id: '',
				shifuId: '',
				// 新增的选项卡数据
				currentTab: 0,
				tabsList: [
					{ name: '聚合订单', badge: '' },
					{ name: '一口价', badge: '5' },
					{ name: '比价模式', badge: '11' },
					{ name: '高价值', badge: '' }
				],
				listType:[{name:'一口价'},{name:'比价模式'},{name:'高价值 '}],
				list: [],
				confirmshow: false,
				masterModalShow: false,
				detailModalShow: false,
				content: '确认接下该订单吗',
				input: '',
				area_id: '',
				limit: 10, // Changed to 10 for more realistic pagination in examples
				page: 1,
				bannerList: [],
				configInfo: '',
				getconfigs: '',
				list1: [],
				lng: '',
				shifustutus: {
					data: 0,
					msg: ''
				},
				QuotationCounts:'',
				lat: '',
				cateList: [],
				currentCateId: '',
				currentCateName: '分类筛选', // Changed default to '分类筛选' for consistency
				copyCateList: [],
				province: '',
				city: '',
				district: '',
				isPageLoaded: false,
				selectedItem: null,
				// 新增的数据
				priceRange: {
					type: 'all', // 'all', 'predefined', 'custom'
					min: null,
					max: null,
					customMin: '',
					customMax: ''
				},
				distance: 20, // 默认20公里
				appliedDistance: 20, // To track applied distance for filter bar text
				// 折叠面板状态控制 - These seem unused with the new filter dropdown, consider removal
				isPriceCollapsed: true,
				isDistanceCollapsed: true,
				isCategoryCollapsed: true,
				// 筛选相关
				showFilter: null,
				currentActivityType: null,

				areaCount: 0
			};
		},
		computed: {
			...mapState({
				configInfos: (state) => state.config.configInfo,
					regeocode: (state) => state.service.regeocode,
				refreshReceiving: (state) => state.service.refreshReceiving || '',
			}),
			priceFilterText() {
				if (this.priceRange.type === 'all' || (this.priceRange.customMin === '' && this.priceRange.customMax === '')) {
					return '价格区间';
				} else if (this.priceRange.type === 'predefined') {
					return `${this.priceRange.min} - ${this.priceRange.max === null ? '500以上' : this.priceRange.max}`;
				} else if (this.priceRange.type === 'custom') {
					let min = this.priceRange.customMin || '最低价';
					let max = this.priceRange.customMax || '最高价';
					if (this.priceRange.customMin && this.priceRange.customMax) {
						return `${min} - ${max}`;
					} else if (this.priceRange.customMin) {
						return `${min}以上`;
					} else if (this.priceRange.customMax) {
						return `${max}以下`;
					}
					return '自定义价格'; // Fallback
				}
				return '价格区间';
			},
			isPriceFilterActive() {
				return this.priceRange.type !== 'all';
			},
			isDistanceFilterActive() {
				return this.distance !== 20; // Assuming 20km is the default/reset state
			},
			isCategoryFilterActive() {
				return this.currentCateId !== ''; // Assuming empty string is the default/reset state for '全部'
			}
		},
		methods: {
			// 新增的选项卡切换方法
			switchTab(index) {
				this.currentTab = index;
				// 根据选项卡切换不同的数据
				this.page = 1;
				this.list = []; // Clear list on tab switch
				this.getListByTab(index);
				this.closeFilter(); // Close any open filter dropdown
			},
			
			// Helper to get order type based on current tab
			getOrderType(tabIndex) {
				switch(tabIndex) {
					case 0: return undefined; // 聚合订单 - no type
					case 1: return 0; // 一口价
					case 2: return 1; // 比价模式
					case 3: return undefined; // 高价值
					default: return undefined;
				}
			},

			// Helper to get menu type based on current tab
			getMenuType(tabIndex) {
				switch(tabIndex) {
					case 3: return 2; // 高价值
					default: return 1; // 其他tab
				}
			},

			// 根据选项卡获取不同的数据
			async getListByTab(tabIndex) {
				uni.showLoading({
					title: '加载中'
				});
				try {
					let apiParams = {
						lng: this.lng,
						lat: this.lat,
						pageNum: this.page,
						pageSize: this.limit,
						parentId: this.currentCateId || 0,
						type: this.getOrderType(tabIndex), // Set type based on tab
						menu: this.getMenuType(tabIndex), // Set menu based on tab
						distance: this.appliedDistance, // Use appliedDistance
						minPrice: this.priceRange.type !== 'all' ? this.priceRange.min : undefined,
						maxPrice: this.priceRange.type !== 'all' ? this.priceRange.max : undefined,
						userId: this.infodata.userId, // Assuming userId is available in infodata
						quotationNum: true // As per requirement
					};

					// Clean up undefined values from apiParams
					Object.keys(apiParams).forEach(key => apiParams[key] === undefined && delete apiParams[key]);
					
					const res = await this.$api.shifu.indexQuote(apiParams);
						console.log(res)
					if (res.code === "-1") {
						uni.showToast({
							icon: 'none',
							title: res.msg
						}, 3000);
					}
					
					this.$set(this, 'list', res.data.list || []);
					let count = this.list.length;
					uni.setStorageSync('listCount', count);
					this.status = (res.data.list && res.data.list.length < this.limit) ? 'nomore' : 'loadmore';
					this.$forceUpdate();
				} catch (error) {
					console.error("Error fetching list by tab:", error);
					this.$set(this, 'list', []);
					this.status = 'nomore';
				} finally {
					uni.hideLoading();
				}
			},
			
			reset() {
				this.currentCateName = '分类筛选';
				this.currentCateId = '';
				this.priceRange = {
					type: 'all',
					min: null,
					max: null,
					customMin: '',
					customMax: ''
				};
				this.distance = 20;
				this.appliedDistance = 20;
				this.page = 1;
				this.getListByTab(this.currentTab);
			},
			
			// This closeCate method seems redundant if using the new filter dropdown system
			closeCate() {
				this.showCate = false;
				setTimeout(() => {
					this.cateList = this.copyCateList;
				}, 500);
			},
			// This chooseCate method seems redundant if using the new filter dropdown system
			chooseCate() {
				this.showCate = !this.showCate;
			},
			
			async selectClick(cate) {
				// Only update the selected category without immediately applying the filter
				// The filter will be applied when '确定' is clicked in the category dropdown
				this.currentCateName = cate.name;
				this.currentCateId = cate.id;
			},
			async getCate() {
				try {
					const res = await this.$api.shifu.serviceCate();
						console.log(res)
					this.cateList = res || [];
					this.copyCateList = res || []; // Keep a copy if you need to reset the list later
				} catch (error) {
					uni.showToast({
						icon: 'none',
						title: '获取分类失败'
					});
				}
			},
			async seeDetail(item) {
				// Fetch shifu status before opening any modal
				try {
					const res = await this.$api.shifu.getshifstutas({
							
					userId:uni.getStorageSync('userId')
				});
					this.shifustutus = res;
					
					this.selectedItem = item;
					if (this.shifustutus.data === -1) {
						uni.showToast({
							icon: 'none',
							title: this.shifustutus.msg || '无法进行此操作'
						});
						return;
					} else {
						this.detailModalShow = true;
					}
				} catch (error) {
					console.error("Error checking shifu status:", error);
					uni.showToast({
						icon: 'none',
						title: '检查身份失败'
					});
				}
			},
			confirmDetail() {
				if (this.selectedItem) {
					uni.setStorageSync('selectedOrder', this.selectedItem);
					uni.navigateTo({
						url: `/shifu/master_order_details?id=${this.selectedItem.id}`
					});
				}
				this.detailModalShow = false;
				this.selectedItem = null;
			},
			async getList() {
				uni.showLoading({
					title: '加载中'
				});
				try {
					let location = {
						longitude: '',
						latitude: ''
					};
					try {
						// Add a small delay if needed for getLocation, but usually not
						location = await new Promise((resolve, reject) => {
							uni.getLocation({
								type: 'gcj02',
								success: resolve,
								fail: reject
							});
						});
						this.lng = location.longitude;
						this.lat = location.latitude;
					} catch (error) {
						uni.showToast({
							icon: 'none',
							title: '定位失败，使用默认位置'
						});
					}
					try {
						const geoRes = await new Promise((resolve, reject) => {
							uni.request({
								url: `https://restapi.amap.com/v3/geocode/regeo?location=${this.lng},${this.lat}&key=2fb9ec1a184338e3cce567b7d2bab08f`,
								success: resolve,
								fail: reject
							});
						});
						this.$store.dispatch('setRegeocode', {
						                                regeocode: geoRes.data.regeocode,
						                                lat: this.lat,
						                                lng: this.lng
						                            });
						this.province = geoRes.data.regeocode.addressComponent.province || '';
						this.city = geoRes.data.regeocode.addressComponent.city || '';
						this.district = geoRes.data.regeocode.addressComponent.district || '';
					} catch (error) {
						this.province = '安徽省'; // Default province
						this.city = '阜阳市'; // Default city
						this.district = '临泉县'; // Default district
						
					}
					
					// Initial list fetch will correspond to the default tab (聚合订单, tabIndex 0)
					const res = await this.$api.shifu.indexQuote({
						lng: this.lng,
						lat: this.lat,
						parentId: 0,
						pageNum: this.page,
						pageSize: this.limit,
						distance: this.appliedDistance, // Ensure initial load uses appliedDistance
						menu: this.getMenuType(this.currentTab), // Include menu for initial load
						userId: this.infodata.userId, // Assuming userId is available in infodata
						quotationNum: true // As per requirement
						// No type parameter for initial聚合订单 load
					});
						console.log(res)
					if (res.code === "-1") {
						uni.showToast({
							icon: 'none',
							title: res.msg
						}, 3000);
					}
					console.log(res)
					this.$set(this, 'list', res.data.list || []);
					let count = this.list.length;
					uni.setStorageSync('listCount', count);
					this.status = (res.data.list && res.data.list.length < this.limit) ? 'nomore' : 'loadmore';
					this.$forceUpdate();
				} catch (error) {
					console.error("Error fetching initial list:", error);
					this.$set(this, 'list', []);
					this.status = 'nomore';
				} finally {
					uni.hideLoading();
				}
			},
			handleReceive(item) {
				// this.textsss(); // Assuming this is defined elsewhere or not critical
				this.orderData = item;
				this.id = item.id;
				if (item.type == 0) { // Assuming type 0 means fixed price that can be directly received
					this.confirmshow = true;
				}
			},
			confirmRe() {
				this.confirmshow = false;
				this.$api.shifu.rece_Order({
					order_id: this.id
				}).then(res => {
					this.getList();
					uni.showToast({
						icon: 'success',
						title: '接单成功',
						duration: 1000
					});
					setTimeout(() => {
						uni.navigateTo({
							url: '/shifu/master_my_order'
						});
					}, 1000);
				}).catch(error => {
					uni.showToast({
						icon: 'fail',
						title: error.message || '接单失败'
					});
				});
			},
			goToSettle() {
				this.masterModalShow = false;
				uni.navigateTo({
					url: '/shifu/Settle'
				});
			},
			async getServiceInfo() {
				try {
					const res = await this.$api.shifu.index({
						city_id: this.area_id
					});
					console.log(res)
					this.bannerList = res.data || [];
					this.list1 = res.data.map(item => item.img) || [];
					if (!this.list1.length) {
						this.list1 = [
							'https://zskj.asia/attachment/image/666/24/09/2bdd13fab41b42b987bcfc501aa535bb.jpg'
						];
					}
				} catch (error) {
					// uni.showToast({
					// 	icon: 'none',
					// 	title: '获取轮播图失败'
					// });
					this.list1 = ['https://zskj.asia/attachment/image/666/24/09/e790eea3f21b4f48ab2b00b034468035.jpg'];
				}
			},
			onReachBottom() {
				if (this.status == 'nomore') return;
				this.status = 'loading';
				this.page++;
				let apiParams = {
					pageNum: this.page,
					pageSize: this.limit,
					parentId: this.currentCateId || 0,
					lat: this.lat,
					lng: this.lng,
					distance: this.appliedDistance, // Include appliedDistance for pagination
					minPrice: this.priceRange.type !== 'all' ? this.priceRange.min : undefined,
					maxPrice: this.priceRange.type !== 'all' ? this.priceRange.max : undefined,
					userId: this.infodata.userId, // Assuming userId is available in infodata
					quotationNum: true // As per requirement
				};

				// Apply the 'type' and 'menu' parameter based on the current tab for pagination
				apiParams.type = this.getOrderType(this.currentTab);
				apiParams.menu = this.getMenuType(this.currentTab);
				
				// Clean up undefined values from apiParams
				Object.keys(apiParams).forEach(key => apiParams[key] === undefined && delete apiParams[key]);

				this.$api.shifu.indexQuote(apiParams).then(res => {
					if (!res.data || !res.data.list || res.data.list.length === 0) {
						console.log(res)
						this.status = 'nomore';
						uni.showToast({
							icon: 'none',
							title: '没有更多数据了'
						});
						return;
					}
					this.$set(this, 'list', [...this.list, ...(res.data.list || [])]);
					if (res.data.list.length < this.limit) {
						this.status = 'nomore';
					} else {
						this.status = 'loadmore';
					}
				}).catch(error => {
					this.status = 'nomore';
					uni.showToast({
						icon: 'none',
						title: '加载失败，请稍后重试'
					});
				});
			},
			async initializePage() {
				uni.showLoading({
					title: '初始化中'
				});
				try {
					// const systemInfo = uni.getSystemInfoSync(); // This line is not used
					await this.getServiceInfo();
					await this.getCate();
					await this.getList(); // This will load the '聚合订单' initially
				
					// Get userId from storage if available
					if (uni.getStorageSync('shiInfo')) {
						this.infodata = JSON.parse(uni.getStorageSync('shiInfo'));
					}
					
					this.configInfo = uni.getStorageSync('configInfo');
					this.$forceUpdate();
				} catch (error) {
					console.error("Error initializing page:", error);
					uni.showToast({
						icon: 'none',
						title: '初始化失败，请稍后重试'
					});
				} finally {
					uni.hideLoading();
				}
			},
			// 新增的方法
			selectPriceRange(min, max, type) {
				this.priceRange = {
					type: type,
					min: min,
					max: max,
					customMin: min !== null ? min.toString() : '',
					customMax: max !== null ? max.toString() : ''
				};
			},
			
			validatePriceInput(type) {
				if (type === 'min') {
					const min = parseFloat(this.priceRange.customMin);
					if (!isNaN(min) && min >= 0) {
						this.priceRange.min = min;
					} else {
						this.priceRange.customMin = '';
						this.priceRange.min = null;
					}
				} else if (type === 'max') {
					const max = parseFloat(this.priceRange.customMax);
					if (!isNaN(max) && max > 0) {
						this.priceRange.max = max;
					} else {
						this.priceRange.customMax = '';
						this.priceRange.max = null;
					}
				}
				
				// Determine priceRange.type based on custom inputs or predefined selection
				const hasCustomMin = this.priceRange.customMin !== '';
				const hasCustomMax = this.priceRange.customMax !== '';
			
				if (hasCustomMin || hasCustomMax) {
					this.priceRange.type = 'custom';
			
					// Check if custom inputs match any predefined range for highlighting
					const predefinedRanges = [
						{ min: 0, max: 100 },
						{ min: 100, max: 200 },
						{ min: 200, max: 500 },
						{ min: 500, max: null }
					];
					let matchedPredefined = false;
					for (const range of predefinedRanges) {
						if (this.priceRange.min === range.min && this.priceRange.max === range.max) {
							this.priceRange.type = 'predefined';
							matchedPredefined = true;
							break;
						}
					}
					if (!matchedPredefined && (hasCustomMin || hasCustomMax)) {
						this.priceRange.type = 'custom';
					}
				} else if (!this.priceRange.min && !this.priceRange.max) {
					this.priceRange.type = 'all';
				} else if (this.priceRange.type !== 'predefined') {
					// Fallback if somehow min/max are set but custom inputs are empty and not predefined
					this.priceRange.type = 'custom';
				}
			},
			
			onDistanceChange(e) {
				this.distance = e.detail.value;
			},
			
			// This applyFilters method is a general one; the specific apply methods (price, distance, category) will call fetchFilteredData
			applyFilters() {
				// This method can be used if there was a single "Apply All Filters" button.
				// Since we have separate "确定" buttons for each dropdown, we will use
				// applyPriceFilter, applyDistanceFilter, applyCategoryFilter.
				// For now, it will just call the common fetchFilteredData with current state.
				this.fetchFilteredData(this.buildFilterParams());
				this.closeFilter();
			},

			// Adding a helper to build common API parameters
			buildFilterParams() {
				let apiParams = {
					lng: this.lng,
					lat: this.lat,
					pageNum: 1, // Always reset to page 1 on new filter application
					pageSize: this.limit,
					parentId: this.currentCateId || 0,
					distance: this.appliedDistance,
					userId: this.infodata.userId, // Assuming userId is available in infodata
					quotationNum: true // As per requirement
				};
				
				// Add price range parameters
				if (this.priceRange.type !== 'all') {
					apiParams.minPrice = this.priceRange.min;
					apiParams.maxPrice = this.priceRange.max;
				}

				// Set type and menu based on the current active tab
				apiParams.type = this.getOrderType(this.currentTab);
				apiParams.menu = this.getMenuType(this.currentTab);
				
				// Clean up undefined values from apiParams
				Object.keys(apiParams).forEach(key => apiParams[key] === undefined && delete apiParams[key]);

				return apiParams;
			},
			
			// Adding collapse functionality (if still needed, though current UI uses show/hide)
			toggleCollapse(type) {
				if (type === 'price') {
					this.isPriceCollapsed = !this.isPriceCollapsed;
				} else if (type === 'distance') {
					this.isDistanceCollapsed = !this.isDistanceCollapsed;
				} else if (type === 'category') {
					this.isCategoryCollapsed = !this.isCategoryCollapsed;
				}
			},
			// 筛选相关方法
			toggleFilter(filter) {
				if (this.showFilter === filter) {
					this.showFilter = null;
				} else {
					this.showFilter = filter;
				}
			},

			closeFilter() {
				this.showFilter = null;
			},
			// These activity, area, and sort related methods seem to be remnants and aren't tied to the current UI
			// They should be removed if not implemented in the template.
			selectActivityType(item) {
				this.currentActivityType = item.value;
			},
			applyActivityFilter() {
				let apiParams = this.buildFilterParams();
				if (this.currentActivityType) {
					apiParams.activityType = this.currentActivityType;
				}
				this.fetchFilteredData(apiParams);
				this.closeFilter();
			},
			resetActivityFilter() {
				this.currentActivityType = '';
			},
			selectArea(item) {
				this.currentArea = item.name;
			},
			applyAreaFilter() {
				let apiParams = this.buildFilterParams();
				if (this.currentArea) {
					const selectedArea = this.areaList.find(item => item.name === this.currentArea);
					if (selectedArea) {
						apiParams.area = selectedArea.value;
					}
				}
				this.fetchFilteredData(apiParams);
				this.closeFilter();
			},
			resetAreaFilter() {
				this.currentArea = '';
			},
			selectSort(item) {
				this.currentSort = item.name;
			},
			applySortFilter() {
				let apiParams = this.buildFilterParams();
				if (this.currentSort) {
					const selectedSort = this.sortOptions.find(item => item.name === this.currentSort);
					if (selectedSort) {
						apiParams.sort = selectedSort.value;
					}
				}
				this.fetchFilteredData(apiParams);
				this.closeFilter();
			},
			resetSortFilter() {
				this.currentSort = '';
			},
			
			applyAdvancedFilter() {
				// This method is redundant if filters are applied individually.
				// If it's for a "master" apply button, it would call fetchFilteredData with current state.
				this.fetchFilteredData(this.buildFilterParams());
				this.closeFilter();
			},
			
			resetAdvancedFilter() {
				this.priceRange = {
					type: 'all',
					min: null,
					max: null,
					customMin: '',
					customMax: ''
				};
				this.distance = 20;
				this.appliedDistance = 20;
				this.currentCateId = '';
				this.currentCateName = '分类筛选';
				this.page = 1; // Reset page on full reset
				this.fetchFilteredData(this.buildFilterParams()); // Apply reset
				this.closeFilter();
			},
			
			// 统一的数据获取方法
			async fetchFilteredData(apiParams) {
				uni.showLoading({
					title: '加载中'
				});
				this.list = []; // Clear list before fetching new data
				this.page = 1; // Ensure page is reset when new filters are applied

				// Ensure userId is consistently added if infodata is available
				// if (this.infodata && this.infodata.userId) {
				// 	apiParams.userId = this.infodata.userId;
				// }

				try {
					const res = await this.$api.shifu.indexQuote(apiParams);
						console.log(res)
					if (res.code === "-1") {
						uni.showToast({
							icon: 'none',
							title: res.msg
						}, 3000);
					}
					
					this.$set(this, 'list', res.data.list || []);
					let count = this.list.length;
					uni.setStorageSync('listCount', count);
					this.status = (res.data.list && res.data.list.length < this.limit) ? 'nomore' : 'loadmore';
					this.$forceUpdate();
				} catch (error) {
					console.error("Error fetching filtered data:", error);
					this.$set(this, 'list', []);
					this.status = 'nomore';
				} finally {
					uni.hideLoading();
				}
			},
			// 价格筛选相关方法
			applyPriceFilter() {
				this.fetchFilteredData(this.buildFilterParams());
				this.closeFilter();
			},
			
			resetPriceFilter() {
				this.priceRange = {
					type: 'all',
					min: null,
					max: null,
					customMin: '',
					customMax: ''
				};
				this.applyPriceFilter(); // Apply reset immediately
			},
			
			// 距离筛选相关方法
			applyDistanceFilter() {
				this.appliedDistance = this.distance; // Update applied distance
				this.fetchFilteredData(this.buildFilterParams());
				this.closeFilter();
			},
			
			resetDistanceFilter() {
				this.distance = 20; // Reset slider value
				this.appliedDistance = 20; // Reset applied value
				this.applyDistanceFilter(); // Apply reset immediately
			},
			
			// 分类筛选相关方法
			applyCategoryFilter() {
				this.fetchFilteredData(this.buildFilterParams());
				this.closeFilter();
			},
			
			resetCategoryFilter() {
				this.currentCateId = '';
				this.currentCateName = '分类筛选';
				this.applyCategoryFilter(); // Apply reset immediately
			},
		},
		async onLoad() {
			this.$api.base.getConfig().then(res => {
				this.getconfigs = res.shifuQualityCommitment
			})
		this.$api.shifu.getQuotationCounts().then(res => {
				if(res.code==='-1'){
					uni.showToast({
						icon: 'none',
						title: res.msg
					}, 3000);
				}else{
					this.QuotationCounts=res.data
				}
			
		});
		
			if (uni.getStorageSync('shiInfo')) {
				this.infodata = JSON.parse(uni.getStorageSync('shiInfo'));
			}
			this.isPageLoaded = true;
			await this.initializePage();
		},
		async onPullDownRefresh() {
			this.page = 1;
			this.list = [];
			await this.getListByTab(this.currentTab);
			uni.stopPullDownRefresh();
		},
		async onShow() {
			// Ensure shifu status is checked on show if not already done, or if it needs to be refreshed
			this.$api.shifu.getshifstutas({
					userId:uni.getStorageSync('userId')
				}).then(res => {
						console.log(res)
				this.shifustutus = res;
			});

			// Re-fetch list if refresh signal is received (e.g., from another page after an order action)
			uni.$on('refreshReceivingList', () => {
				this.page = 1;
				this.list = [];
				this.getListByTab(this.currentTab);
			});
		},
		onHide() {
			uni.$off('refreshReceivingList'); // Unregister event listener when page hides
		},
		onUnload() {
			uni.$off('refreshReceivingList'); // Unregister event listener when page unloads
		}
	};
</script>

<style scoped lang="scss">
	.page {
		min-height: 100vh;
		overflow: auto;
		background-color: #f3f4f5;
		padding-bottom: 120rpx;

		.img {
			width: 690rpx;
			margin: 20rpx auto;
		}

		.location-bar {
			display: flex;
			padding: 20rpx;
			border: 1rpx solid #eeeeee;
			color: #999;
			font-size: 28rpx;
		}

		.subscription {
			flex-shrink: 0;
		}

		.location-info {
			margin-left: auto;
			text-align: right;
		}

		// 新增的选项卡样式
		.tabs-container {
			background-color: #fff;
			margin: 0 20rpx 20rpx 20rpx;
			border-radius: 10rpx;
			overflow: hidden;
		}

		.custom-tabs {
			display: flex;
			background-color: #fff;
			border-bottom: 1rpx solid #f0f0f0;

			.tab-item {
				flex: 1;
				padding: 25rpx 10rpx;
				text-align: center;
				position: relative;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.tab-text {
					font-size: 28rpx;
					color: #666;
					font-weight: 400;
					transition: color 0.3s ease;
				}

				.tab-badge {
					position: absolute;
					top: 10rpx;
					right: 15rpx;
					background-color: #ff4757;
					color: #fff;
					font-size: 20rpx;
					padding: 2rpx 8rpx;
					border-radius: 12rpx;
					min-width: 20rpx;
					height: 24rpx;
					line-height: 20rpx;
					text-align: center;
				}

				&.active {
					.tab-text {
						color: #2E80FE;
						font-weight: 600;
					}
					
					&::after {
						content: '';
						position: absolute;
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						width: 60rpx;
						height: 4rpx;
						background-color: #2E80FE;
						border-radius: 2rpx;
					}
				}
			}
		}

		.filter-container {
			display: flex;
			flex-direction: column;
			background-color: #fff;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #eee;
			position: relative;
			z-index: 10;
		}

		.filter-bar {
			display: flex;
			background-color: #fff;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #eee;
			position: relative;
			z-index: 10;
		}

		.filter-item-container {
			flex: 1;
			position: relative;
		}

		.filter-item {
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			color: #333;
			height: 100%;
			&.active-filter-item {
				color: #2E80FE;
				font-weight: 600;
			}
		}

		.filter-item text {
			margin-right: 10rpx;
		}

		.arrow {
			font-size: 24rpx;
			color: #999;
			transition: transform 0.3s ease;
		}

		.arrow.rotate {
			transform: rotate(180deg);
		}

		.filter-dropdown {
			position: absolute;
			top: 100%;
			left: 0;
			width: 100%;
			max-width: none;
			transform: none;
			background-color: #fff;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
			z-index: 999;
			border-radius: 0 0 12rpx 12rpx;
		}

		.dropdown-content {
			padding: 30rpx;
			max-height: 80vh;
			overflow-y: auto;
		}

		.filter-section {
			margin-bottom: 30rpx;
		}

		.section-title {
			font-size: 28rpx;
			color: #333;
			font-weight: 500;
			margin-bottom: 20rpx;
		}

		.option-list {
			display: flex;
			flex-wrap: wrap;
		}

		.option-item {
			padding: 15rpx 30rpx;
			background-color: #f5f5f5;
			border-radius: 8rpx;
			margin-right: 20rpx;
			margin-bottom: 20rpx;
			font-size: 26rpx;
			color: #666;
		}

		.option-item.active {
			background-color: #2E80FE;
			color: #fff;
			border: 1px solid #2E80FE;
		}

		.custom-price-inputs {
			display: flex;
			align-items: center;
		}

		.custom-price-inputs input {
			flex: 1;
			height: 70rpx;
			border: 1rpx solid #eee;
			border-radius: 8rpx;
			padding: 0 20rpx;
			font-size: 26rpx;
			background-color: #f5f5f5;
		}

		.custom-price-inputs text {
			margin: 0 20rpx;
			color: #999;
		}

		.distance-slider {
			padding: 20rpx 0;
		}

		.distance-slider text {
			font-size: 26rpx;
			color: #666;
			display: block;
			margin-bottom: 20rpx;
		}

		.distance-labels {
			display: flex;
			justify-content: space-between;
			font-size: 24rpx;
			color: #999;
			margin-top: 10rpx;
		}

		.filter-actions {
			display: flex;
			padding: 20rpx 0;
			border-top: 1rpx solid #f0f0f0;
			margin-top: 20rpx;
		}

		.filter-btn {
			flex: 1;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			border-radius: 8rpx;
			font-size: 28rpx;
		}

		.filter-btn.reset {
			background-color: #fff;
			color: #666;
			border: 1rpx solid #ccc;
		}

		.filter-btn.confirm {
			background-color: #2E80FE;
			color: #fff;
			margin-left: 20rpx;
		}
		
		.quotation-counts {
			margin: 20rpx;
			padding: 20rpx;
			background-color: #fff;
			border-radius: 10rpx;
			font-size: 28rpx;
			color: #333;
			line-height: 1.6;
		}

		.check_box {
			margin: 20rpx auto;
			width: 690rpx;
			background-color: #fff;
			border-radius: 10rpx;
			overflow: hidden;
			
			.collapse-container {
				width: 100%;
			}
			
			.collapse-item {
				border-bottom: 1rpx solid #eee;
			}
			
			.collapse-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 25rpx 30rpx;
				font-size: 30rpx;
				color: #333;
				background-color: #fff;
			}
			
			.collapse-title {
				font-weight: 500;
			}
			
			.collapse-icon {
				font-size: 24rpx;
				color: #999;
				transition: transform 0.3s ease;
				
				&.rotate {
					transform: rotate(180deg);
				}
			}
			
			.collapse-content {
				padding: 20rpx 30rpx;
				background-color: #f8f8f8;
			}
			
			.price-range, .distance-range, .cate-list {
				width: 100%;
			}
			
			.price-options {
				display: flex;
				flex-wrap: wrap;
				margin-bottom: 20rpx;
				
				.price-option {
					width: calc(50% - 20rpx);
					margin: 10rpx;
					padding: 15rpx 0;
					text-align: center;
					background-color: #fff;
					border-radius: 8rpx;
					font-size: 28rpx;
					
					&.active {
						background-color: #2E80FE;
						color: #fff;
					}
				}
			}
			
			.custom-price {
				margin-top: 20rpx;
				
				.custom-price-title {
					font-size: 28rpx;
					margin-bottom: 15rpx;
				}
				
				.custom-price-inputs {
					display: flex;
					align-items: center;
					
					input {
						flex: 1;
						height: 70rpx;
						border: 1rpx solid #eee;
						border-radius: 8rpx;
						padding: 0 20rpx;
						font-size: 28rpx;
						background-color: #fff;
					}
					
					text {
						margin: 0 20rpx;
					}
				}
			}
			
			.distance-slider {
				padding: 20rpx 0;
				
				text {
					font-size: 28rpx;
					display: block;
					margin-bottom: 20rpx;
				}
				
				.distance-labels {
					display: flex;
					justify-content: space-between;
					font-size: 24rpx;
					color: #999;
					margin-top: 10rpx;
				}
			}
			
			.cate-list {
				display: flex;
				flex-wrap: wrap;
				
				.cate-item {
					width: calc(33.33% - 20rpx);
					margin: 10rpx;
					padding: 15rpx 0;
					text-align: center;
					background-color: #fff;
					border-radius: 8rpx;
					font-size: 28rpx;
					
					&.active {
						background-color: #2E80FE;
						color: #fff;
					}
				}
			}
			
			.filter-actions {
				display: flex;
				padding: 20rpx 0;
				
				.filter-btn {
					flex: 1;
					height: 70rpx;
					line-height: 70rpx;
					text-align: center;
					border-radius: 8rpx;
					font-size: 28rpx;
					margin: 0 10rpx;
					
					&.primary {
						background-color: #2E80FE;
						color: #fff;
					}
					
					&.plain {
						background-color: #fff;
						color: #2E80FE;
						border: 1rpx solid #2E80FE;
					}
				}
			}
		}

		.modal-content {
			padding: 20rpx;
			max-height: 400rpx;
			overflow-y: auto;
		}

		.re_item {
			width: 690rpx;
			background-color: #fff;
			margin: 20rpx auto;
			padding: 40rpx;
			border-radius: 10rpx; // Added border-radius for consistency

			.top {
				display: flex;

				image {
					margin-right: 20rpx;
				}

				.order {
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					flex: 1; // Allows order content to take remaining space

					.title {
						font-size: 28rpx;
						font-weight: 500;
						color: #171717;
						max-width: 100%; // Ensure title doesn't overflow its parent
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}

					.price {
						font-size: 28rpx;
						font-weight: 500;
						color: #2E80FE;
					}
				}
			}

			.info {
				margin-top: 40rpx;

				.address, .tel {
					display: flex;
					align-items: center;
					margin-bottom: 20rpx; // Spacing between address and tel
					&:last-child {
						margin-bottom: 0; // No margin for the last one
					}

					.left {
						margin-right: 20rpx;
						flex-shrink: 0; // Prevent icon from shrinking
					}

					.right {
						flex: 1; // Allows address/tel info to take remaining space
						.address_name {
							font-size: 32rpx; // Adjusted font size for better readability
							font-weight: 500;
							color: #333333;
							max-width: 100%;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}

						.address_Info {
							margin-top: 8rpx; // Adjusted spacing
							font-size: 26rpx; // Adjusted font size
							font-weight: 400;
							color: #666666; // Slightly lighter color for info
							max-width: 100%;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
					}
				}
				.tel .right {
					font-size: 32rpx; // Consistent font size with address_name
					font-weight: 500;
					color: #333333;
					max-width: 100%;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}

			.notes {
				background-color: #f2f3f4;
				border-radius: 5rpx;
				padding: 15rpx; // Increased padding
				margin-top: 20rpx; // Added margin top
				font-size: 26rpx; // Added font size
				line-height: 1.5; // Added line height

				view {
					color:#999999;
					margin-bottom: 5rpx;
				}
			}

			.btn {
				margin: 40rpx auto 0; // Adjusted margin
				width: 100%; // Full width within item padding
				height: 82rpx;
				border-radius: 12rpx;
				border: 2rpx solid #2E80FE;
				line-height: 82rpx;
				text-align: center;
				font-size: 32rpx;
				font-weight: 500;
				color: #2E80FE;
			}
		}

		.loadmore {
			display: flex;
			justify-content: center;
			padding: 20rpx 0; // Added padding
		}

		.footer {
			color: #333;
			margin: 20rpx 0;
			text-align: center;
			font-size: 24rpx;
		}
	}
</style>
```