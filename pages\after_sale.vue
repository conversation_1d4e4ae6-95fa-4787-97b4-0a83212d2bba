<template>
	<view class="page">
		<u-datetime-picker :show="showTime" v-model="form.time" mode="datetime" :formatter="formatter"
			ref="datetimePicker" @confirm="confirmTime" @cancel="cancelTime"></u-datetime-picker>
		<view class="form_item">
			<view class="left"><span>*</span>报修品牌</view>
			<input type="text" placeholder="请输入报修品牌" v-model="form.brand">
		</view>
		<view class="form_item">
			<view class="left"><span>*</span>报修名称</view>
			<input type="text" placeholder="请输入报修名称" v-model="form.title">
		</view>
		<view class="form_item">
			<view class="left"><span>*</span>联系人姓名</view>
			<input type="text" placeholder="请输入联系人姓名" v-model="form.name">
		</view>
		<view class="form_item">
			<view class="left"><span>*</span>联系人电话</view>
			<input type="text" placeholder="请输入联系人电话" v-model="form.tel">
		</view>
		<view class="form_item">
			<view class="left"><span>*</span>上门地址</view>
			<input type="text" placeholder="请输入上门地址" v-model="form.address">
		</view>
		<view class="form_item">
			<view class="left">上门时间</view>
			<input type="text" placeholder="请选择上门时间" @click="getFocus" v-model="form.time" disabled>
		</view>
		<view class="notes">
			<view class="left">备注</view>
			<textarea  v-model="form.mark"></textarea>
		</view>
		<view class="footer">
			<view class="btn" @click="submit">立即提交</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				showTime: false,
				form:{
					brand:'',
					title:'',
					name:"",
					tel:'',
					address:'',
					time:'',
					mark:''
				}
			}
		},
		methods: {
			submit(){
				for (let key in this.form) {
					if (this.form[key] === '' && key !== 'mark' && key !=='time') {
						uni.showToast({
							icon: 'none',
							title: '请填写完整提交',
							duration: 1500
						})
						return
					}
				}
				let phoneReg = /^1[3456789]\d{9}$/
				if (!phoneReg.test(this.form.tel)) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的手机号',
						duration: 1500
					})
					return
				}
				let subForm = JSON.parse(JSON.stringify(this.form))
				this.$api.mine.subBxList(this.form).then(res=>{
					uni.showToast({
						icon:'success',
						title:'提交成功',
						duration:1000
					})
					setTimeout(()=>{
						uni.navigateTo({
							url:'/pages/repair_record'
						})
						this.form = {
							brand:'',
							title:'',
							name:"",
							tel:'',
							address:'',
							time:'',
							mark:''
						}
					},1000)
				})
			},
			cancelTime() {
				this.showTime = false
			},
			confirmTime() {
				this.showTime = false
				console.log(this.form.time);
			},
			getFocus() {
				this.showTime = true
			},
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				return value
			},
		},
		onReady() {
			this.$refs.datetimePicker.setFormatter(this.formatter)
		},
		watch: {
			"form.time": {
				handler(nval) {
					this.form.time = this.$util.timestampToTime(nval)
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		padding: 0 30rpx;

		.form_item {
			height: 120rpx;
			border-bottom: 2rpx solid #E9E9E9;
			display: flex;
			align-items: center;

			.left {
				width: 190rpx;
				font-size: 28rpx;
				font-weight: 500;
				color: #333333;

				span {
					color: red;
				}
			}
		}
		.notes{
			padding-top: 42rpx;
			.left{
				font-size: 28rpx;
				font-weight: 500;
				color: #333333;
			}
			textarea{
				box-sizing: border-box;
				padding: 40rpx 30rpx;
				margin-top: 40rpx;
				width: 686rpx;
				height: 242rpx;
				background: #F7F7F7;
				border-radius: 20rpx 20rpx 20rpx 20rpx;
				opacity: 1;
			}
		}
		.footer{
			box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193,193,193,0.3);
			height: 192rpx;
			width: 750rpx;
			position: fixed;
			bottom: 0;
			left: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			.btn{
				width: 690rpx;
				height: 98rpx;
				background: #2E80FE;
				border-radius: 50rpx 50rpx 50rpx 50rpx;
				font-size: 32rpx;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 98rpx;
				text-align: center;
			}
		}
	}
</style>