<template>
  <view class="page">
    <view class="header">
      <view class="head">
        <view class="left">
          <image :src="userInfo.avatarUrl ? userInfo.avatarUrl : '/static/mine/default_user.png'" mode=""></image>
          <view class="name">{{ userInfo.nickName || '' }}</view>
        </view>
        <view class="right">已邀请{{ total }}</view>
      </view>
    </view>
    <view class="fg"></view>
    <view class="box">
      <view class="title">我邀请的</view>
      <view class="list">
        <view style="display: flex; justify-content: space-between; align-items: center;" class="list_item" v-for="(item, index) in list" :key="index">
        <view style="display: flex; justify-content: space-between; align-items: center;" class="">
        	<image :src="item.avatarUrl ? item.avatarUrl : '/static/mine/default_user.png'" mode=""></image>
        	<view class="info">
        	  <view class="nam">{{ item.nickName }}</view>
        	  <view class="nam">{{ item.phone }}</view>
        	</view>
        </view>
		<view>
			<view  class="">
			  <view style="display: flex; justify-content: center; align-items: center;" class="">
			  	  {{item.shifu===0?"用户":"师傅"}}
			  </view>
			  <view style="font-size: 24rpx;" class="">
			  	  {{item.createTime}}
			  </view>
			</view>
		</view>
        </view>
      </view>
      <!-- Optional: Load More Button -->
      <view v-if="list.length < total" class="load-more" @click="loadMore">加载更多</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      page: 1,
      userInfo: {},
      list: [],
      total: 0, // Initialize as 0
      limit: 10,
      loading: false // Prevent multiple simultaneous requests
    };
  },
  onPullDownRefresh() {
    console.log('refresh');
    // Reset to first page on pull-down refresh
    this.page = 1;
    this.list = [];
    this.getList();
    setTimeout(() => {
      uni.stopPullDownRefresh();
    }, 1000);
  },
  onReachBottom() {
    // Triggered when user scrolls to the bottom
    if (this.list.length < this.total && !this.loading) {
      this.page += 1;
      this.getList();
    }
  },
  methods: {
    getUserInfo() {
      this.$api.user.userInfo().then(res => {
        this.userInfo = res;
      });
    },
    getList() {
      if (this.loading) return; // Prevent multiple requests
      this.loading = true;
      this.$api.service
        .getMyTeam({
          pageNum: this.page,
          pageSize: this.limit
        })
        .then(res => {
          this.total = res.totalCount;
          // Append new items to the list
          this.list = this.page === 1 ? res.list : [...this.list, ...res.list];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    loadMore() {
      // Optional: Manual load more button
      if (this.list.length < this.total && !this.loading) {
        this.page += 1;
        this.getList();
      }
    }
  },
  mounted() {
    this.getUserInfo();
    this.getList();
  }
};
</script>

<style scoped lang="scss">
.page {
  .header {
    padding: 40rpx 30rpx;

    .head {
      width: 690rpx;
      height: 186rpx;
      background: #2e80fe;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30rpx;

      .left {
        display: flex;
        align-items: center;

        image {
          width: 106rpx;
          height: 106rpx;
          border-radius: 50%;
        }

        .name {
          margin-left: 20rpx;
          font-size: 32rpx;
          font-weight: 500;
          color: #ffffff;
          max-width: 240rpx;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      .right {
        width: fit-content;
        height: 74rpx;
        background: #81b3ff;
        border-radius: 12rpx;
        line-height: 74rpx;
        text-align: center;
        padding: 0 14rpx;
        font-size: 32rpx;
        font-weight: 500;
        color: #ffffff;
      }
    }
  }

  .fg {
    background: #f3f4f5;
    width: 100%;
    height: 20rpx;
  }

  .box {
    padding: 40rpx 30rpx;

    .title {
      font-size: 32rpx;
      font-weight: 500;
      color: #171717;
    }

    .list {
      margin-top: 42rpx;

      .list_item {
        display: flex;
        margin-bottom: 20rpx;

        image {
          width: 104rpx;
          height: 104rpx;
          border-radius: 50%;
        }

        .info {
          margin-left: 20rpx;

          .nam {
            font-size: 28rpx;
            font-weight: 400;
            color: #171717;
            max-width: 480rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .phone {
            margin-top: 20rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #999999;
          }
        }
      }
    }

    .load-more {
      text-align: center;
      padding: 20rpx;
      font-size: 28rpx;
      color: #2e80fe;
      cursor: pointer;
    }
  }
}
</style>