<template>
	<view class="page">
		<view class="header">
			<view class="left">提现至</view>
			<view class="right">微信</view>
		</view>
		<view class="mid">
			<view class="title">提现金额</view>
			<view class="top">
				<view class="t_left">
					<u--input placeholder="请输入提现金额" type="number" border="none" v-model="money" @change="change"
						prefixIcon="rmb"></u--input>
				</view>
				<view class="r_left" @tap="goAll">全部提现</view>
			</view>
			<view class="bottom">可提现金额￥{{ allmoney }}</view>
		</view>
		<view class="btn" @tap="debounceConfirmTx" :disabled="isSubmitting">确认提现</view>
		<text class="tips">温馨提示：提现申请发起后，预计3个工作日内到账。</text>
		<text class="contact">有问题请联系客服 <text class="phone" @tap="copyPhoneNumber">4008326986</text></text>
		<u-modal :show="showNameIdModal" title="请完善实名信息以确保提现安全" confirmText="保存" showCancelButton @confirm="saveNameIdInfo"
			@cancel="showNameIdModal = false"
			:contentStyle="{ padding: '40rpx', background: '#ffffff', borderRadius: '16rpx' }">
			<view class="slot-content">
				<view class="main_item">
					<view class="title"><span>*</span>姓名</view>
					<input type="text" v-model="tempForm.coachName" placeholder="请输入姓名" class="modal-input" />
				</view>
				<view class="main_item">
					<view class="title"><span>*</span>身份证号</view>
					<input type="text" v-model="tempForm.idCode" placeholder="请输入身份证号" class="modal-input" />
				</view>
				<view class="main_item">
					<view class="title"><span>*</span>上传身份证照片</view>
					<view class="card">
						<view class="card_item">
							<view class="top">
								<view class="das">
									<view class="up">
										<upload @upload="imgUploadTemp" @del="imgUploadTemp"
											:imagelist="tempForm.id_card1" imgtype="id_card1" imgclass="id_card_box"
											text="身份证人像面" :imgsize="1"></upload>
									</view>
								</view>
							</view>
							<view class="bottom">拍摄人像面</view>
						</view>
						<view class="card_item">
							<view class="top">
								<view class="das">
									<view class="up">
										<upload @upload="imgUploadTemp" @del="imgUploadTemp"
											:imagelist="tempForm.id_card2" imgtype="id_card2" imgclass="id_card_box"
											text="身份证国徽面" :imgsize="1"></upload>
									</view>
								</view>
							</view>
							<view class="bottom">拍摄国徽面</view>
						</view>
					</view>
				</view>
			</view>
		</u-modal>
	</view>

</template>

<script>
	export default {
		data() {
			return {
				money: '',
				allmoney: '0',
				tempForm: { // Temporary form for name/ID input
					coachName: '',
					idCode: '',
					id_card1: [],
					id_card2: [],
				},
				isSubmitting: false,
				showNameIdModal: false, // New state for the name/ID modal
				mchId: '1648027588', // Replace with your actual Merchant ID
				debounceConfirmTx: null, // 用于存储防抖函数
			};
		},
		onLoad() {
			this.getMoney();
			// 初始化防抖函数
			this.debounceConfirmTx = this.debounce(this.confirmTx.bind(this), 1000); // 1秒内只允许触发一次
		},
		methods: {
			imgUploadTemp(e) {
				console.log('imgUploadTemp event:', e);
				const { imagelist, imgtype } = e;
				this.$set(this.tempForm, imgtype, imagelist);
			},
			async saveNameIdInfo() {
				const {
					coachName,
					idCode,
					id_card1,
					id_card2
				} = this.tempForm;

				if (!coachName || !idCode || id_card1.length === 0 || id_card2.length === 0) {
					uni.showToast({
						icon: 'none',
						title: '请填写所有必填项并上传照片'
					});
					return;
				}

				let p = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
				if (!p.test(idCode)) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的身份证号',
					});
					return;
				}
				let shifuid = JSON.parse(uni.getStorageSync('shiInfo'))
				let userId = (uni.getStorageSync('userId'))
				console.log(shifuid)
				console.log(userId)
				// Construct the payload for saving name and ID card information
				const payload = {
					coachName: coachName,
					idCode: idCode,
					id: shifuid.id,
					userId: userId,
					idCard: [id_card1[0].path, id_card2[0]
					.path], // Assuming imgsize is 1, so only one image per type
				};

				try {
					const res = await this.$api.shifu.updataInfoSF(payload); // Replace with your actual API call
					console.log(res)
					if (res.code === "200") { // Assuming "0" means success
						uni.showToast({
							icon: 'success',
							title: '身份信息保存成功',
						});
						this.showNameIdModal = false;
						// You might want to re-attempt the quote submission or refresh data here
						// For now, let's just close the modal.
					} else {
						uni.showToast({
							icon: 'none',
							title: res.msg || '身份信息保存失败'
						});
					}
				} catch (error) {
					uni.showToast({
						icon: 'error',
						title: error.message || '身份信息保存失败'
					});
				}
			},
			// 防抖函数
			debounce(fn, delay) {
				let timer = null;
				return function(...args) {
					if (timer) clearTimeout(timer);
					timer = setTimeout(() => {
						fn.apply(this, args);
						timer = null;
					}, delay);
				};
			},
			async confirmTx() {
				if (this.isSubmitting) return; // 防止重复点击
				this.isSubmitting = true; // 立即禁用按钮

				const amount = Number(this.money);
				if (!amount || amount <= 0) {
					uni.showToast({
						title: '请输入有效的提现金额',
						icon: 'none',
					});
					this.isSubmitting = false;
					return;
				}
				if (amount > Number(this.allmoney)) {
					uni.showToast({
						title: '超过可提现金额',
						icon: 'none',
					});
					this.isSubmitting = false;
					return;
				}
				if (amount > 800) {
					uni.showToast({
						title: '最高提现金额为799元',
						icon: 'none',
					});
					return;
				}
				if (amount < 1) {
					uni.showToast({
						title: '最低提现金额为1元',
						icon: 'none',
					});
					this.isSubmitting = false;
					return;
				}

				// Show confirmation modal before proceeding
				uni.showModal({
					title: '确认提现',
					content: '为确保您的账户余额准确无误，提现操作一旦提交，请不要中途退出或刷新页面，若您在提现过程中中止操作，可能会导致余额错误，需等待1-3个工作日处理您的请求。',
					confirmText: '确定',
					cancelText: '取消',
					success: async (res) => {
						if (res.confirm) {
							// Proceed with withdrawal only if user confirms
							if (!uni.canIUse('requestMerchantTransfer')) {
								uni.showModal({
									content: '你的微信版本过低，请更新至最新版本。',
									showCancel: false,
								});
								this.isSubmitting = false;
								return;
							}

							try {
								// Request signed package from backend
								const res = await this.$api.mine.applyWallet({
									amount: this.money,
									type: 2
								});
								console.log(res)
								if (res.data === -5) {
									uni.showToast({
										icon: 'none',
										title: res.msg
									});
									this.showNameIdModal = true; // Show the new modal
									return;
								}
								if (!res.data.packageInfo) {
									uni.showToast({
										title: res.msg || '无法生成提现请求',
										icon: 'none',
									});
									this.isSubmitting = false;
									return;
								}
							if (res.code === "-1") {
								uni.showToast({
									icon: 'none',
									title: res.msg
								});
							this.isSubmitting = false;
								return;
							}
								// Initiate WeChat transfer
								uni.requestMerchantTransfer({
									mchId: res.data.mchId,
									appId: res.data.appId,
									package: res.data.packageInfo,
									success: (transferRes) => {
										if (transferRes.result === 'success') {
											uni.showToast({
												icon: 'success',
												title: '提现申请提交成功',
											});
											setTimeout(() => {
												uni.navigateTo({
													url: '/shifu/income'
												});
											}, 1000);
										} else {
											uni.showToast({
												title: '提现申请失败，请稍后重试',
												icon: 'none',
											});
										}
									},
									fail: (transferRes) => {
										uni.showToast({
											title: transferRes.errMsg || '提现失败，请稍后重试',
											icon: 'none',
										});
									},
									complete: () => {
										this.isSubmitting = false;
									},
								});
							} catch (error) {
								uni.showToast({
									title: '请稍后重试',
									icon: 'none',
								});
								this.isSubmitting = false;
							}
						} else {
							this.isSubmitting = false; // 用户取消时恢复按钮状态
						}
					},
				});
			},
			goAll() {
				this.money = this.allmoney;
			},
			change(e) {
				// Handle input change if needed
			},
			async getMoney() {
				try {
					const res = await this.$api.shifu.coachCash();
					this.allmoney = res.servicePrice || '0';
					this.money = this.allmoney;
				} catch (error) {
					uni.showToast({
						title: '获取可提现金额失败',
						icon: 'none',
					});
				}
			},
			copyPhoneNumber() {
				uni.setClipboardData({
					data: '4008326986',
					success: () => {
						uni.showToast({
							title: '客服电话已复制',
							icon: 'success',
						});
					},
					fail: () => {
						uni.showToast({
							title: '复制失败，请稍后重试',
							icon: 'none',
						});
					}
				});
			},
		},
	};
</script>

<style scoped lang="scss">
	.page {
		background-color: #f8f8f8;
		min-height: 100vh;
		padding: 20rpx 0;

		.header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 28rpx;
			font-weight: 500;
			color: #3b3b3b;
			padding: 0 30rpx;
			width: 750rpx;
			height: 118rpx;
			background: #ffffff;

			.right {
				display: flex;
				align-items: center;
			}
		}

		.mid {
			margin-top: 20rpx;
			width: 750rpx;
			height: 276rpx;
			background: #ffffff;
			padding: 0 30rpx;
			padding-top: 40rpx;

			.title {
				font-size: 28rpx;
				font-weight: 500;
				color: #3b3b3b;
			}

			.top {
				display: flex;
				align-items: flex-end;
				justify-content: space-between;
				padding-top: 28rpx;
				padding-bottom: 20rpx;
				border-bottom: 2rpx solid #f2f3f6;

				.r_left {
					font-size: 28rpx;
					font-weight: 500;
					color: #e51837;
				}
			}

			.bottom {
				padding-top: 20rpx;
				font-size: 24rpx;
				font-weight: 500;
				color: #999999;
			}
		}

		.btn {
			margin: 60rpx auto 0;
			width: 690rpx;
			height: 98rpx;
			background: #2e80fe;
			border-radius: 50rpx;
			font-size: 32rpx;
			font-weight: 500;
			color: #ffffff;
			line-height: 98rpx;
			text-align: center;

			&:disabled {
				background: #cccccc;
			}
		}

		.tips {
			display: block;
			font-size: 24rpx;
			font-weight: 500;
			color: #999999;
			text-align: center;
			margin-top: 20rpx;
		}

		.contact {
			display: block;
			font-size: 24rpx;
			font-weight: 500;
			color: #999999;
			text-align: center;
			margin-top: 20rpx;

			.phone {
				color: #2e80fe;
				text-decoration: underline;
			}
		}
	}
	.slot-content {
		padding: 20rpx 0;
	}
	
	.main_item {
		margin-bottom: 32rpx;
		padding: 0 24rpx;
	
		.title {
			margin-bottom: 16rpx;
			font-size: 30rpx;
			font-weight: 500;
			color: #1a1a1a;
			display: flex;
			align-items: center;
	
			span {
				color: #e72427;
				margin-right: 8rpx;
			}
		}
	
		.modal-input {
			width: 100%;
			height: 80rpx;
			background: #f8f8f8;
			border: 1rpx solid #e5e7eb;
			border-radius: 12rpx;
			font-size: 28rpx;
			font-weight: 400;
			line-height: 80rpx;
			padding: 0 24rpx;
			box-sizing: border-box;
			transition: all 0.2s ease-in-out;
	
			&:focus {
				border-color: #2e80fe;
				background: #ffffff;
				box-shadow: 0 0 8rpx rgba(46, 128, 254, 0.2);
			}
	
			&:disabled {
				background: #f0f0f0;
				color: #999;
				cursor: not-allowed;
			}
		}
	
		.card {
			display: flex;
			justify-content: space-between;
			gap: 16rpx;
	
			.card_item {
				width: 48%;
				background: #f2fafe;
				border-radius: 16rpx;
				overflow: hidden;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
				transition: transform 0.2s ease-in-out;
	
				&:hover {
					transform: translateY(-4rpx);
				}
	
				.top {
					height: 180rpx;
					width: 100%;
					padding-top: 20rpx;
	
					.das {
						margin: 0 auto;
						width: 85%;
						height: 120rpx;
						border: 2rpx dashed #2e80fe;
						border-radius: 8rpx;
						padding: 10rpx;
						display: flex;
						align-items: center;
						justify-content: center;
	
						.up {
							width: 100%;
							height: 100%;
							display: flex;
							align-items: center;
							justify-content: center;
						}
					}
				}
	
				.bottom {
					height: 60rpx;
					width: 100%;
					background: linear-gradient(90deg, #2e80fe 0%, #5ba0ff 100%);
					font-size: 24rpx;
					font-weight: 500;
					color: #ffffff;
					text-align: center;
					line-height: 60rpx;
				}
			}
		}}
</style>