!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Painter={})}(this,(function(t){"use strict";var e=function(){return e=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var o in e=arguments[i])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},e.apply(this,arguments)};function i(t,e,i,n){return new(i||(i=Promise))((function(o,r){function s(t){try{h(n.next(t))}catch(t){r(t)}}function a(t){try{h(n.throw(t))}catch(t){r(t)}}function h(t){var e;t.done?o(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(s,a)}h((n=n.apply(t,e||[])).next())}))}function n(t,e){var i,n,o,r,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(r){return function(a){return function(r){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,n&&(o=2&r[0]?n.return:r[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,r[1])).done)return o;switch(n=0,o&&(r=[2&r[0],o.value]),r[0]){case 0:case 1:o=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,n=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(!(o=s.trys,(o=o.length>0&&o[o.length-1])||6!==r[0]&&2!==r[0])){s=0;continue}if(3===r[0]&&(!o||r[1]>o[0]&&r[1]<o[3])){s.label=r[1];break}if(6===r[0]&&s.label<o[1]){s.label=o[1],o=r;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(r);break}o[2]&&s.ops.pop(),s.trys.pop();continue}r=e.call(t,s)}catch(t){r=[6,t],n=0}finally{i=o=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,a])}}}var o,r,s={MP_WEIXIN:"mp-weixin",MP_QQ:"mp-qq",MP_ALIPAY:"mp-alipay",MP_BAIDU:"mp-baidu",MP_TOUTIAO:"mp-toutiao",MP_DINGDING:"mp-dingding",H5:"h5",WEB:"web",PLUS:"plus"},a=["contentSize","clientSize","borderSize","offsetSize"],h="row",d="column",c="top",l="middle",f="bottom",u="left",p="center",g="right",v="view",y="text",b="image",x="qrcode",m="block",w="inline-block",S="none",z="flex",M="absolute",B="fixed",I="transparent",k="fill",P={display:m,color:"#000000",lineHeight:"1.4em",fontSize:14,fontWeight:400,fontFamily:"sans-serif",lineCap:"butt",flexDirection:h,flexWrap:"nowrap",textAlign:"left",alignItems:"flex-start",justifyContent:"flex-start",position:"static",transformOrigin:"center center"},W={upx2px:function(t){return window.innerWidth/750*t},getSystemInfoSync:function(){return{screenWidth:window.innerWidth}},getImageInfo:function(t){var e=t.src,i=t.success,n=t.fail,o=new Image;o.onload=function(){i({width:this.naturalWidth,height:this.naturalHeight,path:this.src,src:e})},o.onerror=n,o.src=e}},R="object",O="undefined",T=typeof window==R?typeof uni==O||typeof uni!==O&&!uni.addInterceptor?s.WEB:s.H5:typeof swan==R?s.MP_BAIDU:typeof tt==R?s.MP_TOUTIAO:typeof plus===R?s.PLUS:typeof wx==R?s.MP_WEIXIN:void 0,L=T==s.MP_WEIXIN?wx:typeof uni!=O?uni.getImageInfo?{upx2px:function(t){return uni.upx2px(t)},getSystemInfoSync:function(){return uni.getSystemInfoSync()},getImageInfo:function(t){return uni.getImageInfo(t)},downloadFile:function(t){return uni.downloadFile(t)}}:Object.assign(uni,W):typeof window!=O?W:uni;if(!L.upx2px){var A=(null!==(r=L.getSystemInfoSync&&(null===(o=uni.getSystemInfoSync())||void 0===o?void 0:o.screenWidth))&&void 0!==r?r:375)/750;L.upx2px=function(t){return A*t}}function F(t){return/^-?\d+(\.\d+)?$/.test(t)}function j(t,e,i){if("number"==typeof t)return t;if(F(t))return 1*t;if("string"==typeof t){var n=/^-?([0-9]+)?([.]{1}[0-9]+){0,1}(em|rpx|px|%)$/g.exec(t);if(!t||!n)return 0;var o=n[3];t=parseFloat(t);var r=0;return"rpx"===o?r=L.upx2px(t):"px"===o?r=1*t:"%"===o&&e?r=t*j(e)/100:"em"===o&&e&&(r=t*j(e||14)),1*r.toFixed(2)}return 0}function C(t){return/%$/.test(t)}var E=function(t){return!(!t||!t.startsWith("linear")&&!t.startsWith("radial"))},H=function(t,e,i,n,o,r){t.startsWith("linear")?function(t,e,i,n,o,r){for(var s=function(t,e,i,n,o){void 0===n&&(n=0);void 0===o&&(o=0);var r=t.match(/([-]?\d{1,3})deg/),s=r&&r[1]?parseFloat(r[1]):0;s>=360&&(s-=360);s<0&&(s+=360);if(0===(s=Math.round(s)))return{x0:Math.round(e/2)+n,y0:i+o,x1:Math.round(e/2)+n,y1:o};if(180===s)return{x0:Math.round(e/2)+n,y0:o,x1:Math.round(e/2)+n,y1:i+o};if(90===s)return{x0:n,y0:Math.round(i/2)+o,x1:e+n,y1:Math.round(i/2)+o};if(270===s)return{x0:e+n,y0:Math.round(i/2)+o,x1:n,y1:Math.round(i/2)+o};var a=Math.round(180*Math.asin(e/Math.sqrt(Math.pow(e,2)+Math.pow(i,2)))/Math.PI);if(s===a)return{x0:n,y0:i+o,x1:e+n,y1:o};if(s===180-a)return{x0:n,y0:o,x1:e+n,y1:i+o};if(s===180+a)return{x0:e+n,y0:o,x1:n,y1:i+o};if(s===360-a)return{x0:e+n,y0:i+o,x1:n,y1:o};var h=0,d=0,c=0,l=0;if(s<a||s>180-a&&s<180||s>180&&s<180+a||s>360-a){var f=s*Math.PI/180,u=s<a||s>360-a?i/2:-i/2,p=Math.tan(f)*u,g=s<a||s>180-a&&s<180?e/2-p:-e/2-p;h=-(c=p+(v=Math.pow(Math.sin(f),2)*g)),d=-(l=u+v/Math.tan(f))}if(s>a&&s<90||s>90&&s<90+a||s>180+a&&s<270||s>270&&s<360-a){var v;f=(90-s)*Math.PI/180,p=s>a&&s<90||s>90&&s<90+a?e/2:-e/2,u=Math.tan(f)*p,g=s>a&&s<90||s>270&&s<360-a?i/2-u:-i/2-u;h=-(c=p+(v=Math.pow(Math.sin(f),2)*g)/Math.tan(f)),d=-(l=u+v)}return h=Math.round(h+e/2)+n,d=Math.round(i/2-d)+o,c=Math.round(c+e/2)+n,l=Math.round(i/2-l)+o,{x0:h,y0:d,x1:c,y1:l}}(o,t,e,i,n),a=s.x0,h=s.y0,d=s.x1,c=s.y1,l=r.createLinearGradient(a,h,d,c),f=o.match(/linear-gradient\((.+)\)/)[1],u=U(f.substring(f.indexOf(",")+1)),p=0;p<u.colors.length;p++)l.addColorStop(u.percents[p],u.colors[p]);r.setFillStyle(l)}(e,i,n,o,t,r):t.startsWith("radial")&&function(t,e,i,n,o,r){for(var s=U(o.match(/radial-gradient\((.+)\)/)[1]),a=Math.round(t/2)+i,h=Math.round(e/2)+n,d=r.createRadialGradient(a,h,0,a,h,Math.max(t,e)/2),c=0;c<s.colors.length;c++)d.addColorStop(s.percents[c],s.colors[c]);r.setFillStyle(d)}(e,i,n,o,t,r)};function U(t){for(var e=[],i=[],n=0,o=t.substring(0,t.length-1).split("%,");n<o.length;n++){var r=o[n];e.push(r.substring(0,r.lastIndexOf(" ")).trim()),i.push(r.substring(r.lastIndexOf(" "),r.length)/100)}return{colors:e,percents:i}}function Y(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function $(){return $=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t},$.apply(this,arguments)}function D(t,e){return D=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},D(t,e)}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function X(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(i)return(i=i.call(t)).next.bind(i);if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return _(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function N(t){return"number"==typeof t}function V(t){return"auto"===t||null===t}function G(t){return/%$/.test(t)}var q,J=0,Q=function(){function t(){Y(this,"elements",[]),Y(this,"afterElements",[]),Y(this,"beforeElements",[]),Y(this,"ids",[]),Y(this,"width",0),Y(this,"height",0),Y(this,"top",0),Y(this,"left",0),Y(this,"pre",null),Y(this,"offsetX",0),Y(this,"offsetY",0),J++,this.id=J}var e=t.prototype;return e.fixedBind=function(t,e){void 0===e&&(e=0),this.container=e?t.parent:t.root,this.container.fixedLine=this,this.fixedAdd(t)},e.fixedAdd=function(t){this.elements.push(t);var e=t.computedStyle.zIndex;(void 0===e?0:e)>=0?this.afterElements.push(t):this.beforeElements.push(t),this.refreshLayout()},e.bind=function(t){this.container=t.parent,this.container.line=null,this.container.lines?(this.container.lines.push(this),this.pre=this.getPreLine(),this.top=this.pre.top+this.pre.height,this.left=this.container.contentSize.left):(this.top=this.container.contentSize.top,this.left=this.container.contentSize.left,this.container.lines=[this]),this.isInline=t.isInline(),this.container.line=this,this.outerWidth=t.parent&&t.parent.contentSize.width?t.parent.contentSize.width:1/0,this.add(t)},e.getPreLine=function(){return this.container.lines[this.container.lines.length-2]},e.canIEnter=function(t){return!((100*t.offsetSize.width+100*this.width)/100>this.outerWidth)||(this.closeLine(),!1)},e.closeLine=function(){delete this.container.line},e.add=function(t){this.ids.push(t.id),this.elements.push(t),this.refreshWidthHeight(t)},e.refreshWidthHeight=function(t){t.offsetSize.height>this.height&&(this.height=t.offsetSize.height),this.width+=t.offsetSize.width||0,(this.container.lineMaxWidth||0)<this.width&&(this.container.lineMaxWidth=this.width)},e.refreshXAlign=function(){if(this.isInline){var t=this.container.contentSize.width-this.width,e=this.container.style.textAlign;"center"===e?t/=2:"left"===e&&(t=0),this.offsetX=t}},e.getOffsetY=function(t){if(!t||!t.style)return 0;var e=(t.style||{}).verticalAlign;return"bottom"===e?this.height-t.contentSize.height:"middle"===e?(this.height-t.contentSize.height)/2:0},e.layout=function(t,e){for(var i in this.refreshXAlign(),this.pre?(this.top=this.pre.top+this.pre.height+this.offsetY,this.left=e+this.offsetX):(this.top=Math.max(this.top,this.container.contentSize.top,t)+this.offsetY,this.left=Math.max(this.left,this.container.contentSize.left,e)+this.offsetX),this.elements){var n=this.elements[i],o=this.elements[i-1],r=this.getOffsetY(n);n.style.top=this.top+r,n.style.left=o?o.offsetSize.left+o.offsetSize.width:this.left,n.getBoxPosition2()}},e.refreshLayout=function(){this.afterElements=this.afterElements.sort((function(t,e){return t.computedStyle.zIndex-e.computedStyle.zIndex})),this.beforeElements=this.beforeElements.sort((function(t,e){return t.computedStyle.zIndex-e.computedStyle.zIndex}))},t}(),Z=((q={})[h]={width:"width",contentWidth:"width",x:"x",y:"y",contentX:"left",height:"height",contentHeight:"height"},q[d]={width:"height",contentWidth:"height",x:"y",y:"x",contentX:"top",height:"width",contentHeight:"width"},q),K=function(t){var e,i;function n(){var e;return Y(function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(e=t.call(this)||this),"outerWidth",0),e.exactValue=0,e.flexTotal=0,e.key=null,e}i=t,(e=n).prototype=Object.create(i.prototype),e.prototype.constructor=e,D(e,i);var o=n.prototype;return o.bind=function(t){console.warn("[painter] flex-box 功能未完善谨慎使用"),this.container=t.parent,this.container.line=this,this.container.lines?(this.container.lines.push(this),this.pre=this.getPreLine(),this.top=this.pre.top+this.pre.height,this.left=this.container.contentSize.left):(this.top=this.container.contentSize.top,this.left=this.container.contentSize.left,this.container.lines=[this]),t.parent&&(this.key=Z[t.parent.style.flexDirection]),this.initHeight(t),this.outerWidth=t.parent&&t.parent.contentSize[this.key.contentWidth]?t.parent.contentSize[this.key.contentWidth]:1/0,this.add(t)},o.add=function(t){this.ids.push(t.id),N(t.style.flex)?this.flexTotal+=t.style.flex:N(t.style[this.key.width])&&(this.exactValue+=t.contentSize[this.key.width]),this.elements.push(t),this.refreshWidthHeight(t),t.next||this.closeLine()},o.closeLine=function(){this.calcFlex()},o.initHeight=function(t){this[this.key.height]=0},o.calcFlex=function(){var t=this,e=this.container.contentSize[this.key.contentWidth];this.elements.forEach((function(i){var n=i.style.width||0;N(i.style.flex)&&(n=i.style.flex/t.flexTotal*(e-t.exactValue)),i.computedStyle[t.key.width]=n,i.getBoxWidthHeight()}))},o.refreshWidthHeight=function(t){this.container.style.alignItems&&!t.style.alignSelf&&(t.style.alignSelf=this.container.style.alignItems),t.contentSize[this.key.height]>this[this.key.height]&&(this[this.key.height]=t.offsetSize[this.key.height]),this[this.key.width]+=t.offsetSize[this.key.width],(this.container.lineMaxWidth||0)<this[this.key.width]&&(this.container.lineMaxWidth=this[this.key.width])},o.refreshXAlign=function(){var t=this,e=this.elements.reduce((function(e,i){return e+i.offsetSize[t.key.width]}),0),i=this.outerWidth==1/0?0:this.outerWidth-Math.max(this[this.key.width],e);"center"===this.container.style.justifyContent?i/=2:"flex-start"===this.container.style.justifyContent&&(i=0),this.offsetX=i||0,this.refreshYAlign()},o.refreshYAlign=function(){if(1==this.container.lines.length)return 0;var t=this.container.lines.reduce((function(t,e){return t+e.height}),0);if("center"===this.container.style.alignItems){var e=(this.container.contentSize[this.key.contentHeight]-t)/(this.container.lines.length+1);this.container.lines.forEach((function(t){t.offsetY=e}))}if("flex-end"===this.container.style.alignItems){var i=this.container.contentSize[this.key.contentHeight]-t;this.container.lines[0].offsetY=i}},o.getOffsetY=function(t){return this.container.lines.length>1?0:"flex-end"===t.style.alignSelf?this.container.contentSize[this.key.contentHeight]-t.contentSize[this.key.height]:"center"===t.style.alignSelf?(this.container.contentSize[this.key.contentHeight]-t.contentSize[this.key.height])/2:0},n}(Q),et=b,it=y,nt=v,ot=x,rt=m,st=w,at=z,ht=M,dt=B,ct=0,lt={left:null,top:null,width:null,height:null},ft=function(){function t(t,e,i,n){var o=this;Y(this,"id",ct++),Y(this,"style",{left:null,top:null,width:null,height:null}),Y(this,"computedStyle",{}),Y(this,"originStyle",{}),Y(this,"children",{}),Y(this,"layoutBox",$({},lt)),Y(this,"contentSize",$({},lt)),Y(this,"clientSize",$({},lt)),Y(this,"borderSize",$({},lt)),Y(this,"offsetSize",$({},lt)),this.ctx=n,this.root=i,e&&(this.parent=e),this.name=t.name||t.type,this.attributes=this.getAttributes(t);var r=this.getComputedStyle(t,null==e?void 0:e.computedStyle);this.isAbsolute=r.position==ht,this.isFixed=r.position==dt,this.originStyle=r,Object.keys(r).forEach((function(t){Object.defineProperty(o.style,t,{configurable:!0,enumerable:!0,get:function(){return r[t]},set:function(e){r[t]=e}})}));var s={contentSize:$({},this.contentSize),clientSize:$({},this.clientSize),borderSize:$({},this.borderSize),offsetSize:$({},this.offsetSize)};Object.keys(s).forEach((function(t){Object.keys(o[t]).forEach((function(e){Object.defineProperty(o[t],e,{configurable:!0,enumerable:!0,get:function(){return s[t][e]},set:function(i){s[t][e]=i}})}))})),this.computedStyle=this.style}var e=t.prototype;return e.add=function(t){t.parent=this,this.children[t.id]=t},e.getChildren=function(){var t=this;return Object.keys(this.children).map((function(e){return t.children[e]}))},e.getLineRect=function(t,e){var i={width:0,height:0},n=e?e.lines:this.parent&&this.parent.lines;return n&&n.find((function(e){return e.ids.includes(t)}))||i},e.getComputedStyle=function(t,e){var i=["color","fontSize","lineHeight","verticalAlign","fontWeight","textAlign"],n=t.css,o=void 0===n?{}:n,r=t.type,s=void 0===r?nt:r,a=$({},P);if([it,et,ot].includes(s)&&!o.display&&(a.display=st),e)for(var h=0;h<i.length;h++){var d=i[h];(o[d]||e[d])&&(o[d]=o[d]||e[d])}for(var c=function(){var t=f[l],e=o[t];if(/-/.test(t)&&(t=t.replace(/-([a-z])/g,(function(t,e){return e.toUpperCase()})),a.key=e),/^(box|text)?shadow$/i.test(t)){var i=[];return e.replace(/((\d+(rpx|px)?\s+?){3})(.+)/,(function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];i=e[1].match(/\d+(rpx|px)?/g).map((function(t){return j(t)})).concat(e[4])})),/^text/.test(t)?a.textShadow=i:a.boxShadow=i,"continue"}if(/^border/i.test(t)&&!/radius$/i.test(t)){var n,r=t.match(/^border([BTRLa-z]+)?/)[0],h=t.match(/[W|S|C][a-z]+/),d=e.replace(/([\(,])\s+|\s+([\),])/g,"$1$2").split(" ").map((function(t){return/^\d/.test(t)?j(t,""):t}));return a[r]=((n={})[r+"Width"]=F(d[0])?d[0]:0,n[r+"Style"]=d[1]||"solid",n[r+"Color"]=d[2]||"black",n),1==d.length&&h&&(a[r][r+h[0]]=d[0]),"continue"}if(/^background(color)?$/i.test(t))return a.backgroundColor=e,"continue";if(/^objectPosition$/i.test(t))return a[t]=e.split(" "),"continue";if(/padding|margin|radius/i.test(t)){var c=/radius$/i.test(t),u=c?"borderRadius":t.match(/[a-z]+/)[0],p=[0,0,0,0].map((function(t,e){return c?["borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius"][e]:[u+"Top",u+"Right",u+"Bottom",u+"Left"][e]})),g="margin";if("padding"===t||t===g||/^(border)?radius$/i.test(t)){var v,y=(null==e?void 0:e.split(" ").map((function(e){return/^\d+(rpx|px)?$/.test(e)?j(e):t!=g&&/auto/.test(e)?0:e}),[]))||[0],b=c?"borderRadius":t,x=y[0],m=y[1],w=y[2],S=y[3];a[b]=((v={})[p[0]]=V(x)?0:x,v[p[1]]=F(m)||V(m)?m:x,v[p[2]]=V(F(w)?w:x)?0:F(w)?w:x,v[p[3]]=F(S)?S:m||x,v)}else{var z;if("object"==typeof a[u])a[u][t]=u==g&&V(e)||G(e)?e:j(e);else a[u]=((z={})[p[0]]=a[u]||0,z[p[1]]=a[u]||0,z[p[2]]=a[u]||0,z[p[3]]=a[u]||0,z),a[u][t]=u==g&&V(e)||G(e)?e:j(e)}return"continue"}if(/^transform$/i.test(t))return a[t]={},e.replace(/([a-zA-Z]+)\(([0-9,-\.%rpxdeg\s]+)\)/g,(function(e,i,n){var r=n.split(",").map((function(t){return t.replace(/(^\s*)|(\s*$)/g,"")})),s=function(t,e){return t.includes("deg")?1*t:e&&!G(e)?j(t,e):t};i.includes("matrix")?a[t][i]=r.map((function(t){return 1*t})):i.includes("rotate")?a[t][i]=1*n.match(/^-?\d+(\.\d+)?/)[0]:/[X, Y]/.test(i)?a[t][i]=/[X]/.test(i)?s(r[0],o.width):s(r[0],o.height):(a[t][i+"X"]=s(r[0],o.width),a[t][i+"Y"]=s(r[1]||r[0],o.height))})),"continue";/^font$/i.test(t)&&console.warn("font 不支持简写"),/^left|top$/i.test(t)&&![ht,dt].includes(o.position)?a[t]=0:a[t]=/^[\d\.]+(px|rpx)?$/.test(e)?j(e):/em$/.test(e)&&s==it?j(e,o.fontSize):e},l=0,f=Object.keys(o);l<f.length;l++)c();return a},e.setPosition=function(t,e){var i={left:"width",top:"height",right:"width",bottom:"height"};Object.keys(i).forEach((function(n){var o="right"==n?"left":"top";["right","bottom"].includes(n)&&void 0!==t.style[n]&&"number"!=typeof t.originStyle[o]?t.style[o]=e[i[n]]-t.offsetSize[i[n]]-j(t.style[n],e[i[n]]):t.style[n]=j(t.style[n],e[i[n]])}))},e.getAttributes=function(t){var e=t.attributes||{};return(t.url||t.src)&&(e.src=e.src||t.url||t.src),t.replace&&(e.replace=t.replace),t.text&&(e.text=t.text),e},e.getOffsetSize=function(t,e,i){void 0===i&&(i=a[3]);var n=e||{},o=n.margin,r=(o=void 0===o?{}:o).marginLeft,s=void 0===r?0:r,h=o.marginTop,d=void 0===h?0:h,c=o.marginRight,l=void 0===c?0:c,f=o.marginBottom,u=void 0===f?0:f,p=n.padding,g=(p=void 0===p?{}:p).paddingLeft,v=void 0===g?0:g,y=p.paddingTop,b=void 0===y?0:y,x=p.paddingRight,m=void 0===x?0:x,w=p.paddingBottom,S=void 0===w?0:w,z=n.border,M=(z=void 0===z?{}:z).borderWidth,B=void 0===M?0:M,I=n.borderTop,k=(I=void 0===I?{}:I).borderTopWidth,P=void 0===k?B:k,W=n.borderBottom,R=(W=void 0===W?{}:W).borderBottomWidth,O=void 0===R?B:R,T=n.borderRight,L=(T=void 0===T?{}:T).borderRightWidth,A=void 0===L?B:L,F=n.borderLeft,j=(F=void 0===F?{}:F).borderLeftWidth,C=void 0===j?B:j,E=s<0&&l<0?Math.abs(s+l):0,H=d<0&&u<0?Math.abs(d+u):0,U=s>=0&&l<0,Y=d>=0&&u<0;return i==a[0]&&(this[i].left=t.left+s+v+C+(U?2*-l:0),this[i].top=t.top+d+b+P+(Y?2*-u:0),this[i].width=t.width+(this[i].widthAdd?0:E),this[i].height=t.height+(this[i].heightAdd?0:H),this[i].widthAdd=E,this[i].heightAdd=H),i==a[1]&&(this[i].left=t.left+s+C+(U<0?-l:0),this[i].top=t.top+d+P+(Y?-u:0),this[i].width=t.width+v+m,this[i].height=t.height+b+S),i==a[2]&&(this[i].left=t.left+s+C/2+(U<0?-l:0),this[i].top=t.top+d+P/2+(Y?-u:0),this[i].width=t.width+v+m+C/2+A/2,this[i].height=t.height+b+S+O/2+P/2),i==a[3]&&(this[i].left=t.left+(U<0?-l:0),this[i].top=t.top+(Y?-u:0),this[i].width=t.width+v+m+C+A+s+l,this[i].height=t.height+b+S+O+P+u+d),this[i]},e.layoutBoxUpdate=function(t,e,i,n){var o=this;if(void 0===i&&(i=-1),"border-box"==e.boxSizing){var r=e||{},s=r.border,h=(s=void 0===s?{}:s).borderWidth,d=void 0===h?0:h,c=r.borderTop,l=(c=void 0===c?{}:c).borderTopWidth,f=void 0===l?d:l,u=r.borderBottom,p=(u=void 0===u?{}:u).borderBottomWidth,g=void 0===p?d:p,v=r.borderRight,y=(v=void 0===v?{}:v).borderRightWidth,b=void 0===y?d:y,x=r.borderLeft,m=(x=void 0===x?{}:x).borderLeftWidth,w=void 0===m?d:m,S=r.padding,z=(S=void 0===S?{}:S).paddingTop,M=void 0===z?0:z,B=S.paddingRight,I=void 0===B?0:B,k=S.paddingBottom,P=void 0===k?0:k,W=S.paddingLeft,R=void 0===W?0:W;i||(t.width-=R+I+b+w),1!==i||n||(t.height-=M+P+f+g)}this.layoutBox&&(a.forEach((function(i){return o.layoutBox[i]=o.getOffsetSize(t,e,i)})),this.layoutBox=Object.assign({},this.layoutBox,this.layoutBox.borderSize))},e.getBoxPosition2=function(){var t=this.computedStyle,e=this.fixedLine,i=this.lines,n=t.left,o=void 0===n?0:n,r=t.top,s=void 0===r?0:r,a=t.padding||{},h=a.paddingBottom,d=void 0===h?0:h,c=a.paddingRight,l=void 0===c?0:c,f=$({},this.contentSize,{left:o,top:s}),u=this.contentSize.top-this.offsetSize.top,p=this.contentSize.left-this.offsetSize.left;if(this.root.fixedLine&&!this.root.isDone){this.root.isDone=!0;for(var g,v=X(this.root.fixedLine.elements);!(g=v()).done;){var y=g.value;y.setPosition(y,this.root.offsetSize),y.getBoxPosition2()}}if(e)for(var b,x=X(e.elements);!(b=x()).done;){var m=b.value;m.setPosition(m,f),m.style.left+=o+p+l,m.style.top+=s+u+d,m.getBoxPosition2()}if(i)for(var w,S=X(i);!(w=S()).done;){w.value.layout(f.top+u,f.left+p)}return this.layoutBoxUpdate(f,t),this.layoutBox},e.getBoxState=function(t,e){return this.isBlock(t)||this.isBlock(e)},e.isBlock=function(t){return void 0===t&&(t=this),t&&t.style.display==rt},e.isFlex=function(t){return void 0===t&&(t=this),t&&t.style.display==at},e.isInFlow=function(){return!(this.isAbsolute||this.isFixed)},e.inFlexBox=function(t){return void 0===t&&(t=this),!!t.isInFlow()&&(!!t.parent&&(!(!t.parent||t.parent.style.display!==at)||void 0))},e.isInline=function(t){return void 0===t&&(t=this),t&&t.style.display==st},e.contrastSize=function(t,e,i){var n=t;return i&&(n=Math.min(n,i)),e&&(n=Math.max(n,e)),n},e.measureText=function(t,e){var i=this.ctx.measureText(t),n=i.width,o=i.actualBoundingBoxAscent,r=i.actualBoundingBoxDescent;return{ascent:o,descent:r,width:n,fontHeight:o+r||.7*e+1}},e.getBoxWidthHeight=function(){var t,e=this,i=this.name,n=this.computedStyle,o=this.attributes,r=this.parent,s=void 0===r?{}:r,a=this.ctx,h=this.getChildren(),d=n.left,c=void 0===d?0:d,l=n.top,f=void 0===l?0:l,u=n.bottom,p=n.right,g=n.width,v=void 0===g?0:g,y=n.minWidth,b=n.maxWidth,x=n.minHeight,m=n.maxHeight,w=n.height,S=void 0===w?0:w,z=n.fontSize,M=void 0===z?14:z,B=n.fontWeight,I=n.fontFamily,k=n.fontStyle,P=n.position,W=n.lineClamp,R=n.lineHeight,O=n.padding,T=void 0===O?{}:O,L=n.margin,A=void 0===L?{}:L,F=n.border,C=(F=void 0===F?{}:F).borderWidth,E=void 0===C?0:C,H=n.borderRight,U=(H=void 0===H?{}:H).borderRightWidth,Y=void 0===U?E:U,$=n.borderLeft,D=($=void 0===$?{}:$).borderLeftWidth,_=void 0===D?E:D,X=s.contentSize&&s.contentSize.width,N=s.contentSize&&s.contentSize.height;if(G(v)&&X&&(v=j(v,X)),G(v)&&!X&&(v=null),G(S)&&N&&(S=j(S,N)),G(S)&&!N&&(S=null),G(y)&&X&&(y=j(y,X)),G(b)&&X&&(b=j(b,X)),G(x)&&N&&(x=j(x,N)),G(m)&&N&&(m=j(m,N)),n.padding&&null!=(t=s.contentSize)&&t.width)for(var V in n.padding)Object.hasOwnProperty.call(n.padding,V)&&(n.padding[V]=j(n.padding[V],X));var q=T.paddingRight,J=void 0===q?0:q,Z=T.paddingLeft,tt=void 0===Z?0:Z;if(n.margin&&[n.margin.marginLeft,n.margin.marginRight].includes("auto"))if(v){var ot=X&&X-v-J-tt-_-Y||0;n.margin.marginLeft==n.margin.marginRight?n.margin.marginLeft=n.margin.marginRight=ot/2:"auto"==n.margin.marginLeft?n.margin.marginLeft=ot:n.margin.marginRight=ot}else n.margin.marginLeft=n.margin.marginRight=0;var rt=A.marginRight,st=void 0===rt?0:rt,at=A.marginLeft,dt={width:v,height:S,left:0,top:0},ct=tt+J+_+Y+(void 0===at?0:at)+st;if(i==it&&!this.attributes.widths){var lt=o.text||"";a.save(),a.setFonts({fontFamily:I,fontSize:M,fontWeight:B,fontStyle:k});var ft=new Map;lt.split("\n").map((function(t){var i=t.split("").map((function(t){var i=ft.get(t);if(i)return i;var n=e.measureText(t,M).width;return ft.set(t,n),n})),n=e.measureText(t,M),o=n.fontHeight,r=n.ascent,s=n.descent;e.attributes.fontHeight=o,e.attributes.ascent=r,e.attributes.descent=s,e.attributes.widths||(e.attributes.widths=[]),e.attributes.widths.push({widths:i,total:i.reduce((function(t,e){return t+e}),0)})})),a.restore()}if(i==et&&null==v){var ut=o.width,pt=o.height;dt.width=this.contrastSize(Math.round(ut*S/pt)||0,y,b),this.layoutBoxUpdate(dt,n,0)}if(i==it&&null==v){var gt=this.attributes.widths,vt=Math.max.apply(Math,gt.map((function(t){return t.total})));if(s&&X>0&&(vt>X||this.isBlock(this))&&!this.isAbsolute&&!this.isFixed)vt=X-ct;dt.width=this.contrastSize(vt,y,b),this.layoutBoxUpdate(dt,n,0)}if(i==it&&!this.attributes.lines){var yt=this.attributes.widths.length;this.attributes.widths.forEach((function(t){return t.widths.reduce((function(t,e,i){return t+e>dt.width?(yt++,e):t+e}),0)})),yt=W&&yt>W?W:yt,this.attributes.lines=yt}if(i==et&&null==S){var bt=o.width,xt=o.height;dt.height=this.contrastSize(j(dt.width*xt/bt)||0,x,m),this.layoutBoxUpdate(dt,n,1)}i==it&&null==S&&(R=j(R,M),dt.height=this.contrastSize(j(this.attributes.lines*R),x,m),this.layoutBoxUpdate(dt,n,1,!0)),s&&s.children&&X&&([nt,it].includes(i)&&this.isFlex()||i==nt&&this.isBlock(this)&&!this.isInFlow())&&(dt.width=this.contrastSize(X-ct,y,b),this.layoutBoxUpdate(dt,n)),v&&!G(v)&&(dt.width=this.contrastSize(v,y,b),this.layoutBoxUpdate(dt,n,0)),S&&!G(S)&&(dt.height=this.contrastSize(dt.height,x,m),this.layoutBoxUpdate(dt,n,1));var mt=0;if(h.length){var wt=null;h.forEach((function(t,i){t.getBoxWidthHeight();var o=h[i+1];if(o&&o.isInFlow()&&(t.next=o),t.isInFlow()&&!t.inFlexBox()){var r=e.getBoxState(wt,t);e.line&&e.line.canIEnter(t)&&!r?e.line.add(t):(new Q).bind(t),wt=t}else t.inFlexBox()?e.line&&(e.line.canIEnter(t)||"nowrap"==n.flexWrap)?e.line.add(t):(new K).bind(t):t.isFixed?e.root.fixedLine?e.root.fixedLine.fixedAdd(t):(new Q).fixedBind(t):e.fixedLine?e.fixedLine.fixedAdd(t):(new Q).fixedBind(t,1)})),this.lines&&(mt=this.lines.reduce((function(t,e){return t+e.height}),0))}var St=0,zt=0;if(!v&&(this.isAbsolute||this.isFixed)&&X){var Mt=P==ht?X:this.root.width,Bt=Mt-(G(c)?j(c,Mt):c)-(G(p)?j(p,Mt):p);St=n.left?Bt:this.lineMaxWidth}if(!S&&(null!=f?f:this.isAbsolute||this.isFixed&&N)){var It=P==ht?N:this.root.height,kt=It-(G(f)?j(f,It):f)-(G(u)?j(u,It):u);zt=n.top?kt:0}if(v&&!G(v)||dt.width||(dt.width=St||this.contrastSize((this.isBlock(this)&&!this.isInFlow()?X||s.lineMaxWidth:this.lineMaxWidth)||this.lineMaxWidth,y,b),this.layoutBoxUpdate(dt,n,0)),S||!mt&&!zt||(dt.height=zt||this.contrastSize(mt,x,m),this.layoutBoxUpdate(dt,n)),n.borderRadius&&this.borderSize&&this.borderSize.width)for(var V in n.borderRadius)Object.hasOwnProperty.call(n.borderRadius,V)&&(n.borderRadius[V]=j(n.borderRadius[V],this.borderSize.width));return this.layoutBox},e.layout=function(){return this.getBoxWidthHeight(),this.root.offsetSize=this.offsetSize,this.getBoxPosition2(),this.offsetSize},t}(),ut=function(){var t,e,i,n,o,r,s=[0,11,15,19,23,27,31,16,18,20,22,24,26,28,20,22,24,24,26,28,28,22,24,24,26,26,28,28,24,24,26,26,26,28,28,24,26,26,26,28,28],a=[3220,1468,2713,1235,3062,1890,2119,1549,2344,2936,1117,2583,1330,2470,1667,2249,2028,3780,481,4011,142,3098,831,3445,592,2517,1776,2234,1951,2827,1070,2660,1345,3177],h=[30660,29427,32170,30877,26159,25368,27713,26998,21522,20773,24188,23371,17913,16590,20375,19104,13663,12392,16177,14854,9396,8579,11994,11245,5769,5054,7399,6608,1890,597,3340,2107],d=[1,0,19,7,1,0,16,10,1,0,13,13,1,0,9,17,1,0,34,10,1,0,28,16,1,0,22,22,1,0,16,28,1,0,55,15,1,0,44,26,2,0,17,18,2,0,13,22,1,0,80,20,2,0,32,18,2,0,24,26,4,0,9,16,1,0,108,26,2,0,43,24,2,2,15,18,2,2,11,22,2,0,68,18,4,0,27,16,4,0,19,24,4,0,15,28,2,0,78,20,4,0,31,18,2,4,14,18,4,1,13,26,2,0,97,24,2,2,38,22,4,2,18,22,4,2,14,26,2,0,116,30,3,2,36,22,4,4,16,20,4,4,12,24,2,2,68,18,4,1,43,26,6,2,19,24,6,2,15,28,4,0,81,20,1,4,50,30,4,4,22,28,3,8,12,24,2,2,92,24,6,2,36,22,4,6,20,26,7,4,14,28,4,0,107,26,8,1,37,22,8,4,20,24,12,4,11,22,3,1,115,30,4,5,40,24,11,5,16,20,11,5,12,24,5,1,87,22,5,5,41,24,5,7,24,30,11,7,12,24,5,1,98,24,7,3,45,28,15,2,19,24,3,13,15,30,1,5,107,28,10,1,46,28,1,15,22,28,2,17,14,28,5,1,120,30,9,4,43,26,17,1,22,28,2,19,14,28,3,4,113,28,3,11,44,26,17,4,21,26,9,16,13,26,3,5,107,28,3,13,41,26,15,5,24,30,15,10,15,28,4,4,116,28,17,0,42,26,17,6,22,28,19,6,16,30,2,7,111,28,17,0,46,28,7,16,24,30,34,0,13,24,4,5,121,30,4,14,47,28,11,14,24,30,16,14,15,30,6,4,117,30,6,14,45,28,11,16,24,30,30,2,16,30,8,4,106,26,8,13,47,28,7,22,24,30,22,13,15,30,10,2,114,28,19,4,46,28,28,6,22,28,33,4,16,30,8,4,122,30,22,3,45,28,8,26,23,30,12,28,15,30,3,10,117,30,3,23,45,28,4,31,24,30,11,31,15,30,7,7,116,30,21,7,45,28,1,37,23,30,19,26,15,30,5,10,115,30,19,10,47,28,15,25,24,30,23,25,15,30,13,3,115,30,2,29,46,28,42,1,24,30,23,28,15,30,17,0,115,30,10,23,46,28,10,35,24,30,19,35,15,30,17,1,115,30,14,21,46,28,29,19,24,30,11,46,15,30,13,6,115,30,14,23,46,28,44,7,24,30,59,1,16,30,12,7,121,30,12,26,47,28,39,14,24,30,22,41,15,30,6,14,121,30,6,34,47,28,46,10,24,30,2,64,15,30,17,4,122,30,29,14,46,28,49,10,24,30,24,46,15,30,4,18,122,30,13,32,46,28,48,14,24,30,42,32,15,30,20,4,117,30,40,7,47,28,43,22,24,30,10,67,15,30,19,6,118,30,18,31,47,28,34,34,24,30,20,61,15,30],c=[255,0,1,25,2,50,26,198,3,223,51,238,27,104,199,75,4,100,224,14,52,141,239,129,28,193,105,248,200,8,76,113,5,138,101,47,225,36,15,33,53,147,142,218,240,18,130,69,29,181,194,125,106,39,249,185,201,154,9,120,77,228,114,166,6,191,139,98,102,221,48,253,226,152,37,179,16,145,34,136,54,208,148,206,143,150,219,189,241,210,19,92,131,56,70,64,30,66,182,163,195,72,126,110,107,58,40,84,250,133,186,61,202,94,155,159,10,21,121,43,78,212,229,172,115,243,167,87,7,112,192,247,140,128,99,13,103,74,222,237,49,197,254,24,227,165,153,119,38,184,180,124,17,68,146,217,35,32,137,46,55,63,209,91,149,188,207,205,144,135,151,178,220,252,190,97,242,86,211,171,20,42,93,158,132,60,57,83,71,109,65,162,31,45,67,216,183,123,164,118,196,23,73,236,127,12,111,246,108,161,59,82,41,157,85,170,251,96,134,177,187,204,62,90,203,89,95,176,156,169,160,81,11,245,22,235,122,117,44,215,79,174,213,233,230,231,173,232,116,214,244,234,168,80,88,175],l=[1,2,4,8,16,32,64,128,29,58,116,232,205,135,19,38,76,152,45,90,180,117,234,201,143,3,6,12,24,48,96,192,157,39,78,156,37,74,148,53,106,212,181,119,238,193,159,35,70,140,5,10,20,40,80,160,93,186,105,210,185,111,222,161,95,190,97,194,153,47,94,188,101,202,137,15,30,60,120,240,253,231,211,187,107,214,177,127,254,225,223,163,91,182,113,226,217,175,67,134,17,34,68,136,13,26,52,104,208,189,103,206,129,31,62,124,248,237,199,147,59,118,236,197,151,51,102,204,133,23,46,92,184,109,218,169,79,158,33,66,132,21,42,84,168,77,154,41,82,164,85,170,73,146,57,114,228,213,183,115,230,209,191,99,198,145,63,126,252,229,215,179,123,246,241,255,227,219,171,75,150,49,98,196,149,55,110,220,165,87,174,65,130,25,50,100,200,141,7,14,28,56,112,224,221,167,83,166,81,162,89,178,121,242,249,239,195,155,43,86,172,69,138,9,18,36,72,144,61,122,244,245,247,243,251,235,203,139,11,22,44,88,176,125,250,233,207,131,27,54,108,216,173,71,142,0],f=[],u=[],p=[],g=[],v=[],y=2;function b(t,e){var i;t>e&&(i=t,t=e,e=i),i=e,i*=e,i+=e,i>>=1,g[i+=t]=1}function x(t,i){var n;for(p[t+e*i]=1,n=-2;n<2;n++)p[t+n+e*(i-2)]=1,p[t-2+e*(i+n+1)]=1,p[t+2+e*(i+n)]=1,p[t+n+1+e*(i+2)]=1;for(n=0;n<2;n++)b(t-1,i+n),b(t+1,i-n),b(t-n,i-1),b(t+n,i+1)}function m(t){for(;t>=255;)t=((t-=255)>>8)+(255&t);return t}var w=[];function S(t,e,i,n){var o,r,s;for(o=0;o<n;o++)f[i+o]=0;for(o=0;o<e;o++){if(255!=(s=c[f[t+o]^f[i]]))for(r=1;r<n;r++)f[i+r-1]=f[i+r]^l[m(s+w[n-r])];else for(r=i;r<i+n;r++)f[r]=f[r+1];f[i+n-1]=255==s?0:l[m(s+w[0])]}}function z(t,e){var i;return t>e&&(i=t,t=e,e=i),i=e,i+=e*e,i>>=1,g[i+=t]}function M(t){var i,n,o,r;switch(t){case 0:for(n=0;n<e;n++)for(i=0;i<e;i++)i+n&1||z(i,n)||(p[i+n*e]^=1);break;case 1:for(n=0;n<e;n++)for(i=0;i<e;i++)1&n||z(i,n)||(p[i+n*e]^=1);break;case 2:for(n=0;n<e;n++)for(o=0,i=0;i<e;i++,o++)3==o&&(o=0),o||z(i,n)||(p[i+n*e]^=1);break;case 3:for(r=0,n=0;n<e;n++,r++)for(3==r&&(r=0),o=r,i=0;i<e;i++,o++)3==o&&(o=0),o||z(i,n)||(p[i+n*e]^=1);break;case 4:for(n=0;n<e;n++)for(o=0,r=n>>1&1,i=0;i<e;i++,o++)3==o&&(o=0,r=!r),r||z(i,n)||(p[i+n*e]^=1);break;case 5:for(r=0,n=0;n<e;n++,r++)for(3==r&&(r=0),o=0,i=0;i<e;i++,o++)3==o&&(o=0),(i&n&1)+!(!o|!r)||z(i,n)||(p[i+n*e]^=1);break;case 6:for(r=0,n=0;n<e;n++,r++)for(3==r&&(r=0),o=0,i=0;i<e;i++,o++)3==o&&(o=0),(i&n&1)+(o&&o==r)&1||z(i,n)||(p[i+n*e]^=1);break;case 7:for(r=0,n=0;n<e;n++,r++)for(3==r&&(r=0),o=0,i=0;i<e;i++,o++)3==o&&(o=0),(o&&o==r)+(i+n&1)&1||z(i,n)||(p[i+n*e]^=1)}}function B(t){var e,i=0;for(e=0;e<=t;e++)v[e]>=5&&(i+=3+v[e]-5);for(e=3;e<t-1;e+=2)v[e-2]==v[e+2]&&v[e+2]==v[e-1]&&v[e-1]==v[e+1]&&3*v[e-1]==v[e]&&(0==v[e-3]||e+3>t||3*v[e-3]>=4*v[e]||3*v[e+3]>=4*v[e])&&(i+=40);return i}function I(){var t,i,n,o,r,s=0,a=0;for(i=0;i<e-1;i++)for(t=0;t<e-1;t++)(p[t+e*i]&&p[t+1+e*i]&&p[t+e*(i+1)]&&p[t+1+e*(i+1)]||!(p[t+e*i]||p[t+1+e*i]||p[t+e*(i+1)]||p[t+1+e*(i+1)]))&&(s+=3);for(i=0;i<e;i++){for(v[0]=0,n=o=t=0;t<e;t++)(r=p[t+e*i])==o?v[n]++:v[++n]=1,a+=(o=r)?1:-1;s+=B(n)}a<0&&(a=-a);var h=a,d=0;for(h+=h<<2,h<<=1;h>e*e;)h-=e*e,d++;for(s+=10*d,t=0;t<e;t++){for(v[0]=0,n=o=i=0;i<e;i++)(r=p[t+e*i])==o?v[n]++:v[++n]=1,o=r;s+=B(n)}return s}var k=null;return{api:{get ecclevel(){return y},set ecclevel(t){y=t},get size(){return _size},set size(t){_size=t},get canvas(){return k},set canvas(t){k=t},getFrame:function(v){return function(v){var B,k,P,W,R,O,T,L;W=v.length,t=0;do{if(t++,P=4*(y-1)+16*(t-1),i=d[P++],n=d[P++],o=d[P++],r=d[P],W<=(P=o*(i+n)+n-3+(t<=9)))break}while(t<40);for(e=17+4*t,R=o+(o+r)*(i+n)+n,W=0;W<R;W++)u[W]=0;for(f=v.slice(0),W=0;W<e*e;W++)p[W]=0;for(W=0;W<(e*(e+1)+1)/2;W++)g[W]=0;for(W=0;W<3;W++){for(P=0,k=0,1==W&&(P=e-7),2==W&&(k=e-7),p[k+3+e*(P+3)]=1,B=0;B<6;B++)p[k+B+e*P]=1,p[k+e*(P+B+1)]=1,p[k+6+e*(P+B)]=1,p[k+B+1+e*(P+6)]=1;for(B=1;B<5;B++)b(k+B,P+1),b(k+1,P+B+1),b(k+5,P+B),b(k+B+1,P+5);for(B=2;B<4;B++)p[k+B+e*(P+2)]=1,p[k+2+e*(P+B+1)]=1,p[k+4+e*(P+B)]=1,p[k+B+1+e*(P+4)]=1}if(t>1)for(W=s[t],k=e-7;;){for(B=e-7;B>W-3&&(x(B,k),!(B<W));)B-=W;if(k<=W+9)break;x(6,k-=W),x(k,6)}for(p[8+e*(e-8)]=1,k=0;k<7;k++)b(7,k),b(e-8,k),b(7,k+e-7);for(B=0;B<8;B++)b(B,7),b(B+e-8,7),b(B,e-8);for(B=0;B<9;B++)b(B,8);for(B=0;B<8;B++)b(B+e-8,8),b(8,B);for(k=0;k<7;k++)b(8,k+e-7);for(B=0;B<e-14;B++)1&B?(b(8+B,6),b(6,8+B)):(p[8+B+6*e]=1,p[6+e*(8+B)]=1);if(t>6)for(W=a[t-7],P=17,B=0;B<6;B++)for(k=0;k<3;k++,P--)1&(P>11?t>>P-12:W>>P)?(p[5-B+e*(2-k+e-11)]=1,p[2-k+e-11+e*(5-B)]=1):(b(5-B,2-k+e-11),b(2-k+e-11,5-B));for(k=0;k<e;k++)for(B=0;B<=k;B++)p[B+e*k]&&b(B,k);for(R=f.length,O=0;O<R;O++)u[O]=f.charCodeAt(O);if(f=u.slice(0),R>=(B=o*(i+n)+n)-2&&(R=B-2,t>9&&R--),O=R,t>9){for(f[O+2]=0,f[O+3]=0;O--;)W=f[O],f[O+3]|=255&W<<4,f[O+2]=W>>4;f[2]|=255&R<<4,f[1]=R>>4,f[0]=64|R>>12}else{for(f[O+1]=0,f[O+2]=0;O--;)W=f[O],f[O+2]|=255&W<<4,f[O+1]=W>>4;f[1]|=255&R<<4,f[0]=64|R>>4}for(O=R+3-(t<10);O<B;)f[O++]=236,f[O++]=17;for(w[0]=1,O=0;O<r;O++){for(w[O+1]=1,T=O;T>0;T--)w[T]=w[T]?w[T-1]^l[m(c[w[T]]+O)]:w[T-1];w[0]=l[m(c[w[0]]+O)]}for(O=0;O<=r;O++)w[O]=c[w[O]];for(P=B,k=0,O=0;O<i;O++)S(k,o,P,r),k+=o,P+=r;for(O=0;O<n;O++)S(k,o+1,P,r),k+=o+1,P+=r;for(k=0,O=0;O<o;O++){for(T=0;T<i;T++)u[k++]=f[O+T*o];for(T=0;T<n;T++)u[k++]=f[i*o+O+T*(o+1)]}for(T=0;T<n;T++)u[k++]=f[i*o+O+T*(o+1)];for(O=0;O<r;O++)for(T=0;T<i+n;T++)u[k++]=f[B+O+T*r];for(f=u,B=k=e-1,P=R=1,L=(o+r)*(i+n)+n,O=0;O<L;O++)for(W=f[O],T=0;T<8;T++,W<<=1){128&W&&(p[B+e*k]=1);do{R?B--:(B++,P?0!=k?k--:(P=!P,6==(B-=2)&&(B--,k=9)):k!=e-1?k++:(P=!P,6==(B-=2)&&(B--,k-=8))),R=!R}while(z(B,k))}for(f=p.slice(0),W=0,k=3e4,P=0;P<8&&(M(P),(B=I())<k&&(k=B,W=P),7!=W);P++)p=f.slice(0);for(W!=P&&M(W),k=h[W+(y-1<<3)],P=0;P<8;P++,k>>=1)1&k&&(p[e-1-P+8*e]=1,P<6?p[8+e*P]=1:p[8+e*(P+1)]=1);for(P=0;P<7;P++,k>>=1)1&k&&(p[8+e*(e-7+P)]=1,P?p[6-P+8*e]=1:p[7+8*e]=1);return p}(v)},utf16to8:function(t){var e,i,n,o;for(e="",n=t.length,i=0;i<n;i++)(o=t.charCodeAt(i))>=1&&o<=127?e+=t.charAt(i):o>2047?(e+=String.fromCharCode(224|o>>12&15),e+=String.fromCharCode(128|o>>6&63),e+=String.fromCharCode(128|o>>0&63)):(e+=String.fromCharCode(192|o>>6&31),e+=String.fromCharCode(128|o>>0&63));return e},draw:function(t,i,n,o,r){i.drawView(n,o);var s=i.ctx,a=n.contentSize,h=a.width,d=a.height,c=a.left,l=a.top;o.borderRadius,o.backgroundColor;var f=o.color,u=void 0===f?"#000000":f;o.border,n.contentSize.left,n.borderSize.left,n.contentSize.top,n.borderSize.top;if(y=r||y,s){s.save(),i.setOpacity(o),i.setTransform(n,o);var p=Math.min(h,d);t=this.utf16to8(t);var g=this.getFrame(t),v=p/e;s.setFillStyle(u);for(var b=0;b<e;b++)for(var x=0;x<e;x++)g[x*e+b]&&s.fillRect(c+v*b,l+v*x,v,v);s.restore(),i.setBorder(n,o)}else console.warn("No canvas provided to draw QR code in!")}}}}(),pt=b,gt=y,vt=x,yt=v,bt=c,xt=l,mt=f,wt=u,St=p,zt=g,Mt=function(){function t(t,e){var i,n=this;this.v="1.9.3.4",this.id=null,this.pixelRatio=1,this.width=0,this.height=0,this.sleep=1e3/30,this.count=0,this.isRate=!1,this.isDraw=!0,this.isCache=!0,this.fixed="",this.useCORS=!1,this.imageBus=[],this.createImage=function(t,e){return new Promise((function(i,o){var r=null;r=n.canvas.createImage?n.canvas.createImage():new Image,e&&r.setAttribute("crossOrigin","Anonymous"),r.src=t,r.onload=function(){i({width:r.naturalWidth||r.width,height:r.naturalHeight||r.height,path:r,src:this.src})},r.onerror=function(t){o(t)}}))},this.options=t,Object.assign(this,t),this.component=e,this.ctx=((i=t.context).setFonts=function(t){var e=t.fontFamily,n=void 0===e?"sans-serif":e,o=t.fontSize,r=void 0===o?14:o,a=t.fontWeight,h=void 0===a?"normal":a,d=t.fontStyle,c=void 0===d?"normal":d;T==s.MP_TOUTIAO&&(h="bold"==h?"bold":"",c="italic"==c?"italic":""),i.font="".concat(c," ").concat(h," ").concat(Math.round(r),"px ").concat(n)},i.draw&&i.setFillStyle?i:Object.assign(i,{setStrokeStyle:function(t){i.strokeStyle=t},setLineWidth:function(t){i.lineWidth=t},setLineCap:function(t){i.lineCap=t},setFillStyle:function(t){i.fillStyle=t},setFontSize:function(t){i.font="".concat(String(t),"px sans-serif")},setGlobalAlpha:function(t){i.globalAlpha=t},setLineJoin:function(t){i.lineJoin=t},setTextAlign:function(t){i.textAlign=t},setMiterLimit:function(t){i.miterLimit=t},setShadow:function(t,e,n,o){i.shadowOffsetX=t,i.shadowOffsetY=e,i.shadowBlur=n,i.shadowColor=o},setTextBaseline:function(t){i.textBaseline=t},createCircularGradient:function(){},draw:function(){}})),this.progress=0,this.root={width:t.width,height:t.height,fontSizeRate:1,fixedLine:null},this.size=this.root,this.init();var o=0;Object.defineProperty(this,"progress",{configurable:!0,set:function(t){o=t,n.lifecycle("onProgress",t/n.count)},get:function(){return o||0}})}return t.prototype.lifecycle=function(t,e){this.options.listen&&this.options.listen[t]&&this.options.listen[t](e)},t.prototype.init=function(t){t&&(this.ctx=t),(this.canvas.height||s.WEB==T)&&(this.canvas.height=this.size.height*this.pixelRatio,this.canvas.width=this.size.width*this.pixelRatio,this.ctx.scale(this.pixelRatio,this.pixelRatio))},t.prototype.clear=function(){this.ctx.clearRect(0,0,this.size.width,this.size.height)},t.prototype.clipPath=function(t,e,i,n,o,r,s){void 0===r&&(r=!1),void 0===s&&(s=!1);var a=this.ctx;if(/polygon/.test(o)){var h=o.match(/-?\d+(rpx|px|%)?\s+-?\d+(rpx|px|%)?/g)||[];a.beginPath(),h.map((function(o){var r=o.split(" "),s=r[0],a=r[1];return[j(s,i)+t,j(a,n)+e]})).forEach((function(t,e){0==e?a.moveTo(t[0],t[1]):a.lineTo(t[0],t[1])})),a.closePath(),s&&a.stroke(),r&&a.fill()}},t.prototype.roundRect=function(t,e,i,n,o,r,s){if(void 0===r&&(r=!1),void 0===s&&(s=!1),!(o<0)){var a=this.ctx;if(a.beginPath(),o){var h=o||{},d=h.borderTopLeftRadius,c=void 0===d?o||0:d,l=h.borderTopRightRadius,f=void 0===l?o||0:l,u=h.borderBottomRightRadius,p=void 0===u?o||0:u,g=h.borderBottomLeftRadius,v=void 0===g?o||0:g;a.arc(t+i-p,e+n-p,p,0,.5*Math.PI),a.lineTo(t+v,e+n),a.arc(t+v,e+n-v,v,.5*Math.PI,Math.PI),a.lineTo(t,e+c),a.arc(t+c,e+c,c,Math.PI,1.5*Math.PI),a.lineTo(t+i-f,e),a.arc(t+i-f,e+f,f,1.5*Math.PI,2*Math.PI),a.lineTo(t+i,e+n-p)}else a.rect(t,e,i,n);a.closePath(),s&&a.stroke(),r&&a.fill()}},t.prototype.setTransform=function(t,e){var i=e.transform,n=e.transformOrigin,o=this.ctx,r=i||{},s=r.scaleX,a=void 0===s?1:s,h=r.scaleY,d=void 0===h?1:h,c=r.translateX,l=void 0===c?0:c,f=r.translateY,u=void 0===f?0:f,p=r.rotate,g=void 0===p?0:p,v=r.skewX,y=void 0===v?0:v,b=r.skewY,x=void 0===b?0:b,m=t.left,w=t.top,S=t.width,z=t.height;l=j(l,S)||0,u=j(u,z)||0;var M={top:j("0%",1),center:j("50%",1),bottom:j("100%",1)},B={left:j("0%",1),center:j("50%",1),right:j("100%",1)};if(n=n.split(" ").filter((function(t,e){return e<2})).reduce((function(t,e){if(/\d+/.test(e)){var i=j(e,1)/(/px|rpx$/.test(e)?F(t.x)?z:S:1);return F(t.x)?Object.assign(t,{y:i}):Object.assign(t,{x:i})}return F(B[e])&&!F(t.x)?Object.assign(t,{x:B[e]}):Object.assign(t,{y:M[e]||.5})}),{}),(l||u)&&o.translate(l,u),(a||d)&&o.scale(a,d),g){var I=m+S*n.x,k=w+z*n.y;o.translate(I,k),o.rotate(g*Math.PI/180),o.translate(-I,-k)}(y||x)&&o.transform(1,Math.tan(x*Math.PI/180),Math.tan(y*Math.PI/180),1,0,0)},t.prototype.setBackground=function(t,e,i,n,o){var r=this.ctx;t&&t!=I?E(t)?H(t,e,i,n,o,r):r.setFillStyle(t):[s.MP_TOUTIAO,s.MP_BAIDU].includes(T)?r.setFillStyle("rgba(0,0,0,0)"):r.setFillStyle(I)},t.prototype.setShadow=function(t){var e=t.boxShadow,i=void 0===e?[]:e,n=this.ctx;if(i.length){var o=i[0],r=i[1],s=i[2],a=i[3];n.setShadow(o,r,s,a)}},t.prototype.setBorder=function(t,e){var i=this.ctx,n=t.width,o=t.height,r=t.left,s=t.top,a=e.border,h=e.borderBottom,d=e.borderTop,c=e.borderRight,l=e.borderLeft,f=e.borderRadius,u=e.lineCap,p=a||{},g=p.borderWidth,v=void 0===g?0:g,y=p.borderStyle,b=p.borderColor,x=h||{},m=x.borderBottomWidth,w=void 0===m?v:m,S=x.borderBottomStyle,z=void 0===S?y:S,M=x.borderBottomColor,B=void 0===M?b:M,I=d||{},k=I.borderTopWidth,P=void 0===k?v:k,W=I.borderTopStyle,R=void 0===W?y:W,O=I.borderTopColor,L=void 0===O?b:O,A=c||{},F=A.borderRightWidth,j=void 0===F?v:F,C=A.borderRightStyle,E=void 0===C?y:C,H=A.borderRightColor,U=void 0===H?b:H,Y=l||{},$=Y.borderLeftWidth,D=void 0===$?v:$,_=Y.borderLeftStyle,X=void 0===_?y:_,N=Y.borderLeftColor,V=void 0===N?b:N,G=f||{},q=G.borderTopLeftRadius,J=void 0===q?f||0:q,Q=G.borderTopRightRadius,Z=void 0===Q?f||0:Q,K=G.borderBottomRightRadius,tt=void 0===K?f||0:K,et=G.borderBottomLeftRadius,it=void 0===et?f||0:et;if(h||l||d||c||a){var nt=function(t,e,n){"dashed"==e?/mp/.test(T)?i.setLineDash([Math.ceil(4*t/3),Math.ceil(4*t/3)]):i.setLineDash([Math.ceil(6*t),Math.ceil(6*t)]):"dotted"==e&&i.setLineDash([t,t]),i.setStrokeStyle(n)},ot=function(t,e,n,o,r,s,a,h,d,c,l,f,p,g,v){i.save(),i.setLineCap(v?"square":u),i.setLineWidth(f),nt(f,p,g),i.beginPath(),i.arc(t,e,a,Math.PI*d,Math.PI*c),i.lineTo(n,o),i.arc(r,s,h,Math.PI*c,Math.PI*l),i.stroke(),i.restore()};if(i.save(),a&&!h&&!l&&!d&&!c)return i.setLineWidth(v),nt(v,y,b),this.roundRect(r,s,n,o,f,!1,!!b),void i.restore();w&&ot(r+n-tt,s+o-tt,r+it,s+o,r+it,s+o-it,tt,it,.25,.5,.75,w,z,B,D&&j),D&&ot(r+it,s+o-it,r,s+J,r+J,s+J,it,J,.75,1,1.25,D,X,V,P&&w),P&&ot(r+J,s+J,r+n-Z,s,r+n-Z,s+Z,J,Z,1.25,1.5,1.75,P,R,L,D&&j),j&&ot(r+n-Z,s+Z,r+n,s+o-tt,r+n-tt,s+o-tt,Z,tt,1.75,2,.25,j,E,U,P&&w)}},t.prototype.setOpacity=function(t){var e=t.opacity,i=void 0===e?1:e;this.ctx.setGlobalAlpha(i)},t.prototype.drawPattern=function(t,e,o){return i(this,void 0,void 0,(function(){var i=this;return n(this,(function(n){return[2,new Promise((function(n,r){i.drawView(e,o,!0,!1,!0);var s=i,a=s.ctx;s.canvas;var h=e.width,d=e.height,c=e.left,l=e.top,f=o||{},u=f.borderRadius,p=void 0===u?0:u,g=f.backgroundImage,v=f.backgroundRepeat,y=void 0===v?"repeat":v;g&&function(t){var r=a.createPattern(t.src,y);a.setFillStyle(r),i.roundRect(c,l,h,d,p,!0,!1),i.setBorder(e,o),n()}(t)}))]}))}))},t.prototype.drawView=function(t,e,i,n,o){void 0===i&&(i=!0),void 0===n&&(n=!0),void 0===o&&(o=!0);var r=this.ctx,s=t.width,a=t.height,h=t.left,d=t.top,c=e||{},l=c.borderRadius,f=void 0===l?0:l,u=c.backgroundColor,p=void 0===u?I:u,g=c.overflow;e.opacity&&this.setOpacity(e),this.setTransform(t,e),o&&(r.save(),this.setShadow(e)),i&&this.setBackground(p,s,a,h,d),e.clipPath?this.clipPath(h,d,s,a,e.clipPath,i,!1):this.roundRect(h,d,s,a,f,i,!1),o&&r.restore(),n&&this.setBorder(t,e),"hidden"==g&&r.clip()},t.prototype.drawImage=function(t,e,o,r){return void 0===e&&(e={}),void 0===o&&(o={}),void 0===r&&(r=!0),i(this,void 0,void 0,(function(){var a=this;return n(this,(function(h){switch(h.label){case 0:return[4,new Promise((function(h,d){return i(a,void 0,void 0,(function(){var i,a,d,c,l,f,u,p,g,v,y,b,x,m,w,S,z,M,B,P,W,R=this;return n(this,(function(n){return i=this.ctx,a=o.borderRadius,d=void 0===a?0:a,c=o.backgroundColor,l=void 0===c?I:c,f=o.objectFit,u=void 0===f?k:f,p=o.backgroundSize,g=void 0===p?k:p,v=o.objectPosition,y=o.backgroundPosition,b=o.boxShadow,o.backgroundImage&&(u=g,v=y),b&&this.drawView(e,Object.assign(o,{backgroundColor:l||b&&(l||"#ffffff")}),!0,!1,!0),x=e.width,m=e.height,w=e.left,S=e.top,i.save(),z=e.contentSize.left-e.borderSize.left,M=e.contentSize.top-e.borderSize.top,r||(this.setOpacity(o),this.setTransform(e,o),this.setBackground(l,x,m,w,S),this.roundRect(w,S,x,m,d,!!(d||!b&&l),!1)),w+=z,S+=M,i.clip(),B=function(t){if(u!==k){var n=function(t,e,i){var n=t.objectFit,o=t.objectPosition,r=e.width/e.height,s=i.width/i.height,a=1;"contain"==n&&r>=s||"cover"==n&&r<s?a=e.height/i.height:("contain"==n&&r<s||"cover"==n&&r>=s)&&(a=e.width/i.width);var h=i.width*a,d=i.height*a,c=o||[],l=c[0],f=c[1],u=/^\d+px|rpx$/.test(l)?j(l,e.width):(e.width-h)*(C(l)?j(l,1):{left:0,center:.5,right:1}[l||"center"]),p=/^\d+px|rpx$/.test(f)?j(f,e.height):(e.height-d)*(C(f)?j(f,1):{top:0,center:.5,bottom:1}[f||"center"]),g=function(t,e){return[(t-u)/a,(e-p)/a]},v=g(0,0),y=v[0],b=v[1],x=g(e.width,e.height),m=x[0],w=x[1];return{sx:Math.max(y,0),sy:Math.max(b,0),sw:Math.min(m-y,i.width),sh:Math.min(w-b,i.height),dx:Math.max(u,0),dy:Math.max(p,0),dw:Math.min(h,e.width),dh:Math.min(d,e.height)}}({objectFit:u,objectPosition:v},e.contentSize,t),o=n.sx,r=n.sy,a=n.sh,h=n.sw,d=n.dx,c=n.dy,l=n.dh,f=n.dw;T==s.MP_BAIDU?i.drawImage(t.src,d+w,c+S,f,l,o,r,h,a):i.drawImage(t.src,o,r,h,a,d+w,c+S,f,l)}else i.drawImage(t.src,w,S,x,m)},P=function(){i.restore(),R.drawView(e,o,!1,!0,!1),h(1)},W=function(t){B(t),P()},W(t),[2]}))}))}))];case 1:return h.sent(),[2]}}))}))},t.prototype.drawText=function(t,e,i,n){var o=this.ctx,r=e.borderSize,s=e.contentSize,a=e.left,h=e.top,d=s.width,c=s.height,l=s.left-r.left,f=s.top-r.top,u=i.color,p=void 0===u?"#000000":u,g=i.lineHeight,v=void 0===g?"1.4em":g,y=i.fontSize,b=void 0===y?14:y,x=i.fontWeight,m=i.fontFamily,w=i.fontStyle,S=i.textAlign,z=void 0===S?"left":S,M=i.verticalAlign,B=void 0===M?xt:M,I=i.backgroundColor,k=i.lineClamp,P=i.backgroundClip,W=i.textShadow,R=i.textDecoration;if(this.drawView(e,i,P!=gt),v=j(v,b),t){switch(o.save(),this.setShadow({boxShadow:W}),a+=l,h+=f,o.setFonts({fontFamily:m,fontSize:b,fontWeight:x,fontStyle:w}),o.setTextBaseline(xt),o.setTextAlign(z),P?this.setBackground(I,d,c,a,h):o.setFillStyle(p),z){case wt:break;case St:a+=.5*d;break;case zt:a+=d}var O=n.lines*v,T=Math.ceil((c-O)/2);switch(T<0&&(T=0),B){case bt:break;case xt:h+=T;break;case mt:h+=2*T}var L=n.fontHeight,A=n.descent,F=(v-L)/2,C=function(t){var e=o.measureText(t),i=e.actualBoundingBoxDescent,n=void 0===i?0:i,r=e.actualBoundingBoxAscent,s=void 0===r?0:r,a=n+s||.7*b+1;return B==bt?{fix:s,height:a,lineY:v-a}:B==xt?{fix:v/2+n/4,height:a,lineY:(v-a)/2}:B==mt?{fix:v-n,height:a,lineY:0}:{fix:0,height:0,lineY:0}},E=function(t,e,i){var r=t;switch(z){case wt:r+=i;break;case St:r=(t-=i/2)+i;break;case zt:r=t,t-=i}if(R){o.setLineWidth(b/13),o.beginPath();var s=.1*n.fontHeight;/\bunderline\b/.test(R)&&(A||(e+=F/2),o.moveTo(t,e+s),o.lineTo(r,e+s)),/\boverline\b/.test(R)&&(A||(e-=F/2),o.moveTo(t,e-n.fontHeight-s),o.lineTo(r,e-n.fontHeight-s)),/\bline-through\b/.test(R)&&(o.moveTo(t,e-.5*n.fontHeight),o.lineTo(r,e-.5*n.fontHeight)),o.closePath(),o.setStrokeStyle(p),o.stroke()}};if(!n.widths||1==n.widths.length&&n.widths[0].total<=s.width){var H=C(t),U=H.fix,Y=H.lineY;return o.fillText(t,a,h+U),E(a,(h+=v)-Y,n&&n.widths&&n.widths[0].total||n.text),o.restore(),void this.setBorder(e,i)}for(var $=t.split(""),D=h,_=a,X="",N=0,V=0;V<=$.length;V++){var G=$[V]||"",q="\n"===G,J=""==G,Q=X+(G=q?"":G),Z=o.measureText(Q).width;if(N>=k)break;if(_=a,Z>s.width||q||J){if(N++,X=J&&Z<=s.width?Q:X,N===k&&Z>d){for(;o.measureText("".concat(X,"...")).width>s.width&&!(X.length<=1);)X=X.substring(0,X.length-1);X+="..."}var K=C(X);U=K.fix,Y=K.lineY;if(o.fillText(X,_,h+U),E(_,(h+=v)-Y,Z),X=G,h>D+c)break}else X=Q}o.restore()}},t.prototype.source=function(t){var e;return i(this,void 0,void 0,(function(){var i,o,r,s=this;return n(this,(function(n){switch(n.label){case 0:if(this.node=null,i=+new Date,"{}"==JSON.stringify(t))return[2];if(!t.type)for(o in t.type=yt,t.css=t.css||{},t)["views","children","type","css"].includes(o)||(t.css[o]=t[o],delete t[o]);return(null===(e=t.css)||void 0===e?void 0:e.width)||t.css||(t.css={}),[4,this.create(t)];case 1:return r=n.sent(),this.size=(null==r?void 0:r.layout())||{},this.node=r,this.onEffectFinished().then((function(t){return s.lifecycle("onEffectSuccess",t)})).catch((function(t){return s.lifecycle("onEffectFail",t)})),console.log("布局用时："+(+new Date-i)+"ms"),[2,this.size]}}))}))},t.prototype.getImageInfo=function(t){return this.imageBus[t]||(this.imageBus[t]=this.createImage(t,this.useCORS)),this.imageBus[t]},t.prototype.create=function(t,o){var r,s;return i(this,void 0,void 0,(function(){var i,a,h,d,c,l,f,u,p,g,v,y,b,x,m,w,z;return n(this,(function(n){switch(n.label){case 0:if(i=t.type==pt,a=[gt,vt].includes(t.type),h=t.css||{},d=h.backgroundImage,c=h.display,i&&!t.src&&!t.url||a&&!t.text)return[2];if(c==S)return[2];if(a&&(t.text=String(t.text)),!(i||t.type==yt&&d))return[3,4];l=i?t.src:"",f=/url\((.+)\)/,d&&(null===(r=f.exec(d))||void 0===r?void 0:r[1])&&(l=(null===(s=f.exec(d))||void 0===s?void 0:s[1])||""),n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.getImageInfo(l)];case 2:return u=n.sent(),p=u.width,g=u.height,!(v=u.path)&&i?[2]:(v&&(t.attributes=Object.assign(t.attributes||{},{width:p,height:g,path:v,src:v,naturalSrc:l})),[3,4]);case 3:return y=n.sent(),t.type!=yt?[2]:(this.lifecycle("onEffectFail",e(e({},y),{src:l})),[3,4]);case 4:if(this.count+=1,b=new ft(t,o,this.root,this.ctx),!(x=t.views||t.children))return[3,8];m=0,n.label=5;case 5:return m<x.length?(w=x[m],[4,this.create(w,b)]):[3,8];case 6:(z=n.sent())&&b.add(z),n.label=7;case 7:return m++,[3,5];case 8:return[2,b]}}))}))},t.prototype.drawNode=function(t,e){return void 0===e&&(e=!1),i(this,void 0,void 0,(function(){var i,o,r,s,a,h,d,c,l,f,u,p,g,v,y,b,x,m,w,S;return n(this,(function(n){switch(n.label){case 0:return i=t.layoutBox,o=t.computedStyle,r=t.attributes,s=t.name,a=t.children,h=t.fixedLine,d=t.attributes,c=d.src,l=d.text,["fixed"].includes(o.position)&&!e?[2]:(this.ctx.save(),s!==yt?[3,7]:c?o.backgroundRepeat?[4,this.drawPattern(r,i,o)]:[3,2]:[3,5]);case 1:return n.sent(),[3,4];case 2:return[4,this.drawImage(r,i,o,!1)];case 3:n.sent(),n.label=4;case 4:return[3,6];case 5:this.drawView(i,o),n.label=6;case 6:return[3,10];case 7:return s===pt&&c?[4,this.drawImage(r,i,o,!1)]:[3,9];case 8:return n.sent(),[3,10];case 9:s===gt?this.drawText(l,i,o,r):s===vt&&ut.api&&ut.api.draw(l,this,i,o),n.label=10;case 10:if(this.progress+=1,u=(f=h||{}).beforeElements,p=f.afterElements,!u)return[3,14];g=0,v=u,n.label=11;case 11:return g<v.length?(S=v[g],[4,this.drawNode(S)]):[3,14];case 12:n.sent(),n.label=13;case 13:return g++,[3,11];case 14:if(!a)return[3,18];y=Object.values?Object.values(a):Object.keys(a).map((function(t){return a[t]})),b=0,x=y,n.label=15;case 15:return b<x.length?"absolute"===(S=x[b]).computedStyle.position?[3,17]:[4,this.drawNode(S)]:[3,18];case 16:n.sent(),n.label=17;case 17:return b++,[3,15];case 18:if(!p)return[3,22];m=0,w=p,n.label=19;case 19:return m<w.length?(S=w[m],[4,this.drawNode(S)]):[3,22];case 20:n.sent(),n.label=21;case 21:return m++,[3,19];case 22:return this.ctx.restore(),[2]}}))}))},t.prototype.render=function(t){var e=this;return void 0===t&&(t=30),new Promise((function(o,r){return i(e,void 0,void 0,(function(){var e,i,s,a,h,d,c,l,f,u;return n(this,(function(n){switch(n.label){case 0:return e=+new Date,this.init(),[4,(p=t,void 0===p&&(p=0),new Promise((function(t){return setTimeout(t,p)})))];case 1:n.sent(),n.label=2;case 2:if(n.trys.push([2,14,,15]),!this.node)return[3,12];if(i=this.root.fixedLine||{},s=i.beforeElements,a=i.afterElements,!s)return[3,6];h=0,d=s,n.label=3;case 3:return h<d.length?(f=d[h],[4,this.drawNode(f,!0)]):[3,6];case 4:n.sent(),n.label=5;case 5:return h++,[3,3];case 6:return[4,this.drawNode(this.node)];case 7:if(n.sent(),!a)return[3,11];c=0,l=a,n.label=8;case 8:return c<l.length?(f=l[c],[4,this.drawNode(f,!0)]):[3,11];case 9:n.sent(),n.label=10;case 10:return c++,[3,8];case 11:return o(this.node),[3,13];case 12:this.lifecycle("onEffectFail","node is empty"),n.label=13;case 13:return[3,15];case 14:return u=n.sent(),this.lifecycle("onEffectFail",u),r(u),[3,15];case 15:return console.log("渲染用时："+(+new Date-e-30)+"ms"),[2]}var p}))}))}))},t.prototype.onEffectFinished=function(){var t=this,e=Object.keys(this.imageBus).map((function(e){return t.imageBus[e]}));return Promise.all(e)},t.prototype.destroy=function(){this.node=[]},t.prototype.save=function(t){try{var e=t||{},i=e.fileType,n=void 0===i?"png":i,o=e.quality,r=void 0===o?1:o;return this.canvas.toDataURL("image/".concat(n),r)}catch(t){return this.lifecycle("onEffectFail","image cross domain"),t}},t}();s.WEB==T&&(window.Painter=Mt),t.Painter=Mt,t.default=Mt,Object.defineProperty(t,"__esModule",{value:!0})}));
