<template>
	<view class="page">
		<view class="time"><text>支付剩余时间</text> <u-count-down :time="15 * 60 * 1000" format="mm:ss"></u-count-down>
		</view>
		<span class="price"><span>￥</span>{{allprice}}</span>
		<view class="payCard">
			<view class="left">
				<image src="../static/svg/weixinfang.svg" mode="aspectFill"></image>
				<text>微信支付</text>
			</view>
			<view class="choose" :style="currentIndex == 0?'background:#2E80FE;border:2rpx solid #2E80FE':''"
				@click="currentIndex = 0">
				<u-icon name="checkbox-mark" color="#fff" size="16"></u-icon>
			</view>
		</view>
		<view class="payCard" @click="chooseYh">
			<view class="left">
				优惠券
			</view>
			<view class="right">
				<text v-if="!couType">{{couponNum}}张可用</text>
				<text v-else>{{confirmCou.title}} -{{confirmCou.discount}}</text>
				<u-icon name="arrow-right" color="#999999" size="14"></u-icon>
			</view>
		</view>
		<!-- <view class="payCard">
			<view class="left">
				<image src="../static/svg/qianbao.svg" mode="aspectFill"></image>
				<text>余额支付</text>
			</view>
			<view class="choose" :style="currentIndex == 1?'background:#2E80FE;border:2rpx solid #2E80FE':''"
				@click="currentIndex = 1">
				<u-icon name="checkbox-mark" color="#fff" size="16"></u-icon>
			</view>
		</view> -->
		<view class="footer">
			<view class="btn" @click="confirmPay">确认支付</view>
		</view>
		<!-- 优惠券弹出框 -->
		<view class="choose_yh" :style="showYh?'':'height:0'">

			<view class="head">优惠券</view>
			<view class="close" @click="showYh = false">
				<image src="../static/images/9397.png" mode=""></image>
			</view>
			<u-empty mode="coupon" icon="http://cdn.uviewui.com/uview/empty/coupon.png"
				v-if="couponlist.length == 0 && nocouponlist.length == 0">
			</u-empty>
			<scroll-view scroll-y="true" style="height: 832rpx;" v-else>
				<view class="cou_item" v-for="(item,index) in couponlist" :key="index">
					<view class="top">
						<view class="box1" v-if="item.type == 0">
							<span>满</span>{{item.full}}<span>减</span>{{item.discount}}
						</view>
						<view class="box1" v-else><span>￥</span>{{item.discount}}</view>
						<view class="box2">
							<text>{{item.title}}</text>
							<span v-if="item.start_time == 0">有效期：自领券日起{{item.day}}天</span>
							<span v-else>有效期：{{item.startTime}}</span>
						</view>
						<view class="box3" :style="item.choose?'background:#2E80FE;border:2rpx solid #2E80FE':''"
							@click="chooseItemyh(item)">
							<u-icon name="checkbox-mark" color="#fff" size="16"></u-icon>
						</view>
					</view>
					<view class="bottom">
						{{item.rule}}
					</view>
				</view>
				<view class="noYh">
					<view class="left">不可使用优惠券</view>
					<view class="right" :style="notYh?'background:#2E80FE;border:2rpx solid #2E80FE':''"
						@click="chooseNotyh()"><u-icon name="checkbox-mark" color="#fff" size="16"></u-icon></view>
				</view>
				<view class="notcan">
					不可使用优惠券
				</view>
				<view class="cou_item" style="border: 2rpx solid #ADADAD;background: #fff;"
					v-for="(item,index) in nocouponlist" :key="index">
					<view class="top" style="border-bottom: 2rpx dashed #ADADAD;">
						<view class="box1" v-if="item.type == 0" style="color: #ADADAD ;">
							<span>满</span>{{item.full}}<span>减</span>{{item.discount}}
						</view>
						<view class="box1" style="color: #ADADAD ;"><span>￥</span>{{item.discount}}</view>
						<view class="box2">
							<view style="display: flex;  justify-content: space-between; align-items: center;" class="">
								<text style="color: #ADADAD ; ">{{item.title}}</text>
								<text v-if="item.available===0" style="color: #E72427 ; font-size: 25rpx;">{{item.availableText}}</text>
							</view>
							<span v-if="item.startTime == 0">有效期：自领券日起{{item.day}}天</span>
							<span v-else>有效期：{{item.startTime}}</span>
						</view>
					</view>
					<view class="bottom">
						{{item.rule}}
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentIndex: 0,
				id: '',
				infoyouhuij: '',
				price: '',
				showYh: false,
				couponlist: [], //可用优惠券列表
				nocouponlist: [], //不可用优惠券列表，
				couType: false, //是否选择优惠券
				confirmCou: null, //当前选中的优惠券，
				couponNum: '',
				goods_id: '',
				type: 0,
				tmplIds: 'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY',
			}
		},
		computed: {
			allprice() {
				const discount = this.confirmCou == null ? 0 : this.confirmCou.discount * 1;
				const result = this.price * 1 - discount;
				return result <= 0 ? 0.01 : Number(result.toFixed(2));
			}
		},
		methods: {
			// 检查当前平台
			getCurrentPlatform() {
				// #ifdef APP-PLUS
				return 'app-plus';
				// #endif
				// #ifdef MP-WEIXIN
				return 'mp-weixin';
				// #endif
				// #ifdef H5
				return 'h5';
				// #endif
				return 'unknown';
			},
			async getList() {
				console.log(111)
				await this.$api.service.myWelfare({
					status: 1,
					serviceId: this.goodsId,
					payPrice: this.allprice
				}).then(res => {
					let arr = []
					let arr1 = []
					res.data.list.forEach(item => {
						item.choose = false
						if (item.available === 1) {
							arr.push(item)
						} else {
							arr1.push(item)
						}
					})
					console.log(res)
					arr = [...new Set(arr)]
					arr1 = [...new Set(arr1)]
					this.couponlist = arr
					this.nocouponlist = arr1
					this.couponNum = arr.length
				})
			},
			chooseNotyh() { //点击不适用优惠券
				this.notYh = !this.notYh
				if (this.notYh) {
					this.couponlist.forEach(item => {
						item.choose = false
					})
				}
			},
			chooseYh() { //打开优惠券弹框
				this.showYh = true
			},
			chooseItemyh(item) {
				if (item.choose == false) {
					//判断当前是否符合使用条件
					if (item.type == 0) { //如果是满减券
						if (item.full * 1 > this.price * 1) {
							uni.showToast({
								icon: 'none',
								title: '当前金额未满足使用条件'
							})
							return
						}
					}
					this.couponlist.forEach(e => {
						e.choose = false
					})
					item.choose = true //显示对勾
					this.couType = true //当前已选优惠券
					this.confirmCou = item
				} else {
					item.choose = false
					this.couType = false
					this.confirmCou = null
				}
			},
			confirmPay() {
				// 获取当前平台
				const platform = this.getCurrentPlatform();
				console.log('当前平台:', platform);

				this.$api.service.nowPay({
					orderId: this.id,
					couponId: this.confirmCou ? this.confirmCou.couponId : 0,
					type: 1,
					// is_banlance: this.currentIndex
				}).then(res => {
					if(res.code==='-1'){
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}else{
						console.log(res)
						let obj = res.data
						let packageStr = "prepay_id=" + obj.prepayId;
						console.log(String(packageStr))
						console.log(obj.nonceStr)
						console.log(packageStr)
						console.log(obj.nonceStr)
						console.log(String(obj.timestamp))
						console.log(obj.sign)

						// 根据平台选择不同的支付方式
						if (platform === 'app-plus') {
							// APP环境使用微信支付
							console.log('APP环境，使用微信支付');
							this.handleAppWechatPay(obj);
						} else if (platform === 'mp-weixin') {
							// 微信小程序环境保持原有逻辑
							console.log('微信小程序环境，使用小程序支付');
							this.handleMiniProgramPay(obj);
						} else {
							// 其他环境（H5等）
							console.log('其他环境，使用默认支付方式');
							this.handleMiniProgramPay(obj);
						}
					}
				})
			},

			// APP微信支付处理
			handleAppWechatPay(obj) {
				console.log(111)
				uni.requestPayment({
					"provider": "wxpay",
					    orderInfo: 'orderInfo',
					orderInfo: {
						appid: obj.appId,
						noncestr: obj.nonceStr,
						package: 'Sign=WXPay',
						partnerid: obj.partnerId,
						prepayid: obj.prepayId,
						timestamp: String(obj.timestamp),
						sign: obj.sign
					},
					success: (res) => {
						console.log('APP微信支付成功', res);
						uni.showToast({
							title: '支付成功',
							icon: 'success'
						})
						// this.dingyue()
						setTimeout(() => {
							uni.redirectTo({
								url: '/user/order_list?tab=0'
							})
						}, 1000)
					},
					fail: (err) => {
						console.error('APP微信支付失败:', err);
						if (err.errMsg && err.errMsg.includes('cancel')) {
							uni.showToast({
								title: '您已取消支付',
								icon: 'none'
							});
						} else {
							uni.showToast({
								title: '支付失败，请稍后重试',
								icon: 'none'
							});
						}
					}
				});
			},

			// 微信小程序支付处理（保持原有逻辑）
			handleMiniProgramPay(obj) {
				const paymentParams = {
					timeStamp: String(obj.timestamp), // 一定要是 string
					nonceStr: obj.nonceStr,
					package: "prepay_id=" + obj.prepayId,
					signType: 'MD5',
					paySign: obj.sign
				};
				console.log(JSON.stringify(paymentParams));
				uni.requestPayment({
					"provider": 'wxpay',
					timeStamp: String(obj.timestamp),
					nonceStr: obj.nonceStr,
					package: "prepay_id=" + obj.prepayId,
					partnerid: obj.partnerId,
					signType: "MD5",
					paySign: obj.sign,
					appId: obj.appId,
					success: (res1) => {
						// 支付成功回调
						console.log('支付成功', res1);
						uni.showToast({
							title: '支付成功',
							icon: 'success'
						})
						this.dingyue()
						setTimeout(() => {
							uni.redirectTo({
								url: '/user/order_list?tab=0'
							})
						}, 1000)
					},
					fail: (err) => {
						// 支付失败回调
						console.error('requestPayment fail object:', err);
						console.error('requestPayment fail JSON:', JSON.stringify(err));
						if (err.errMsg.includes('fail cancel')) {
							uni.showToast({
								title: '您已取消支付',
								icon: 'none'
							});
						} else {
							uni.showToast({
								title: '支付失败，请稍后重试',
								icon: 'none'
							});
						}
						console.error('支付失败', err);
						uni.showToast({
							title: '支付失败请检查网络',
							icon: 'error'
						})
					},
				})
			},
			dingyue() {
				uni.requestSubscribeMessage({
					provider: 'weixin',
					tmplIds: this.tmplIds,
					success: (res) => {
						const anyAccepted = this.tmplIds.some(id => res[id] === 'accept');
						if (anyAccepted) {
							uni.showToast({
								title: '消息订阅成功',
								icon: 'success'
							});
						}
					},
					fail: (err) => {
						// uni.showToast({
						// 	title: '订阅失败，请稍后重试',
						// 	icon: 'none'
						// });
					}
				});
			},
			// async getDetail() {
			// 	const res = await this.$api.service.orderdet({
			// 		id: this.id
			// 	})
			// 	this.goods_id = res.order_goods[0].goods_id
			// }
		},
		async onLoad(options) {
			this.infoyouhuij = this.$store.state.service.orderInfo
			console.log(this.infoyouhuij)
			console.log(options)
			this.id = options.id
			this.price = options.price
			this.type = options.type
			this.goodsId = options.goodsId
			this.getList()
			// await this.getDetail()
		}
	}
</script>

<style scoped lang="scss">
	.page {
		background-color: #f8f8f8;
		height: 100vh;
		padding-top: 40rpx;

		.choose_yh {
			padding-top: 40rpx;
			width: 750rpx;
			height: 1106rpx;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 0rpx 0rpx;
			opacity: 1;
			position: fixed;
			bottom: 0;
			z-index: 10088;
			transition: all 0.5s;

			.head {
				font-size: 32rpx;
				font-weight: 500;
				color: #171717;
				text-align: center;
				margin-bottom: 44rpx;
			}

			.close {
				position: absolute;
				top: 44rpx;
				right: 32rpx;

				image {
					width: 37rpx;
					height: 37rpx;
				}
			}

			.cou_item {
				margin: 0 auto;
				width: 690rpx;
				height: 202rpx;
				background: #DCEAFF;
				border-radius: 20rpx 20rpx 20rpx 20rpx;
				margin-bottom: 20rpx;
				border: 2rpx solid #2E80FE;

				.top {
					height: 150rpx;
					display: flex;
					align-items: center;
					padding-top: 26rpx;
					padding-left: 24rpx;
					padding-right: 14rpx;
					position: relative;
					border-bottom: 2rpx dashed #2E80FE;

					.box1 {
						text-align: center;
						width: 185rpx;
						font-size: 40rpx;
						font-weight: 500;
						color: #E72427;

						span {
							font-size: 15rpx;
						}
					}

					.box2 {
						margin-left: 28rpx;

						text {
							display: block;
							font-size: 32rpx;
							font-weight: 500;
							color: #171717;
							max-width: 450rpx;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}

						span {
							margin-top: 10rpx;
							font-size: 24rpx;
							font-weight: 400;
							color: #B2B2B2;
						}
					}

					.box3 {
						position: absolute;
						right: 22rpx;
						top: 40rpx;
						width: 40rpx;
						height: 40rpx;
						background: #fff;
						border: 2rpx solid #B2B2B2;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}

				.bottom {
					padding: 0 24rpx;
					height: 50rpx;
					max-width: 500rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					line-height: 50rpx;
					font-size: 20rpx;
					font-weight: 400;
					color: #B2B2B2;
				}
			}

			.noYh {
				width: 690rpx;
				margin: 0 auto;
				margin-top: 52rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 22rpx;

				.left {
					font-size: 32rpx;
					font-weight: 500;
					color: #171717;
				}

				.right {
					width: 40rpx;
					height: 40rpx;
					background: #fff;
					border: 2rpx solid #B2B2B2;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

			.notcan {
				margin-top: 52rpx;
				margin-bottom: 20rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: #B2B2B2;
				padding: 0 30rpx;
			}
		}

		.time {
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			font-weight: 500;
			color: #333333;

			text {
				margin-right: 15rpx;
			}
		}

		.price {
			display: inline-block;
			width: 750rpx;
			text-align: center;
			margin-top: 20rpx;
			font-size: 80rpx;
			font-weight: 500;
			color: #292C39;

			span {
				font-size: 36rpx;
			}
		}

		.payCard {
			margin: 0 auto;
			width: 686rpx;
			height: 130rpx;
			background: #FFFFFF;
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			margin-top: 40rpx;
			padding: 0 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.left {
				display: flex;
				align-items: center;

				image {
					width: 70rpx;
					height: 70rpx;
				}

				text {
					margin-left: 20rpx;
					font-size: 32rpx;
					font-weight: 500;
					color: #171717;
				}
			}

			.right {
				display: flex;
				align-items: center;

				text {
					font-size: 24rpx;
					font-weight: 400;
					color: #E72427;
				}
			}

			.choose {
				width: 40rpx;
				height: 40rpx;
				background-color: #fff;
				border: 2rpx solid #ADADAD;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.footer {
			width: 750rpx;
			height: 192rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: fixed;
			bottom: 0;
			// background-color: #FFFFFF;

			.btn {
				width: 686rpx;
				height: 88rpx;
				background: #2E80FE;
				border-radius: 44rpx 44rpx 44rpx 44rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 88rpx;
				text-align: center;
			}
		}
	}
</style>