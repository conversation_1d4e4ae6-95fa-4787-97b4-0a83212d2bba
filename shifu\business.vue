<template>
	<view class="page">
		<view class="header">邀请好友  赚奖励</view>
		<view class="box">
			<view class="name">{{info.name || ''}}</view>
			<!-- <view class="desc">邀请你注册师傅到家，扫码立刻注册</view> -->
			<view class="desc">每邀请一位师傅和商家入驻，师傅、商家订单验收完结后，邀请人每单可获得1%的奖励。</view>
			<image :src="imgUrl" mode=""></image>
			<view class="invite-code-container">
				<view class="invite-code">邀请码: {{inviteCode || '无'}}</view>
				<!-- <view class="copy-btn" @click="copyInviteCode">复制</view> -->
			</view>
		</view>
		<view class="button-container">
			<view class="btn save-btn" @click="saveimg">保存图片</view>
			<button class="btn share-btn" open-type="share" :disabled="!imgUrl">分享</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			imgUrl: '',
			infos:'',
			inviteCode:'',
			info: {}
		}
	},
	created() {
		//#ifdef MP-WEIXIN
		wx.showShareMenu({
			withShareTicket: true,
			menus: ['shareAppMessage', 'shareTimeline'],
			success: () => {
				console.log('Share menu enabled');
			},
			fail: (e) => {
				console.error('Failed to enable share menu:', e);
			}
		});
		//#endif
	},
	onShareAppMessage(res) { // Customize share content
		const inviteCode = this.inviteCode || '';
		const shareData = {
			title: '每邀请一位师傅和商家入驻，师傅、商家订单验收完结后，邀请人每单可获得1%的奖励！',
			path: `/pages/mine?inviteCode=${inviteCode}`,
			imageUrl: this.imgUrl || ''
		};
		console.log('Sharing with:', shareData);
		return shareData;
	},
	methods: {
		// 优化后的保存图片方法
		saveimg() {
			const that = this;
			
			if (!this.imgUrl) {
				uni.showToast({
					title: '图片未加载',
					icon: 'none'
				});
				return;
			}
			
			// 检查授权状态
			uni.getSetting({
				success: function(res) {
					if (res.authSetting['scope.writePhotosAlbum']) {
						// 已授权，直接下载保存
						that.downloadAndSave();
					} else if (res.authSetting['scope.writePhotosAlbum'] === undefined) {
						// 首次请求，直接下载保存（会自动弹出授权）
						that.downloadAndSave();
					} else {
						// 用户曾经拒绝过授权，引导用户手动开启
						uni.showModal({
							title: '授权提示',
							content: '需要您授权保存图片到相册，请在设置中开启相册权限',
							showCancel: true,
							confirmText: '去设置',
							cancelText: '取消',
							success: function(modalRes) {
								if (modalRes.confirm) {
									uni.openSetting({
										success: function(settingRes) {
											if (settingRes.authSetting['scope.writePhotosAlbum']) {
												that.downloadAndSave();
											} else {
												uni.showToast({
													title: '未开启相册权限',
													icon: 'none'
												});
											}
										},
										fail: function(error) {
											console.log(error, 'openSetting');
											uni.showToast({
												title: '打开设置失败',
												icon: 'none'
											});
										}
									});
								} else {
									uni.showToast({
										title: '取消授权无法保存',
										icon: 'none'
									});
								}
							}
						});
					}
				},
				fail: function(error) {
					console.log(error, 'getSetting');
					uni.showToast({
						title: '获取授权状态失败',
						icon: 'none'
					});
				}
			});
		},
		
		// 下载并保存图片的具体实现
		downloadAndSave() {
			const that = this;
			
			// 显示加载提示
			uni.showLoading({
				title: '保存中...',
				mask: true
			});
			
			uni.downloadFile({
				url: this.imgUrl,
				success: res => {
					if (res.statusCode === 200) {
						uni.saveImageToPhotosAlbum({
							filePath: res.tempFilePath,
							success: () => {
								uni.hideLoading();
								uni.showToast({
									title: '保存成功',
									icon: 'success'
								});
							},
							fail: (e) => {
								uni.hideLoading();
								console.error('Save image failed:', e);
								
								// 根据错误类型给出不同提示
								let errorMsg = '保存失败';
								if (e.errMsg && e.errMsg.includes('auth deny')) {
									errorMsg = '保存失败，请检查相册权限';
								}
								
								uni.showToast({
									title: errorMsg,
									icon: 'none'
								});
							}
						});
					} else {
						uni.hideLoading();
						console.error('Download failed:', res);
						uni.showToast({
							title: '图片下载失败',
							icon: 'none'
						});
					}
				},
				fail: (e) => {
					uni.hideLoading();
					console.error('Download error:', e);
					
					// 根据错误类型给出不同提示
					let errorMsg = '图片下载失败';
					if (e.errMsg && e.errMsg.includes('network')) {
						errorMsg = '网络连接失败';
					} else if (e.errMsg && e.errMsg.includes('timeout')) {
						errorMsg = '下载超时';
					}
					
					uni.showToast({
						title: errorMsg,
						icon: 'none'
					});
				}
			});
		},
		
		getImg() {
			this.$api.service.getUserQr().then(res => {
				console.log('QR code fetched:', res);
				this.imgUrl = res.qrUrl;
				this.inviteCode=res.qrCode;
			}).catch(e => {
				console.error('Failed to fetch QR code:', e);
				uni.showToast({
					title: '获取二维码失败',
					icon: 'none'
				});
			});
		},
		copyInviteCode() {
			if (!this.info.inviteCode) {
				uni.showToast({
					title: '无邀请码',
					icon: 'none'
				});
				return;
			}
			uni.setClipboardData({
				data: this.info.inviteCode,
				success: () => {
					uni.showToast({
						title: '复制成功',
						icon: 'none'
					});
				},
				fail: (e) => {
					console.error('Copy failed:', e);
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					});
				}
			});
		}
	},
	onLoad() {
		//   this.$api.user.userInfo().then(res=>{
		// 	 this.infos=res
		//  })
		//  // this.infos=JSON.parse(infodata)
		//  console.log( this.infos)
		// this.info = uni.getStorageSync('userInfo') || {};
		console.log('User info loaded:', this.info);
		this.getImg();
	}
}
</script>

<style scoped lang="scss">
	.page {
		background: #f8f8f8;
		height: 100vh;
		padding: 40rpx 30rpx;

		.header {
			text-align: center;
			font-size: 52rpx;
			font-weight: 600;
			color: #000000;
		}

		.box {
			margin-top: 40rpx;
			width: 690rpx;
			height: 748rpx;
			background: #FFFFFF;
			border-radius: 32rpx;
			padding: 40rpx 0;
			display: flex;
			flex-direction: column;
			align-items: center;

			.name {
				font-size: 32rpx;
				font-weight: 600;
				color: #000000;
				text-align: center;
			}

			.desc {
				margin-top: 20rpx;
				font-size: 24rpx;
				text-align: center;
				font-weight: 400;
				color: #000000;
			}

			image {
				width: 444rpx;
				height: 444rpx;
				margin: 18rpx auto 0;
			}

			.invite-code-container {
				display: flex;
				align-items: center;
				justify-content: center;
				margin-top: 20rpx;
				gap: 20rpx;
			}

			.invite-code {
				font-size: 28rpx;
				font-weight: 400;
				color: #000000;
			}

			.copy-btn {
				width: 120rpx;
				height: 60rpx;
				line-height: 60rpx;
				text-align: center;
				background: #2E80FE;
				color: #FFFFFF;
				font-size: 24rpx;
				border-radius: 30rpx;
			}
		}

		.button-container {
			display: flex;
			gap: 20rpx;
			position: absolute;
			bottom: 42rpx;
			width: 690rpx;
		}

		.btn {
			flex: 1;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 50rpx;
			line-height: 98rpx;
			text-align: center;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
		}

		.save-btn {
			/* Specific styles for save button if needed */
		}

		.share-btn {
			/* Ensure button inherits same styles */
			border: none;
			padding: 0;
			margin: 0;
			background: #2E80FE;
			/* Remove default button styles */
			&:after {
				border: none;
			}
		}

		.share-btn[disabled] {
			background: #cccccc;
			pointer-events: none;
		}
	}
</style>