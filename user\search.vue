<template>
	<view class="page">
		<view class="search_position">
			<uni-icons type="search" size="20" color="#ADADAD"></uni-icons>
			<input type="text" placeholder="空调维修" v-model="value" @input="handleInput">
			<view class="btn" @click="handleSearch">搜索</view>
		</view>
		<view class="search_main">
			<view class="search_item" v-for="(item, index) in searchList" :key="index" @click="chooseOne(item)">{{ item.title }}</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			list: [],
			searchList: [],
			value: ''
		}
	},
	methods: {
		// Custom debounce function
		debounce(fn, delay) {
			let timer = null;
			return function (...args) {
				if (timer) clearTimeout(timer);
				timer = setTimeout(() => {
					fn.apply(this, args);
				}, delay);
			};
		},
		handleSearch() {
			this.searchList = [];
			if (this.value.trim()) {
				this.searchList = this.list.filter(item => 
					item.title.toLowerCase().includes(this.value.toLowerCase())
				);
			}
		},
		handleInput() {
			// Debounced search to reduce frequent updates
			this.debounce(this.handleSearch, 300).call(this);
		},
		chooseOne(item) {
			uni.navigateTo({
				url: `/user/commodity_details?id=${item.id}`
			});
		},
		getList() {
			let city_id = uni.getStorageSync('city');
			if (!city_id?.position) {
				console.error('City ID not found');
				return;
			}
			this.$api.service.serviceCate(city_id.position).then(res => {
				this.list = [];
				res.forEach(item => {
					if (item.children) {
						item.children.forEach(e => {
							if (e.serviceList) {
								this.list = [...this.list, ...e.serviceList];
							}
						});
					}
				});
				console.log('List:', this.list);
			}).catch(err => {
				console.error('API error:', err);
			});
		}
	},
	onLoad() {
		this.getList();
	}
}
</script>

<style scoped lang="scss">
.page {
	.search_position {
		margin: 0 auto;
		margin-top: 24rpx;
		width: 690rpx;
		height: 72rpx;
		background: #F1F1F1;
		border-radius: 36rpx 36rpx 36rpx 36rpx;
		display: flex;
		align-items: center;
		padding-left: 40rpx;
		padding-right: 20rpx;
		position: relative;
		
		input {
			margin-left: 22rpx;
			font-size: 28rpx;
			font-weight: 400;
			color: #ADADAD;
			width: 260rpx;
		}
	
		.btn {
			width: 112rpx;
			height: 56rpx;
			background: #2E80FE;
			border-radius: 28rpx 28rpx 28rpx 28rpx;
			font-size: 28rpx;
			font-weight: 400 discriminators;
			color: #FFFFFF;
			line-height: 56rpx;
			text-align: center;
			position: absolute;
			right: 20rpx;
		}
	}
	.search_main {
		.search_item {
			height: 120rpx;
			border-bottom: 2rpx solid #E9E9E9;
			display: flex;
			align-items: center;
			font-size: 28rpx;
			font-weight: 400;
			color: #333333;
			padding: 0 30rpx;
		}
	}
}
</style>