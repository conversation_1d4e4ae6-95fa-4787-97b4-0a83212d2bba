<template>
	<view class="page">
		<view class="header">
			<u-search placeholder="请输入城市名称" v-model="keyword" :show-action="false" @input="searchCity"></u-search>
			<view class="nowP">
				<view class="left">
					<u-icon name="map-fill"></u-icon>
					<text style="margin-left: 16rpx;margin-right: 12rpx;">{{position}}</text>
					<span>当前定位</span>
				</view>
				<view class="right" @tap="getPositionAgain">
					重新定位
				</view>
			</view>
		</view>
		<view class="main">
			<u-index-list :index-list="indexList" v-if="keyword == '' && flag">
				<!-- <view class="head">
					
					<view class="hot">
						<text>热门城市</text>
						<view class="hot-box">
							<view class="box-item" v-for="(item,index) in hotList" :key="index">
								{{item.cityname}}
							</view>
						</view>
					</view>
				</view> -->
				<template v-for="(item, index) in itemArr" >
					<!-- #ifdef APP-NVUE -->
					<u-index-anchor :text="indexList[index]"></u-index-anchor>
					<!-- #endif -->
					<u-index-item>
						<!-- #ifndef APP-NVUE -->
						<u-index-anchor :text="indexList[index]"></u-index-anchor>
						<!-- #endif -->
						<view class="list-cell" v-for="(cell, j) in item" :key="j" @click="handlePosition(cell)">
							{{cell.true_name}}
						</view>
					</u-index-item>
				</template>
			</u-index-list>
			<view class="city_box" v-else>
				<view class="box_item" v-for="(item,index) in searchArr" :key="index"
					style="height: 100rpx;line-height: 100rpx;padding: 0 30rpx;" @click="handlePosition(item)">
					{{item.true_name}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import pinyin from 'js-pinyin'
	export default {
		data() {
			return {
				flag: false,
				city_id: '',
				position: '',
				keyword: '',
				indexList: ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S",
					"T", "U",
					"V", "W", "X", "Y", "Z"
				],
				itemArr: [
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[],
					[]
				],
				pxArr: [],
				searchArr: [],
				hotList: []
			}
		},
		methods: {
			searchCity() { //搜索城市
				this.searchArr = []
				if (this.keyword == '') return
				this.pxArr.forEach(item => {
					if (item.true_name.includes(this.keyword)) {
						this.searchArr.push(item)
					}
				})
			},
			async handlePosition(e) {
				console.log(e)
				await this.$api.service.selectCity({
					cityname: e.true_name,
					city_id: e.id
				})
				uni.setStorageSync('city', {
					id: e.id,
					position: e.true_name
				});
				this.position = e.true_name
				this.city_id = e.id
				this.keyword = ''
			},
			getPositionAgain() {
				uni.showToast({
					icon: 'loading',
					duration: 2000,
					title: '定位中'
				})
				this.getNowPosition()
			},
			getFocus() { //输入框获得焦点
				uni.navigateTo({
					url: '/pages/city_search/city_search'
				})
			},
			getNowPosition() { //当前位置
				let that = this
				uni.getLocation({
					type: 'gcj02',
					isHighAccuracy: true,
					accuracy: 'best',
					success: function(res) {
						uni.request({
							url: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,
							success: function(res1) {
								let province = res1.data.regeocode.addressComponent.province
								that.position = typeof res1.data.regeocode.addressComponent.city ==
									'string' ? res1.data.regeocode.addressComponent.city : res1
									.data.regeocode.addressComponent.province
								// that.$api.service.getCity(0).then(res2 => {
								// 	let index = res2.findIndex(item => item.true_name ==
								// 		province)
								// 	let province_id = res2[index].id
								// 	that.$api.service.getCity(province_id).then(res3 => {
								// 		let index2 = res3.findIndex(e => e
								// 			.true_name == that.position)
								// 		that.city_id = [res3[index2].id]
								// 		that.$api.service.index(that.city_id).then(
								// 			res4 => {
								// 				that.bannerList = res4.banner
								// 				that.list1 = res4.banner.map(
								// 					item => {
								// 						return item.img
								// 					})
								// 				that.baseList = res4.jingang
								// 				that.text1 = res4.notice.map(
								// 					item => {
								// 						return item.content
								// 					})
								// 				that.service = res4
								// 					.service_cate
								// 			})
								// 	})
								// })
								// let data = {
								// 	city: that.position,
								// 	lng: res.longitude,
								// 	lat: res.latitude
								// }
								// uni.setStorageSync('city',{city_id:that.city_id,position:that.position})
							}
						})
					}
				})
			},
			// getAllCity() {
			// 	uni.request({
			// 		url: 'https://restapi.amap.com/v3/config/district?keywords=中国&subdistrict=2&key=4272f5716dfd17882409f306c0299666',
			// 		success: (res) => {
			// 			res.data.districts[0].districts.forEach(item => {
			// 				item.districts.forEach(e => {
			// 					this.pxArr.push(e.name)
			// 				})
			// 			})
			// 			this.chageABC(this.pxArr)
			// 		}
			// 	})
			// },
			getCity() {
				this.$api.service.allCity().then(res => {
					res.forEach(item => {
						item.children.forEach(e => {
							this.pxArr.push(e)
						})
					})
					this.chageABC(this.pxArr)
				})
			},
			getHotCity() {
				this.$api.service.hotCity().then(res => {
					if (res.length > 9) {
						this.hotList = res.slice(0, 9)
					} else {
						this.hotList = res
					}
				})
			},
			chageABC(arr) {
				arr.map(item => {
					const key = pinyin.getFullChars(item.true_name).charAt(0)
					let index = this.indexList.findIndex(e => e == key)
					this.itemArr[index].push(item)
				})
				this.flag = true
			}

		},
		onLoad() {
			// this.city_id = uni.getStorageSync('city').id;
			// this.position = uni.getStorageSync('city').position
			this.getCity()
			// this.getAllCity()
			this.getHotCity()
			// this.getPosition()
			pinyin.setOptions({
				checkPolyphone: false,
				charCase: 0
			});
		},
		onReady() {
			console.log(this.itemArr, this.indexList);
		},
		// onUnload() {
		// 	uni.$emit('confirmPosition', {
		// 		position: this.position,
		// 		city_id: this.city_id
		// 	});
		// },
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100%;

		.header {
			padding: 24rpx 30rpx;
			background-color: #fff;

			.nowP {
				height: 40rpx;
				margin-top: 20rpx;
				display: flex;
				justify-content: space-between;

				.left {
					display: flex;
					align-items: center;

					span {
						font-weight: 400;
						color: #999999;
						font-size: 20rpx;
					}
				}

				.right {
					color: #2E80FE;
					font-weight: 400;
					font-size: 28rpx;
				}
			}
		}

		.main {
			/deep/ .u-index-list__letter {
				top: 140px !important;
			}



			.head {
				height: 400rpx;
				padding: 20rpx 30rpx;
				background-color: #F8F8F8;

				.last {
					height: 140rpx;

					text {
						font-size: 24rpx;
						font-weight: 400;
						color: #999999;
					}

					.last-box {
						display: flex;
						flex-wrap: wrap;

						.box-item {
							width: fit-content;
							min-width: 210rpx;
							height: 68rpx;
							margin: 20rpx 7rpx;
							background: #FFFFFF;
							line-height: 68rpx;
							text-align: center;
						}
					}
				}

				.hot {
					height: 260rpx;

					text {
						font-size: 24rpx;
						font-weight: 400;
						color: #999999;
						margin-bottom: 20rpx;
					}

					.hot-box {
						display: flex;
						flex-wrap: wrap;

						.box-item {
							padding: 0 10rpx;
							width: fit-content;
							min-width: 210rpx;
							margin: 20rpx 7rpx;
							background: #FFFFFF;
							line-height: 68rpx;
							text-align: center;
						}
					}
				}

			}

			.list-cell {
				display: flex;
				box-sizing: border-box;
				width: 100%;
				padding: 10px 24rpx;
				overflow: hidden;
				color: #323233;
				font-size: 14px;
				line-height: 24px;
				background-color: #fff;
			}
		}
	}
</style>