import {
	req
} from '../../utils/req.js';
export default {
	// 申请技师
	coachApply(param) {
		return req.post("/massage/app/IndexUser/coachApply", param)
	},
	// 技师信息
	coachInfo(param) {
		return req.get("/massage/app/IndexUser/coachInfo", param)
	},
	// 编辑技师
	coachUpdate(param) {
		return req.post("/massage/app/IndexCoach/coachUpdate", param)
	},
	// 编辑技师
	coachUpdateV2(param) {
		return req.post("/massage/app/IndexCoach/coachUpdateV2", param)
	},
	// 技师首页
	coachIndex(param) {
		return req.get("/massage/app/IndexCoach/coachIndex", param)
	},
	// 技师报警
	police(param) {
		return req.post("/massage/app/IndexCoach/police", param)
	},
	// 订单列表
	orderList(param) {
		return req.get("/massage/app/IndexCoach/orderList", param)
	},
	// 订单详情
	orderInfo(param) {
		return req.get("/massage/app/IndexCoach/orderInfo", param)
	},
	// 修改订单状态(type,order_id)
	updateOrder(param) {
		return req.post("/massage/app/IndexCoach/updateOrder", param)
	},
	//佣金信息
	capCashInfo(param) {
		return req.get("/massage/app/IndexCoach/capCashInfo", param)
	},
	//佣金信息（车费）
	capCashInfoCar(param) {
		return req.get("/massage/app/IndexCoach/capCashInfoCar", param)
	},
	//申请提现 (apply_price,text,type：1服务费提现，2车费提现)
	applyWallet(param) {
		return req.post("/massage/app/IndexCoach/applyWallet", param)
	},
	//提现记录
	capCashList(param) {
		return req.get("/massage/app/IndexCoach/capCashList", param)
	},
}
