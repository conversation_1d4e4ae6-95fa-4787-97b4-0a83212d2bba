<template>
	<view class="page">
		<view class="main">
			<u-picker :show="showCity" ref="uPicker" :loading="loading" :columns="columnsCity" @change="changeHandler"
				keyName="title" @cancel="showCity = false" @confirm="confirmCity"></u-picker>
			<view class="main_item">
				<view class="title"><span>*</span>姓名</view>
				<input type="text" v-model="form.coach_name" placeholder="请输入姓名">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>手机号</view>
				<input type="text" v-model="form.mobile" placeholder="请输入手机号">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>性别</view>
				<u-radio-group v-model="form.sex" placement="row">
					<u-radio :customStyle="{marginRight: '20px'}" label="男" :name="0"></u-radio>
					<u-radio label="女" :name="1"></u-radio>
				</u-radio-group>
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>从业年份</view>
				<input type="text" v-model="form.work_time" placeholder="请输入从业年份">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>所在地址</view>
				<input type="text" v-model="form.address" placeholder="请输入所在地址">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>选择区域</view>
				<input type="text" v-model="form.city" placeholder="请选择代理区域" disabled @click="showCity = true">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>自我介绍</view>
				<input type="text" v-model="form.text" placeholder="请输入自我介绍">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>身份证号</view>
				<input type="text" v-model="form.id_code" placeholder="请输入身份证号">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>上传身份证照片</view>
				<view class="card">
					<view class="card_item">
						<view class="top">
							<view class="das">
								<view class="up">
									<upload @upload="imgUpload" :imagelist="form.id_card1" imgtype="id_card1"
										imgclass="id_card_box" text="身份证人像面" :imgsize="1"></upload>
								</view>
							</view>
						</view>
						<view class="bottom">拍摄人像面</view>
					</view>
					<view class="card_item">
						<view class="top">
							<view class="das">
								<view class="up">
									<upload @upload="imgUpload" :imagelist="form.id_card2" imgtype="id_card2"
										imgclass="id_card_box" text="身份证国徽面" :imgsize="1"></upload>
								</view>
							</view>
						</view>
						<view class="bottom">拍摄国徽面</view>
					</view>
				</view>
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>上传形象照片</view>
				<upload @upload="imgUpload" @del="imgUpload" :imagelist="form.self_img" imgtype="self_img" imgclass=""
					text="形象照片" :imgsize="3"></upload>
			</view>
		</view>
		<view class="footer">
			<view class="btn" @click="submit">保存</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				showCity: false,
				loading: false,
				columnsCity: [
					[],
					[],
					[]
				],
				form: {
					id:'',
					coach_name: '',
					mobile: '',
					sex: 0,
					work_time: '',
					address: '',
					lng:'',
					lat:'',
					text: '',
					city:'',
					city_id:'',
					id_code: '',
					id_card1: [],
					id_card2: [],
					self_img: []
				}
			}
		},
		methods: {
			getcity(e) {
				this.$api.service.getCity(e).then(res => {
					this.columnsCity[0] = res
					this.$api.service.getCity(res[0].id).then(res1 => {
						this.columnsCity[1] = res1
						this.$api.service.getCity(res1[0].id).then(res2 => {
							this.columnsCity[2] = res2
						})
					})
				})
			},
			changeHandler(e) {
				const {
					columnIndex,
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPicker
				} = e
				if (columnIndex === 0) {
					this.$api.service.getCity(this.columnsCity[0][index].id).then(res => {
						picker.setColumnValues(1, res)
						this.columnsCity[1] = res
						this.$api.service.getCity(res[0].id).then(res1 => {
							picker.setColumnValues(2, res1)
							this.columnsCity[2] = res1
						})
					})
				} else if (columnIndex === 1) {
					this.$api.service.getCity(this.columnsCity[1][index].id).then(res => {
						picker.setColumnValues(2, res)
						this.columnsCity[2] = res
					})
				}
			},
			confirmCity(Array) {
				this.form.city = Array.value.map((item, index) => {
					if (item == undefined) {
						return this.columnsCity[index][0].title
					} else {
						return item.title
					}
				}).join('-')
				this.form.city_id = Array.value.map((e, j) => {
					if (e == undefined) {
						this.columnsCity[j][0].id
					} else {
						return e.id
					}
				})
				this.showCity = false
			},
			submit() { //点击保存
			console.log(this.form);
				for (let key in this.form) {
					if (this.form[key] === '' && !(key == 'lng'||key == 'lat')) {
						uni.showToast({
							icon: 'none',
							title: '请填写完整提交'
						})
						return

					} else if (typeof this.form[key] == 'object' && this.form[key].length == 0) {
						uni.showToast({
							icon: 'none',
							title: '请填写完整提交'
						})
						return
					}
				}
				let p = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
				if (p.test(this.form.id_code) == false) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的身份证号'
					})
					return
				}
				let phoneReg = /^1[3456789]\d{9}$/
				if (!phoneReg.test(this.form.mobile)) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的手机号',
						duration: 1000
					})
					return
				}
				let obj = JSON.parse(JSON.stringify(this.form))
				obj.id_card = [obj.id_card1[0].path, obj.id_card2[0].path]
				obj.self_img = [obj.self_img[0].path]
				obj.city_id = obj.city_id.map(item=>{return item.toString()})
				delete obj.id_card1
				delete obj.id_card2
				this.$api.service.saveMasterInfo(obj).then(res => {
					uni.showToast({
						icon: 'success',
						title: '保存成功'
					})
					setTimeout(() => {
						uni.navigateBack()
					}, 1000)
				})
			},
			imgUpload(e) {
				let {
					imagelist,
					imgtype
				} = e;
				this.form[imgtype] = imagelist;
			},
			getInfo() {
				this.$api.service.masterInfo().then(res => {
					let obj = res
					obj.id_card1 = [{
						path: obj.id_card[0]
					}]
					obj.id_card2 = [{
						path: obj.id_card[1]
					}]
					obj.self_img = [{
						path: obj.self_img[0]
					}]
					for (let key in this.form) {
						this.form[key] = obj[key]
					}
					this.form.city_id = this.form.city_id.split(',')
				})
			}
		},
		onLoad() {
			this.getcity(0)
			this.getInfo()
		}
	}
</script>

<style scoped lang="scss">
	.page {
		padding-bottom: 200rpx;

		.header {
			width: 750rpx;
			height: 58rpx;
			background: #FFF7F1;
			line-height: 58rpx;
			text-align: center;
			font-size: 28rpx;
			font-weight: 400;
		}

		.main {
			padding: 40rpx 30rpx;

			.main_item {
				margin-bottom: 20rpx;

				.title {
					margin-bottom: 20rpx;
					font-size: 28rpx;
					font-weight: 400;
					color: #333333;

					span {
						color: #E72427;
					}
				}

				input {
					width: 690rpx;
					height: 110rpx;
					background: #F8F8F8;
					font-size: 28rpx;
					font-weight: 400;
					line-height: 110rpx;
					padding: 0 40rpx;
					box-sizing: border-box;
				}

				.card {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.card_item {
						width: 332rpx;
						height: 332rpx;
						background: #F2FAFE;
						border-radius: 16rpx 16rpx 16rpx 16rpx;
						overflow: hidden;

						.top {
							height: 266rpx;
							width: 100%;
							padding-top: 40rpx;

							.das {
								margin: 0 auto;
								width: 266rpx;
								height: 180rpx;
								border: 2rpx dashed #2E80FE;
								padding-top: 28rpx;

								.up {
									margin: 0 auto;
									width: 210rpx;
									height: 130rpx;
								}
							}
						}

						.bottom {
							height: 66rpx;
							width: 100%;
							background-color: #2E80FE;
							font-size: 28rpx;
							font-weight: 400;
							color: #FFFFFF;
							text-align: center;
							line-height: 66rpx;
						}
					}
				}
			}
		}

		.footer {
			padding: 52rpx 30rpx;
			width: 750rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
			position: fixed;
			bottom: 0;

			.btn {
				width: 690rpx;
				height: 98rpx;
				background: #2E80FE;
				border-radius: 50rpx 50rpx 50rpx 50rpx;
				font-size: 32rpx;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 98rpx;
				text-align: center;
			}
		}
	}
</style>