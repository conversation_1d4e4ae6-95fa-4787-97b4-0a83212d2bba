<template>
	<view class="page">
		<view class="header">
			<view class="header_item" v-for="(item, index) in list" :key="index" @click="handleHeader(item)">
				<view :style="currentIndex === item.value ? 'color:#2E80FE;' : ''">{{ item.name }}</view>
				<view class="blue" :style="currentIndex === item.value ? '' : 'background-color:#fff;'"></view>
			</view>
		</view>

		<u-empty mode="order" icon="http://cdn.uviewui.com/uview/empty/order.png" v-if="orderList.length === 0">
		</u-empty>

		<view @click="dingyue()" class="main">
			<view v-for="(item, index) in orderList" :key="index">
				<view class="main_item" v-if="item.payType >= -1 "
					@click="item.type !== 5 ? goUrl(`/user/order_details?id=${item.id}`) : null">
					<view class="head">
						<view class="no">单号：{{ item.orderCode }}</view>
						<view class="type">{{ item.payType === -1 ? '已取消' : pay_typeArr[parseInt(item.payType)] }}
						</view>
					</view>
					<view class="mid">
						<view class="lef">
							<image :src="item.goodsCover" mode=""></image>
							<text>{{ item.goodsName }}</text>
							<text style="color:#F60100 ;" v-if="item.type===5">活动订单</text>
						</view>
						<view class="righ"
							v-if="item.payType === 0 || (item.payType === 1 && parseInt(item.payType) >= 1)">
							<view>￥{{ item.payPrice }}</view>
							<view>x{{ item.num ? item.num : 1 }}</view>
						</view>
					</view>
					<view class="bot">
						<text>{{ item.createTime }}</text>
						<view class="qzf" v-if="parseInt(item.payType) === 1&&parseInt(item.type) !== 5"
							@click.stop="gozhifu(item)">
							去支付
						</view>
						
				<!-- 		<view class="qzf" v-if=""
							@click.stop="gozhifu(item)">
							去支付
						</view>
						 -->
						
						
						
						
						
						
						
						
						
						<view class="qzf" v-if="parseInt(item.payType) === 1&&parseInt(item.type) === 5"
							@click.stop="goUrl(`/user/huodongCashier?id=${item.id}&price=${item.payPrice}&type=${item.payType}&goodsId=${item.goodsId}`)">
							去支付
						</view>
						<view class="qzf" v-if="parseInt(item.payType) === 1&&parseInt(item.type) === 5"
							@click.stop="huodongquxiaos(item)">
							取消订单
						</view>
						<view class="qrwc"
							v-if="parseInt(item.payType) >= 2 && parseInt(item.payType) !== 7&& parseInt(item.type) !== 5"
							@click.stop="confirmorder(item)">
							确认完成
						</view>
						<view class="qrwc"
							v-if="parseInt(item.payType) >= 2 && parseInt(item.payType) !== 7&& parseInt(item.payType) !== 6&& parseInt(item.type) !== 5"
							@click.stop="applyT(item)">
							申请退款
						</view>
						<view class="qrwc"
							v-if="parseInt(item.payType) >= 2 && parseInt(item.payType) !== 7&& parseInt(item.payType) !== 6&& parseInt(item.type) === 5"
							@click.stop="huodongwanchengclick(item)">
							确认完成
						</view>
						<view style="color: #999999; background-color: #f0f0f0;" class="qrwc"
							v-if="parseInt(item.payType) >= 2 && parseInt(item.payType) !== 7&& parseInt(item.payType) !== 6&& parseInt(item.type) === 5"
							@click.stop="huodongclick()">
							待上门
						</view>
						<view class="qpl" v-if="parseInt(item.payType) === 7 && item.isComment === 0&& item.type === 5"
							@click.stop="gohuodongevaluate(item)">去评价
						</view>
						<view class="qpl"
							v-if="parseInt(item.payType) === 7 && item.isComment === 1 && item.type === 5">
							已评价
						</view>
						<view class="qpl"
							v-if="parseInt(item.payType) === 7 && item.isComment === 0 && item.type !== 5"
							@click.stop="goevaluate(item)">去评价
						</view>
						<view class="qpl"
							v-if="parseInt(item.payType) === 7 && item.isComment === 1 && item.type !== 5">
							已评价
						</view>
					</view>
				</view>
				<view class="main_item_already" v-else @click="goChoose(item)">
					<view style="font-size: 32rpx;font-weight: 500;" class="title">
						{{ item.quotedPriceVos.length === 0 ? '等待师傅报价' : '等待您选择师傅' }}
					</view>
					<view class="ok" v-if="item.quotedPriceVos.length > 0">已有师傅报价</view>
					<view class="no">单号：{{ item.orderCode }}</view>
					<view class="mid">
						<view class="lef">
							<image :src="item.goodsCover" mode=""></image>
							<text>{{ item.goodsName }}</text>
						</view>
					</view>
					<view class="bot">
						<text>{{ item.createTime }}</text>
					</view>
					<view class="shifu">
						<scroll-view scroll-x="true">
							<view class="shifu_item" v-for="(shfItem, shfIndex) in item.quotedPriceVos" :key="shfIndex">
								<image :src="shfItem.selfImg ? shfItem.selfImg : '/static/mine/default_user.png'"
									mode="aspectFit"></image>
								<text>￥{{ (shfItem.price * (1 + jiaNum / 100)).toFixed(2) }}</text>
							</view>
						</scroll-view>
					</view>
					<view v-if="item.quotedPriceVos.length > 0"
						style="display: flex; justify-content: center; align-items: center; margin-top: 20rpx;">
						<view class="qxdd" @click.stop="cancelorder(item)">取消订单</view>
						<view style="margin-left: 20%;" class="tips" vif="item.quotedPriceVos.length > 0">
							{{ item.quotedPriceVos.length }}位师傅已报价
						</view>
						<view class="qxdd" style="margin-left: 20rpx;">
							选择师傅
						</view>
					</view>
					<view v-if="item.payType===-3&& parseInt(item.type) !== 5" class="qxdd"
						@click.stop="cancelorder(item)">取消订单</view>
				</view>
			</view>
		</view>

		<u-modal :show="showCancel" title="取消订单" content="确认要取消该订单吗" showCancelButton @cancel="showCancel = false"
			@confirm="confirmCancel"></u-modal>
		<u-modal :show="showConfirm" title="完成订单" content="确认要完成该订单吗" showCancelButton @cancel="showConfirm = false"
			@confirm="confirmconfirm"></u-modal>

		<u-modal :show="showPaymentModal" title="提示"  showCancelButton   confirm-text="去支付"
			@cancel="showPaymentModal = false" @confirm="confirmPayment">
			
			<view class="modal-content-red">
				{{ paymentRemind }}
			</view>
			
			</u-modal>

		<u-modal :show="showRefundModal" title="提示" showCancelButton
			@cancel="showRefundModal = false" @confirm="confirmRefund">
			<view class="modal-content-red">
				{{ reminddata }}
			</view>
		</u-modal>
		<view style="display: flex; justify-content: center;" v-if="orderList.length >= 10">
			<u-loadmore :status="status" />
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				status: 'loadmore',
				showConfirm: false,
				showCancel: false,
				showPaymentModal: false,
				showRefundModal: false,
				currentItem: null,
				jiaNum: 0,
				reminddata: '若你选择线下交易，无平台监管遭遇诈骗或者纠纷需由您自行承担损失！',
				paymentRemind: '无平台担保的支付可能遭遇“假维修”“小病大修”等套路（据消协数，40%的线下维修投诉涉及虚报故障）',
				huodonglist: [],
				isFromTiaozhuan: false,
				tmplIds: [
					'qt5Q70oqYNTB2RaTzl0peEHpjK-iinWr3ZfxGvs4wtg',
					'DxiqXzK4yxCTYAqmeK9lEsU0A5-XCF9Fy7kSyX2vmnk',
					'_2z7Bbw8oq4av-yqP31fZLaI82Z_N52wNM_1ihXQv6I',
					'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
				],
				list: [{
						name: '全部',
						value: 0
					},
					{
						name: '报价列表',
						value: -2
					},
					{
						name: '待支付',
						value: 1
					},
					{
						name: '待服务',
						value: 5
					},
					{
						name: '服务中',
						value: 6
					},
					{
						name: '已完成',
						value: 7
					}
				],
				currentIndex: 0,
				page: 1,
				orderList: [],
				pay_typeArr: ['', '待支付', '报价列表', '已接单', '上门中', '待服务', '服务中', '已完成'],
				id: '',
				isLoading: false
			};
		},
		onPullDownRefresh() {
			this.page = 1;
			this.orderList = [];
			this.status = 'loadmore';
			this.getList(this.currentIndex).then(() => {
				uni.stopPullDownRefresh();
			}).catch(() => {
				uni.stopPullDownRefresh();
			});
		},
		onReachBottom() {
			if (this.status === 'nomore' || this.isLoading) return;
			this.isLoading = true;
			this.status = 'loading';
			this.page++;
			setTimeout(() => {
				this.$api.service.userOrder({
					payType: this.currentIndex,
					pageNum: this.page,
					pageSize: 10
				}).then(res => {
					const list = Array.isArray(res.list) ? res.list : [];
					const normalizedList = list.map(item => ({
						...item,
						payType: parseInt(item.payType)
					}));
					this.orderList = [...this.orderList, ...normalizedList];
					this.status = list.length < 10 ? 'nomore' : 'loadmore';
					this.isLoading = false;
				}).catch(err => {
					this.status = 'nomore';
					this.isLoading = false;
					this.page--;
					console.error('Error loading more:', err);
				});
			}, 1500);
		},
		methods: {
			huodongclick() {
				uni.showToast({
					icon: 'none',
					title: '耐心等待师傅上门，有问题联系客服'
				});
			},
			gohuodongevaluate(item) {
				uni.navigateTo({
					url: `/user/addevaluate?id=${item.id}&goodsId=${item.goodsId}&huodong=${1}`
				});
			},
			gozhifu(item) {
				this.currentItem = item;
				this.showPaymentModal = true;
			},
			confirmPayment() {
				this.showPaymentModal = false;
				if (this.currentItem) {
					uni.navigateTo({
						url: `/user/Cashier?id=${this.currentItem.id}&price=${this.currentItem.payPrice}&type=${this.currentItem.payType}&goodsId=${this.currentItem.goodsId}`
					});
				}
			},
			huodongwanchengclick(item) {
				uni.showModal({
					title: '确认完成',
					content: '师傅是否已完成订单？',
					confirmText: '确定',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							this.$api.service.huodongqueding({
								id: item.id
							}).then(res => {
								if (res.code === "200") {
									uni.showToast({
										icon: 'success',
										title: '订单完成'
									});
									this.page = 1;
									this.orderList = [];
									this.getList(this.currentIndex);
								} else {
									uni.showToast({
										icon: 'none',
										title: res.msg
									});
								}
							}).catch(err => {
								uni.showToast({
									icon: 'none',
									title: err.msg || '操作失败'
								});
								console.error('Cancel Error:', err);
							});
						}
					}
				});
			},
			huodongquxiaos(item) {
				this.showCancel = false;
				this.$api.service.huodongquxiao({
					id: item.id
				}).then(res => {
					uni.showToast({
						icon: 'none',
						title: '取消成功'
					});
					this.page = 1;
					this.orderList = [];
					this.getList(this.currentIndex);
				}).catch(err => {
					uni.showToast({
						icon: 'error',
						title: '取消失败'
					});
					console.error('Cancel Error:', err);
				});
			},
			dingyue() {
				const allTmplIds = this.tmplIds;
				const requiredTmplId = 'DxiqXzK4yxCTYAqmeK9lEsU0A5-XCF9Fy7kSyX2vmnk';
				if (allTmplIds.length < 3) {
					console.error("Not enough template IDs available:", allTmplIds);
					return;
				}
				const otherTmplIds = allTmplIds.filter(id => id !== requiredTmplId);
				const shuffled = otherTmplIds.sort(() => 0.5 - Math.random());
				const selectedTmplIds = [requiredTmplId, ...shuffled.slice(0, 2)];
				const templateData = selectedTmplIds.map((id, index) => ({
					templateId: id,
					templateCategoryId: index === 0 ? 10 : 5
				}));
				uni.requestSubscribeMessage({
					tmplIds: selectedTmplIds,
					success: (res) => {
						this.templateCategoryIds = [];
						selectedTmplIds.forEach((templId, index) => {
							if (res[templId] === 'accept') {
								const templateCategoryId = templateData[index].templateCategoryId;
								if (templateCategoryId === 10) {
									for (let i = 0; i < 15; i++) {
										this.templateCategoryIds.push(templateCategoryId);
									}
								} else {
									this.templateCategoryIds.push(templateCategoryId);
								}
							}
						});
					},
					fail: (err) => {}
				});
			},
			updateHighlight(options) {
				const userId = uni.getStorageSync('userId');
				if (!userId) {
					console.log('No userId, skipping updateHighlight');
					return;
				}
				this.$api.service.updataHighlight({
					userId: userId,
					role: 1,
					payType: options.tab
				}).then(res => {
					console.log('updateHighlight response:', res);
				}).catch(err => {
					console.error('updateHighlight error:', err);
				});
			},
			goevaluate(item) {
				uni.navigateTo({
					url: `/user/addevaluate?id=${item.id}&goodsId=${item.goodsId}`
				});
			},
			applyT(item) {
				if (item.refundStatus === 2) {
					uni.showToast({
						icon: 'none',
						title: '您的退款正在审核中'
					});
				} else if (item.refundStatus === 1) {
					uni.showToast({
						icon: 'none',
						title: '您的退款已审核通过，请留意账户动态'
					});
				} else if (item.refundStatus === 3) {
					uni.showToast({
						icon: 'none',
						title: '您的退款申请已被驳回，请重新申请'
					});
				} else {
					this.currentItem = item;
					this.showRefundModal = true;
				}
			},
			confirmRefund() {
				this.showRefundModal = false;
				if (this.currentItem) {
					uni.navigateTo({
						url: `/user/tuicause?order_id=${this.currentItem.id}`
					});
				}
			},
			goChoose(item) {
				this.$store.commit('changeOrderInfo', item);
				uni.navigateTo({
					url: '/user/choose_master'
				});
			},
			confirmorder(item) {
				this.id = item.id;
				this.showConfirm = true;
			},
			confirmconfirm() {
				this.showConfirm = false;
				this.$api.service.confirmOrder({
					orderId: this.id
				}).then(res => {
					if (res === -1) {
						uni.showToast({
							icon: 'none',
							title: '不在服务中不能确认完成',
							duration: 2000
						});
					} else {
						uni.showToast({
							icon: 'success',
							title: '操作成功'
						});
						uni.redirectTo({
							url: `/user/order_list?tab=7`
						});
					}
				}).catch(err => {
					uni.showToast({
						icon: 'error',
						title: '操作失败'
					});
					console.error('Confirm Error:', err);
				});
			},
			confirmCancel() {
				this.showCancel = false;
				this.$api.service.cancelOrder({
					id: this.id
				}).then(res => {
					uni.showToast({
						icon: 'none',
						title: '取消成功'
					});
					this.page = 1;
					this.orderList = [];
					this.getList(this.currentIndex);
				}).catch(err => {
					uni.showToast({
						icon: 'error',
						title: '取消失败'
					});
					console.error('Cancel Error:', err);
				});
			},
			cancelorder(item) {
				this.id = item.id;
				this.showCancel = true;
			},
			goUrl(e) {
				uni.navigateTo({
					url: e
				});
			},
			getList(nval) {
				const payType = nval !== undefined ? nval : this.currentIndex;
				if (payType === 0) {
					return Promise.all([
						this.$api.service.huodongorder(),
						this.$api.service.userOrder({
							payType: 0,
							pageNum: this.page,
							pageSize: 10
						})
					]).then(([huodongRes, userOrderRes]) => {
						const huodongList = (huodongRes.code === "200" && huodongRes.data) ?
							(Array.isArray(huodongRes.data) ? huodongRes.data.filter(item => item != null) : [
								huodongRes.data
							]) : [];
						this.huodonglist = huodongList;
						const userList = Array.isArray(userOrderRes.list) ? userOrderRes.list : [];
						const normalizedUserList = userList.map(item => ({
							...item,
							payType: parseInt(item.payType)
						}));
						const combinedList = [...huodongList, ...normalizedUserList];
						this.$set(this, 'orderList', combinedList);
						this.status = userList.length < 10 ? 'nomore' : 'loadmore';
					}).catch(err => {
						uni.showToast({
							icon: 'error',
							title: '获取订单失败'
						});
						console.error('API Error:', err);
						this.orderList = [];
						this.huodonglist = [];
						return Promise.reject(err);
					});
				} else {
					this.huodonglist = [];
					return this.$api.service.userOrder({
						payType,
						pageNum: this.page,
						pageSize: 10
					}).then(res => {
						const list = Array.isArray(res.list) ? res.list : [];
						const normalizedList = list.map(item => ({
							...item,
							payType: parseInt(item.payType)
						}));
						this.$set(this, 'orderList', normalizedList);
						this.status = list.length < 10 ? 'nomore' : 'loadmore';
					}).catch(err => {
						uni.showToast({
							icon: 'error',
							title: '获取订单失败'
						});
						console.error('API Error:', err);
						this.orderList = [];
						return Promise.reject(err);
					});
				}
			},
			handleHeader(item) {
				this.currentIndex = item.value;
			},
			getcommissionRatio() {
				this.$api.service.commissionRatio().then(res => {
					this.jiaNum = res;
				}).catch(err => {
					console.error('getcommissionRatio Error:', err);
				});
			}
		},
		onLoad(options) {
			if (options.from && options.from === 'tiaozhuan') {
				this.isFromTiaozhuan = true;
				console.log('来源是跳转页，返回时将执行默认返回');
			} else {
				this.isFromTiaozhuan = false;
				console.log('来源是其他页面，返回时将跳转到"我的"页面');
			}
			this.$api.service.remind().then(res => {
				if (res.code === "200") {
					this.reminddata = res.data.cancelRemind;
					this.paymentRemind = res.data.paymentRemind;
				}
			});
			this.updateHighlight(options);
			this.currentIndex = options.tab ? parseInt(options.tab) : 0;
			this.getList(this.currentIndex);
			this.getcommissionRatio();
		},
		onShow() {
			uni.$on('cancelOr', () => {
				this.currentIndex = 0;
				this.page = 1;
				this.getList();
			});
			const pages = getCurrentPages();
			const currentPage = pages[pages.length - 1];
			const options = currentPage.options || {};
			if (options.tab) {
				this.currentIndex = parseInt(options.tab);
				this.page = 1;
				this.getList(this.currentIndex);
			}
		},
		onBackPress() {
			uni.redirectTo({
				url: '/pages/mine'
			});
			return true;
		},
		onUnload() {
			if (this.isFromTiaozhuan) {
				uni.redirectTo({
					url: '/pages/mine'
				});
			}
		},
		watch: {
			currentIndex(nval) {
				this.page = 1;
				this.orderList = [];
				this.status = 'loadmore';
				this.getList(nval);
			}
		}
	};
</script>

<style scoped lang="scss">
	.page {
		background-color: #F8F8F8;
		min-height: 100vh;
		padding-top: 100rpx;

		.header {
			position: fixed;
			top: 0;
			width: 750rpx;
			height: 100rpx;
			background: #FFFFFF;
			display: flex;
			justify-content: space-around;
			align-items: center;
			z-index: 99;

			.header_item {
				max-width: 90rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #999999;
				display: flex;
				justify-content: center;
				flex-wrap: wrap;
				white-space: nowrap;

				.blue {
					margin-top: 8rpx;
					width: 38rpx;
					height: 6rpx;
					background: #2E80FE;
					border-radius: 4rpx;
				}
			}
		}

		.main {
			padding: 40rpx 30rpx;

			.main_item {
				width: 690rpx;
				background: #FFFFFF;
				border-radius: 24rpx;
				padding: 28rpx 36rpx;
				margin-bottom: 20rpx;
				box-sizing: border-box;

				.head {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 24rpx;
					font-weight: 400;
					color: #999999;

					.no {
						max-width: 500rpx;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}
				}

				.mid {
					margin-top: 20rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.lef {
						display: flex;
						align-items: center;
						flex: 1;
						overflow: hidden;

						image {
							width: 120rpx;
							height: 120rpx;
							flex-shrink: 0;
						}

						text {
							font-size: 28rpx;
							font-weight: 400;
							color: #333333;
							margin-left: 30rpx;
							max-width: 350rpx;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
					}

					.righ {
						font-size: 28rpx;
						font-weight: 400;
						color: #333333;
						text-align: right;
						margin-left: 10rpx;
					}
				}

				.bot {
					margin-top: 20rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #999999;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.qzf,
					.qxdd,
					.lxsf,
					.qrwc,
					.qpl {
						width: 148rpx;
						height: 48rpx;
						background: #2E80FE;
						border-radius: 50rpx;
						font-size: 20rpx;
						font-weight: 500;
						line-height: 48rpx;
						text-align: center;
					}

					.qzf,
					.qrwc,
					.qpl {
						color: #fff;
					}

					.qxdd,
					.lxsf {
						background: #FFFFFF;
						border: 2rpx solid #2E80FE;
						color: #2E80FE;
					}
				}
			}

			.main_item_already {
				padding: 28rpx 36rpx;
				background-color: #fff;
				border-radius: 24rpx;
				margin-bottom: 20rpx;
				box-sizing: border-box;

				.qxdd {
					width: 148rpx;
					height: 48rpx;
					background: #FFFFFF;
					border-radius: 50rpx;
					font-size: 20rpx;
					font-weight: 500;
					line-height: 48rpx;
					text-align: center;
					border: 2rpx solid #2E80FE;
					color: #2E80FE;
				}

				.no {
					margin-top: 20rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #999999;
					max-width: 500rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.mid {
					margin-top: 20rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.lef {
						display: flex;
						align-items: center;
						flex: 1;
						overflow: hidden;

						image {
							width: 120rpx;
							height: 120rpx;
							flex-shrink: 0;
						}

						text {
							font-size: 28rpx;
							font-weight: 400;
							color: #333333;
							margin-left: 30rpx;
							max-width: 350rpx;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
					}
				}

				.bot {
					margin-top: 20rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #999999;
				}

				.shifu {
					margin-top: 20rpx;

					.shifu_item {
						display: inline-flex;
						flex-direction: column;
						align-items: center;
						margin-right: 20rpx;

						image {
							width: 90rpx;
							height: 90rpx;
							border-radius: 50%;
						}

						text {
							margin-top: 10rpx;
							font-size: 24rpx;
							color: #333333;
						}
					}
				}

				.tips {
					margin-top: 10rpx;
					font-size: 24rpx;
					color: #2E80FE;
				}

				.title {
					font-size: 28rpx;
					font-weight: 500;
					color: #333333;
				}

				.ok {
					font-size: 24rpx;
					color: #2E80FE;
					margin-top: 10rpx;
				}
			}
		}
	}

	/* MODIFICATION START: Added style for the red modal content */
	.modal-content-red {
		color: #F60100; // A common red color for warnings
		padding: 10rpx 30rpx; // Add some padding for better spacing
		text-align: center; // Center the text within the modal
	}
	/* MODIFICATION END */
</style>