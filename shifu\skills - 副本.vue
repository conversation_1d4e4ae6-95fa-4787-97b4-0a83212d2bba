<template>
  <view class="page">
    <view class="main">
      <!-- 左侧父类分类列表 -->
      <view class="left">
        <scroll-view scroll-y="true" class="scrollL">
          <view v-if="loading" class="loading">
            <text>加载中...</text>
          </view>
          <view v-else-if="error" class="error">
            <text>{{ error }}</text>
          </view>
          <view v-else-if="!categories.length" class="no-content">
            <text>暂无分类数据</text>
          </view>
          <view
            v-else
            class="left_item"
            v-for="category in categories"
            :key="category.id"
            @tap="selectCategory(category.id)"
            :class="{ active: selectedCategoryId === category.id }"
          >
            <view class="category_name">{{ category.name }}</view>
          </view>
        </scroll-view>
      </view>

      <!-- 右侧子类和服务列表 -->
      <view class="right">
        <scroll-view scroll-y="true" class="scrollR">
          <view v-if="loading" class="loading">
            <text>加载中...</text>
          </view>
          <view v-else-if="error" class="error">
            <text>{{ error }}</text>
          </view>
          <view v-else-if="currentCategory && currentCategory.children && currentCategory.children.length">
            <!-- 循环子类分类 -->
            <view 
              class="subcategory_section" 
              v-for="subCategory in currentCategory.children" 
              :key="subCategory.id"
            >
              <!-- 子类标题和已选择计数 -->
              <view class="subcategory_header">
                <view class="subcategory_title" @click="toggleSubCategory(subCategory.id)">
                  {{ subCategory.name }} 
                  <text class="selected_count">(已选择{{ getSelectedCount(subCategory.id) }})</text>
                </view>
                <view class="select_all" @click="selectAllServices(subCategory.id)">
                  {{ isAllSelected(subCategory.id) ? '取消全选' : '全选' }}
                </view>
                <view class="expand_icon" @click="toggleSubCategory(subCategory.id)">
                  {{ expandedSubCategories.includes(subCategory.id) ? '▲' : '▼' }}
                </view>
              </view>
              
              <!-- 服务列表 -->
              <view 
                class="service_items" 
                v-if="expandedSubCategories.includes(subCategory.id) && subCategory.serviceList && subCategory.serviceList.length"
              >
                <view 
                  class="service_item" 
                  v-for="service in subCategory.serviceList" 
                  :key="service.id"
                  @click="toggleSelectService(service.id, subCategory.id)"
                  :class="{ active: isServiceSelected(service.id, subCategory.id) }"
                >
                  {{ service.title }}
                </view>
              </view>
              
              <!-- 如果没有服务项目 -->
              <view 
                class="no-services" 
                v-else-if="expandedSubCategories.includes(subCategory.id) && (!subCategory.serviceList || !subCategory.serviceList.length)"
              >
                <text>暂无服务项目</text>
              </view>
            </view>
          </view>
          <view v-else class="no-content">
            <text>暂无子分类</text>
          </view>
        </scroll-view>
      </view>
    </view>
    <view class="footer">
      <button class="save_btn" @click="saveSettings">保存设置</button>
    </view>
  </view>
</template>

<script>
import $api from "@/api/index.js";

export default {
  data() {
    return {
      keyword: "",
      categories: [],
	  shifuId:'',
      selectedCategoryId: null,
      expandedSubCategories: [], // 存储展开的子类ID
      loading: false,
      shifuInfo: { serviceIds: [] }, // Initialize with default serviceIds
      error: null,
      dataBox: {}, // 存储用户选择的服务项目，按子类ID分组
    };
  },
  computed: {
    currentCategory() {
      if (!this.selectedCategoryId) return null;
      const category = this.categories.find(cat => cat.id === this.selectedCategoryId);
      console.log("Current category:", category);
      return category;
    }
  },
  methods: {
    // 选择父类分类
    selectCategory(id) {
      console.log("选择父类分类:", id);
      this.selectedCategoryId = id;
      
      // 初始时展开第一个子类
      const category = this.categories.find(cat => cat.id === id);
      if (category && category.children && category.children.length > 0) {
        this.expandedSubCategories = [category.children[0].id];
      }
      
      this.$forceUpdate();
    },
    
    // 切换子类展开/折叠状态
    toggleSubCategory(subCategoryId) {
      console.log("切换子类展开状态:", subCategoryId);
      const index = this.expandedSubCategories.indexOf(subCategoryId);
      
      if (index === -1) {
        this.expandedSubCategories.push(subCategoryId);
      } else {
        this.expandedSubCategories.splice(index, 1);
      }
    },
    
    // 切换服务项选择状态
    toggleSelectService(serviceId, subCategoryId) {
      console.log("切换服务选择状态:", serviceId, subCategoryId);
      
      // Ensure shifuInfo is initialized
      if (!this.shifuInfo) {
        this.shifuInfo = { serviceIds: [] };
      }
      
      // Ensure the subcategory exists in dataBox
      if (!this.dataBox[subCategoryId]) {
        this.$set(this.dataBox, subCategoryId, {
          selectedItems: [],
          count: 0
        });
      }
      
      const index = this.dataBox[subCategoryId].selectedItems.indexOf(serviceId);
      
      if (index === -1) {
        this.dataBox[subCategoryId].selectedItems.push(serviceId);
        if (!this.shifuInfo.serviceIds.includes(serviceId)) {
          this.shifuInfo.serviceIds.push(serviceId);
        }
      } else {
        this.dataBox[subCategoryId].selectedItems.splice(index, 1);
        this.shifuInfo.serviceIds = this.shifuInfo.serviceIds.filter(id => id !== serviceId);
      }
      
      // Update count
      this.dataBox[subCategoryId].count = this.dataBox[subCategoryId].selectedItems.length;
      
      console.log("Updated shifuInfo.serviceIds:", this.shifuInfo.serviceIds);
      this.$forceUpdate();
    },
    
    // 检查服务是否被选中
    isServiceSelected(serviceId, subCategoryId) {
      if (!this.dataBox[subCategoryId]) return false;
      return this.dataBox[subCategoryId].selectedItems.includes(serviceId);
    },
    
    // 获取子类选中的服务数量
    getSelectedCount(subCategoryId) {
      if (!this.dataBox[subCategoryId]) return 0;
      return this.dataBox[subCategoryId].count || 0;
    },
    
    // 全选/取消全选服务项
    selectAllServices(subCategoryId) {
      const subCategory = this.currentCategory.children.find(sub => sub.id === subCategoryId);
      if (!subCategory || !subCategory.serviceList || !subCategory.serviceList.length) return;

      const allServiceIds = subCategory.serviceList.map(service => service.id);
      const isAllCurrentlySelected = this.isAllSelected(subCategoryId);

      if (isAllCurrentlySelected) {
        // 取消全选
        this.dataBox[subCategoryId].selectedItems = [];
        this.shifuInfo.serviceIds = this.shifuInfo.serviceIds.filter(id => !allServiceIds.includes(id));
      } else {
        // 全选
        allServiceIds.forEach(serviceId => {
          if (!this.dataBox[subCategoryId].selectedItems.includes(serviceId)) {
            this.dataBox[subCategoryId].selectedItems.push(serviceId);
          }
          if (!this.shifuInfo.serviceIds.includes(serviceId)) {
            this.shifuInfo.serviceIds.push(serviceId);
          }
        });
      }

      // 更新计数
      this.dataBox[subCategoryId].count = this.dataBox[subCategoryId].selectedItems.length;
      this.$forceUpdate();
    },
    
    // 检查是否所有服务项都已选中
    isAllSelected(subCategoryId) {
      const subCategory = this.currentCategory.children.find(sub => sub.id === subCategoryId);
      if (!subCategory || !subCategory.serviceList || !subCategory.serviceList.length) return false;
      const allServiceIds = subCategory.serviceList.map(service => service.id);
      const selectedServiceIds = this.dataBox[subCategoryId]?.selectedItems || [];
      return allServiceIds.length > 0 && allServiceIds.every(id => selectedServiceIds.includes(id));
    },
    
    // 保存设置
    async saveSettings() {
      try {
        // Save shifuInfo.serviceIds as a comma-separated string
        const serviceIdsString = this.shifuInfo.serviceIds.join(',');
        uni.setStorageSync('selectedServices', serviceIdsString);
        console.log("Saved selectedServices:", serviceIdsString);
        
        // Ensure shifuInfo is initialized
        if (!this.shifuInfo) {
          this.shifuInfo = { serviceIds: [] };
        }
        let userId = this.shifuId;
        let mobile = uni.getStorageSync('phone') || '';
        // Prepare shifuInfo with serviceIds as a comma-separated string
        const shifuInfoToSend = {
          ...this.shifuInfo,
          userId: userId,
          mobile: mobile,
          serviceIds: this.shifuInfo.serviceIds.join(',')
        };
        
        console.log("Saving shifuInfo:", shifuInfoToSend);
        const res = await this.$api.shifu.updataSkill(JSON.stringify(shifuInfoToSend));
        console.log("API Response from updataInfoSF:", res);
        if(res.code==='-1'){
	  
			    uni.showToast({
			      title: res.msg,
			      icon: 'none'
			    });
					  setTimeout(() => {
					    uni.navigateBack({ delta: 1 });
					  }, 2000);
			 
			  } else {
				  uni.showToast({
				    title: res.msg,
				    icon: 'none',
				    duration: 2000,
				    success: () => {
				      setTimeout(() => {
				        uni.navigateBack({ delta: 1 });
				      }, 2000);
				    }
				  });
				  
			
			  }
			} catch (e) {
			  uni.showToast({
			    title: '保存失败',
			    icon: 'none'
			  });
			  console.error('保存失败:', e);
			}
			
			
			
		
    
    },
    goUrl(url) {
      uni.navigateTo({ url });
    },
    
    // 获取分类列表
    async getList() {
      this.loading = true;
      this.error = null;
      try {
        const response = await $api.shifu.getSkill();
        console.log("API Response:", response);

        // 处理响应数据
        let categoriesData = [];
        if (Array.isArray(response)) {
          categoriesData = response;
        } else if (response.data && Array.isArray(response.data)) {
          categoriesData = response.data;
        } else {
          throw new Error("无效或空的数据");
        }
        
        // 确保children和serviceList存在，并初始化dataBox
        categoriesData.forEach(category => {
          if (!category.children) category.children = [];
          category.children.forEach(subCategory => {
            if (!subCategory.serviceList) subCategory.serviceList = [];
            if (!this.dataBox[subCategory.id]) {
              this.$set(this.dataBox, subCategory.id, {
                selectedItems: [],
                count: 0
              });
            }
          });
        });
        
        this.categories = categoriesData;
        console.log("Categories processed:", this.categories);
        
        if (this.categories.length > 0) {
          this.selectedCategoryId = this.categories[0].id;
          const firstCategory = this.categories[0];
          if (firstCategory.children && firstCategory.children.length > 0) {
            this.expandedSubCategories = [firstCategory.children[0].id];
          }
        } else {
          this.error = "分类数据为空";
          uni.showToast({
            title: "分类数据为空",
            icon: "none"
          });
        }
      } catch (err) {
        this.error = "数据加载失败: " + err.message;
        console.error("Error in getList:", err);
        uni.showToast({
          title: this.error,
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    
    // 加载保存的选择（仅作为API失败的备用）
    loadSavedSelections() {
      try {
        const savedData = uni.getStorageSync('selectedServices');
        if (savedData && savedData.trim()) {
          this.shifuInfo.serviceIds = savedData
            .split(',')
            .map(id => parseInt(id.trim(), 10))
            .filter(id => !isNaN(id));
        } else {
          this.shifuInfo.serviceIds = [];
        }
        
        // Reconstruct dataBox from serviceIds
        this.categories.forEach(category => {
          if (category.children && category.children.length) {
            category.children.forEach(subCategory => {
              if (subCategory.serviceList && subCategory.serviceList.length) {
                if (!this.dataBox[subCategory.id]) {
                  this.$set(this.dataBox, subCategory.id, {
                    selectedItems: [],
                    count: 0
                  });
                }
                const matchingServiceIds = this.shifuInfo.serviceIds.filter(serviceId =>
                  subCategory.serviceList.some(service => service.id === serviceId)
                );
                this.dataBox[subCategory.id].selectedItems = matchingServiceIds;
                this.dataBox[subCategory.id].count = matchingServiceIds.length;
              }
            });
          }
        });
        
        console.log("Loaded shifuInfo.serviceIds from storage:", this.shifuInfo.serviceIds);
        console.log("Reconstructed dataBox:", this.dataBox);
        this.$forceUpdate();
      } catch (e) {
        console.error('加载已保存选择失败:', e);
        this.shifuInfo.serviceIds = [];
      }
    },
    
    // 获取并初始化服务ID
    async getInfoS() {
      try {
        const res = await $api.shifu.getSInfo();
        console.log("getSInfo Response:", res);
        
        // Initialize shifuInfo
        this.shifuInfo = res && typeof res === 'object' ? res : { serviceIds: [] };
        this.shifuId=res.data.id
        // Always use API serviceIds if available
        let serviceIdsArray = [];
        if (typeof res.serviceIds === 'string' && res.serviceIds.trim() !== '') {
          serviceIdsArray = res.serviceIds
            .split(',')
            .map(id => parseInt(id.trim(), 10))
            .filter(id => !isNaN(id));
        } else if (Array.isArray(res.serviceIds)) {
          serviceIdsArray = res.serviceIds.map(id => parseInt(id, 10)).filter(id => !isNaN(id));
        }
        this.shifuInfo.serviceIds = serviceIdsArray;
        
        // If API provides no valid serviceIds, try local storage
        if (!this.shifuInfo.serviceIds.length) {
          this.loadSavedSelections();
        }
        
        console.log("Processed Service IDs:", this.shifuInfo.serviceIds);

        // Update dataBox based on shifuInfo.serviceIds
        this.dataBox = {};
        this.categories.forEach(category => {
          if (category.children && category.children.length) {
            category.children.forEach(subCategory => {
              if (subCategory.serviceList && subCategory.serviceList.length) {
                this.$set(this.dataBox, subCategory.id, {
                  selectedItems: [],
                  count: 0
                });
                const matchingServiceIds = this.shifuInfo.serviceIds.filter(serviceId =>
                  subCategory.serviceList.some(service => service.id === serviceId)
                );
                this.dataBox[subCategory.id].selectedItems = matchingServiceIds;
                this.dataBox[subCategory.id].count = matchingServiceIds.length;
              }
            });
          }
        });

        console.log("Updated dataBox:", this.dataBox);
        console.log("Updated shifuInfo.serviceIds:", this.shifuInfo.serviceIds);
        this.$forceUpdate();
      } catch (err) {
        console.error("Error in getInfoS:", err);
        this.shifuInfo = { serviceIds: [] };
        this.loadSavedSelections(); // Fallback to local storage on API failure
        uni.showToast({
          title: "加载服务信息失败",
          icon: "none"
        });
      }
    }
  },
  async onLoad() {
    try {
      const city = uni.getStorageSync("city");
      console.log("City:", city);
      // Clear selectedServices to start fresh
      uni.setStorageSync('selectedServices', '');
      await this.getList();
      await this.getInfoS();
    } catch (err) {
      console.error("Error in onLoad:", err);
      uni.showToast({
        title: "页面加载失败",
        icon: "none"
      });
    }
  },
};
</script>

<style scoped>
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为固定footer预留空间 */
}

.main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left {
  width: 190rpx;
  background-color: #f8f8f8;
}

.scrollL {
  height: 100%;
  overflow-y: auto;
}

.left_item {
  padding: 0 20rpx;
  min-height: 100rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  border-left: 6rpx solid transparent;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
}

.left_item.active {
  color: #2e80fe;
  font-size: 30rpx;
  border-left-color: #2e80fe;
  background-color: #fff;
}

.category_name {
  height: 100rpx;
  width: 100%;
  display: flex;
  align-items: center;
}

.right {
  flex: 1;
  background-color: #fff;
  border-radius: 12rpx 12rpx 0 0;
  margin-left: 10rpx;
}

.scrollR {
  height: 100%;
  overflow-y: auto;
}

.subcategory_section {
  margin-bottom: 15rpx;
}

.subcategory_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fafafa;
  border-bottom: 1rpx solid #f0f0f0;
}

.subcategory_title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.selected_count {
  color: #2e80fe;
  font-weight: normal;
}

.select_all {
  font-size: 26rpx;
  color: #2e80fe;
  margin-left: 20rpx;
}

.expand_icon {
  font-size: 24rpx;
  color: #999;
}

.service_items {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}

.service_item {
  width: calc(33.33% - 20rpx);
  margin: 10rpx;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s;
  text-align: center;
  padding: 0 10rpx;
}

.service_item.active {
  background-color: #e6f0ff;
  color: #2e80fe;
  border: 1rpx solid #2e80fe;
}

.no-services,
.no-content,
.loading,
.error {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx;
}

.error {
  color: #ff4d4f;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  padding: 15rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.save_btn {
  width: 90%;
  height: 90rpx;
  background-color: #2e80fe;
  color: white;
  border-radius: 45rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>