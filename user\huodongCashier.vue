<template>
	<view class="page">
		<view class="time"><text>支付剩余时间</text> <u-count-down :time="15 * 60 * 1000" format="mm:ss"></u-count-down>
		</view>
		<span class="price"><span>￥</span>{{allprice}}</span>
		<view class="payCard">
			<view class="left">
				<image src="../static/svg/weixinfang.svg" mode="aspectFill"></image>
				<text>微信支付</text>
			</view>
			<view class="choose" :style="currentIndex == 0?'background:#2E80FE;border:2rpx solid #2E80FE':''"
				@click="currentIndex = 0">
				<u-icon name="checkbox-mark" color="#fff" size="16"></u-icon>
			</view>
		</view>
		<view class="footer">
			<view class="btn" @click="confirmPay">确认支付</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentIndex: 0,
				id: '',
				infoyouhuij: '',
				price: '',
				goods_id: '',
				type: 0,
				tmplIds: 'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY',
			}
		},
		computed: {
			allprice() {
				const result = this.price * 1;
				return result <= 0 ? 0.01 : Number(result.toFixed(2));
			}
		},
		methods: {
			confirmPay() {
				uni.showModal({
					title: '提示',
					content: '订单支付成功后，平台将根据订单支付时间进行服务排期。服务师傅将在48小时内跟您联系，请注意来电提醒。若您有任何问题，请及时联系客服，我们会尽力为您协调处理。',
					cancelText: '取消',
					confirmText: '确认',
					success: (res) => {
						if (res.confirm) {
							this.$api.service.huodongPay({
								orderId: this.id,
								type: 1,
							}).then(res => {
								if(res.code==="-1"){
									wx.showToast({
										title: res.msg,
										icon: 'none'
									});
								}else{
									console.log(res)
									let obj = res.data
									let packageStr = "prepay_id=" + obj.prepayId;
									console.log(String(packageStr))
									console.log(obj.nonceStr)
									console.log(packageStr)
									console.log(obj.nonceStr)
									console.log(String(obj.timestamp))
									console.log(obj.sign)
									const paymentParams = {
										timeStamp: String(obj.timestamp), 
										nonceStr: obj.nonceStr,
										package: "prepay_id=" + obj.prepayId,
										signType: 'MD5',
										paySign: obj.sign
									};
									console.log(JSON.stringify(paymentParams));
									uni.requestPayment({
										"provider": 'wxpay',
										timeStamp: String(obj.timestamp),
										nonceStr: obj.nonceStr,
										package: "prepay_id=" + obj.prepayId,
										partnerid: obj.partnerId,
										signType: "MD5",
										paySign: obj.sign,
										appId: obj.appId,
										success: (res1) => {
											console.log('支付成功', res1);
											uni.showToast({
												title: '支付成功',
												icon: 'success'
											})
											// this.dingyue()
											setTimeout(() => {
											uni.reLaunch({
											        url: '/user/tiaozhuan'
											    });
											})
										},
										fail: (err) => {
											console.error('requestPayment fail object:', err);
											console.error('requestPayment fail JSON:', JSON.stringify(err));
											if (err.errMsg.includes('fail cancel')) {
												wx.showToast({
													title: '您已取消支付',
													icon: 'none'
												});
											} else {
												wx.showToast({
													title: '支付失败，请稍后重试',
													icon: 'none'
												});
											}
											console.error('支付失败', err);
											uni.showToast({
												title: '支付失败请检查网络',
												icon: 'error'
											})
										},
									})
								}
							})
						}
					}
				});
			},
			dingyue() {
				uni.requestSubscribeMessage({
					provider: 'weixin',
					tmplIds: this.tmplIds,
					success: (res) => {
						const anyAccepted = this.tmplIds.some(id => res[id] === 'accept');
						if (anyAccepted) {
							uni.showToast({
								title: '消息订阅成功',
								icon: 'success'
							});
						}
					},
					fail: (err) => {
					}
				});
			},
		},
		async onLoad(options) {
			this.infoyouhuij = this.$store.state.service.orderInfo
			console.log(this.infoyouhuij)
			console.log(options)
			this.id = options.id
			this.price = options.price
			this.type = options.type
			this.goodsId = options.goodsId
		}
	}
</script>

<style scoped lang="scss">
	.page {
		background-color: #f8f8f8;
		height: 100vh;
		padding-top: 40rpx;

		.time {
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			font-weight: 500;
			color: #333333;

			text {
				margin-right: 15rpx;
			}
		}

		.price {
			display: inline-block;
			width: 750rpx;
			text-align: center;
			margin-top: 20rpx;
			font-size: 80rpx;
			font-weight: 500;
			color: #292C39;

			span {
				font-size: 36rpx;
			}
		}

		.payCard {
			margin: 0 auto;
			width: 686rpx;
			height: 130rpx;
			background: #FFFFFF;
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			margin-top: 40rpx;
			padding: 0 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.left {
				display: flex;
				align-items: center;

				image {
					width: 70rpx;
					height: 70rpx;
				}

				text {
					margin-left: 20rpx;
					font-size: 32rpx;
					font-weight: 500;
					color: #171717;
				}
			}

			.right {
				display: flex;
				align-items: center;

				text {
					font-size: 24rpx;
					font-weight: 400;
					color: #E72427;
				}
			}

			.choose {
				width: 40rpx;
				height: 40rpx;
				background-color: #fff;
				border: 2rpx solid #ADADAD;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.footer {
			width: 750rpx;
			height: 192rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: fixed;
			bottom: 0;

			.btn {
				width: 686rpx;
				height: 88rpx;
				background: #2E80FE;
				border-radius: 44rpx 44rpx 44rpx 44rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 88rpx;
				text-align: center;
			}
		}
	} 
</style>