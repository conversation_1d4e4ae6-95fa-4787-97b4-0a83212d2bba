<template>
	<view class="custom-tabbar fix flex-center fill-base b-1px-t">
		<view v-if="configInfo.tabBar && configInfo.tabBar.length" @tap.stop="changeTab(item.value)"
			class="flex-center flex-column mt-sm"
			:style="{ width: 100 / configInfo.tabBar.length + '%', color: cur == item.value ? '#2e80fe' : '#666' }"
			v-for="(item, index) in configInfo.tabBar" :key="item.value">
			<u-icon :name="item.icon" :color="cur == item.value ? '#599eff' : '#c5cad4'" size="28"></u-icon>
			<view class="text">{{ item.name }}</view>
		</view>
		<view v-else class="no-tabbar">暂无导航栏数据</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from "vuex";

	export default {
		components: {},
		props: {
			cur: {
				type: [Number, String],
				default: "0",
			},
		},
		data() {
			return {
				queryRetries: 0,
				maxRetries: 3,
				tmplIds: [
					'qt5Q70oqYNTB2RaTzl0peEHpjK-iinWr3ZfxGvs4wtg',
					'DxiqXzK4yxCTYAqmeK9lEsU0A5-XCF9Fy7kSyX2vmnk',
					'_2z7Bbw8oq4av-yqP31fZLaI82Z_N52wNM_1ihXQv6I',
					'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
				],
				retryDelay: 100,
			};
		},
	
		computed: {
			...mapState({
				primaryColor: (state) => state.config.configInfo.primaryColor,
				subColor: (state) => state.config.configInfo.subColor,
				configInfo: (state) => state.config.configInfo,
				commonOptions: (state) => state.user.commonOptions,
				activeIndex: (state) => state.order.activeIndex,
			}),
		},
		mounted() {
			console.log("ConfigInfo:", this.configInfo);
			console.log("TabBar:", this.configInfo.tabBar);
			this.$nextTick(() => {
				this.updateTabbarHeight();
			});
		},
		methods: {
			...mapMutations(["updateConfigItem"]),
			updateTabbarHeight() {
				if (this.queryRetries >= this.maxRetries) {
					console.error("Max retries reached for .custom-tabbar query");
					this.setFallbackConfig();
					return;
				}

				const sysheight = uni.getSystemInfoSync().windowHeight;
				let configInfo = JSON.parse(JSON.stringify(this.configInfo));
				let {
					navBarHeight = 0
				} = configInfo;

				const query = uni.createSelectorQuery().in(this);
				query
					.select(".custom-tabbar")
					.boundingClientRect((data) => {
						console.log("Component context query result:", data);
						if (data) {
							this.handleQuerySuccess(data, sysheight, navBarHeight, configInfo);
						} else {
							this.queryGlobalContext(sysheight, navBarHeight, configInfo);
						}
					})
					.exec();
			},
			queryGlobalContext(sysheight, navBarHeight, configInfo) {
				const query = uni.createSelectorQuery();
				query
					.select(".custom-tabbar")
					.boundingClientRect((data) => {
						console.log("Global context query result:", data);
						if (data) {
							this.handleQuerySuccess(data, sysheight, navBarHeight, configInfo);
						} else {
							console.error("Failed to find .custom-tabbar element in global context");
							this.queryRetries++;
							setTimeout(() => {
								this.updateTabbarHeight();
							}, this.retryDelay);
						}
					})
					.exec();
			},
			handleQuerySuccess(data, sysheight, navBarHeight, configInfo) {
				let curSysHeight = sysheight - data.height - navBarHeight;
				configInfo.curSysHeight = curSysHeight;
				configInfo.tabbarHeight = data.height;
				this.updateConfigItem({
					key: "configInfo",
					val: configInfo,
				});
				console.log("Tabbar height updated:", data.height, "curSysHeight:", curSysHeight);
			},
			setFallbackConfig() {
				let sysheight = uni.getSystemInfoSync().windowHeight;
				let configInfo = JSON.parse(JSON.stringify(this.configInfo));
				let {
					navBarHeight = 0
				} = configInfo;
				configInfo.curSysHeight = sysheight - navBarHeight;
				configInfo.tabbarHeight = 98;
				this.updateConfigItem({
					key: "configInfo",
					val: configInfo,
				});
				console.log("Fallback config set: tabbarHeight: 98, curSysHeight:", configInfo.curSysHeight);
			},
			changeTab(index) {
				// Combine all template IDs
				// const allTmplIds = this.tmplIds;
				// // Ensure we have enough template IDs
				// if (allTmplIds.length < 3) {
				// 	console.error("Not enough template IDs available:", allTmplIds);
				// 	return;
				// }
				// // Randomly select 3 template IDs
				// const shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());
				// const selectedTmplIds = shuffled.slice(0, 3);
				// console.log("Selected template IDs:", selectedTmplIds);

				// Make a single subscription request with the 3 random template IDs
				// const panduan=uni.getStorageSync('userId')
				// console.log(panduan)
				// if(panduan){
				// 	console.log(2222)
				// 	uni.requestSubscribeMessage({
				// 		tmplIds: selectedTmplIds,
				// 		success: (res) => {
				// 			console.log('requestSubscribeMessage success:', res, 'with tmplIds:', selectedTmplIds);
				// 		},
				// 		fail: (err) => {
				// 			console.error('requestSubscribeMessage failed:', err, 'with tmplIds:', selectedTmplIds);
				// 		}
				// 	});
				// }
			

				let length = this.$store.state.config.configInfo.tabBar.length;
				let page = {
					0: `/pages/service`,
					1: `/pages/technician`,
					2: `/pages/order`,
					3: `/pages/mine`,
				};
				let page2 = {
					4: `/pages/Receiving`,
					3: `/pages/mine`,
				};

				if (index == this.cur) return;
				this.$util.goUrl({
					url: length == 2 ? page2[index] : page[index],
					openType: `reLaunch`,
					fail: (err) => {
						console.error("Navigation failed:", err);
					},
				});
			},
		},
	};
</script>

<style scoped lang="scss">
	.custom-tabbar {
		height: 98rpx;
		bottom: 0;
		height: calc(98rpx + env(safe-area-inset-bottom) / 2);
		padding-bottom: calc(env(safe-area-inset-bottom) / 2);

		.iconfont {
			font-size: 40rpx;
		}

		.text {
			font-size: 22rpx;
			margin-top: 5rpx;
			height: 32rpx;
		}
	}

	.no-tabbar {
		text-align: center;
		font-size: 28rpx;
		color: #999;
		padding: 20rpx;
	}
</style>