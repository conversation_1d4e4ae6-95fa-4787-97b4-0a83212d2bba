<template>
	<view class="page">
		<view class="header">
			<view class="title">总体评价</view>
			<view class=""><u-rate count="5" v-model="form.star"></u-rate></view>
		</view>
		<view class="mid">
			<textarea v-model="form.text" placeholder="请输入评价内容"></textarea>
			<upload @upload="imgUpload" @del="imgUpload" :imagelist="form.imgs" imgtype="imgs" text="上传图片" :imgsize="3">
			</upload>
		</view>
		<view class="btn" @click="submit">发表评价</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				form: {
					orderId: '',
					text: '',
					star: 0,
					huodongpingjia:0,
					imgs: [],
					serviceId:''
				}
			}
		},
		methods: {
			submit() {
				
			if(this.huodongpingjia===1){
				let obj = JSON.parse(JSON.stringify(this.form))
				obj.imgs = obj.imgs.map(item=>{
					item = item.path
					return item
				})
				obj.imgs = obj.imgs.join(',')
				this.$api.service.addeva(obj).then(res => {
					uni.showToast({
						icon: 'success',
						title: '发表成功'
					})
					uni.$emit('cancelOr')
					setTimeout(() => {
						uni.navigateBack()
					}, 1000)
				})
			}else{
				let obj = JSON.parse(JSON.stringify(this.form))
				obj.imgs = obj.imgs.map(item=>{
					item = item.path
					return item
				})
				obj.imgs = obj.imgs.join(',')
				this.$api.service.huodongpingjia(obj).then(res => {
					uni.showToast({
						icon: 'success',
						title: '发表成功'
					})
					uni.$emit('cancelOr')
					setTimeout(() => {
						uni.navigateBack()
					}, 1000)
				})
			}
			},
			imgUpload(e) {
				let {
					imagelist,
					imgtype
				} = e;
				this.form[imgtype] = imagelist;
			},
		},
		onLoad(options) {
			console.log(options)
			this.huodongpingjia=options.huodong?0:1
			this.form.orderId = options.id
			this.form.serviceId = options.goodsId
		}
	}
</script>

<style scoped lang="scss">
	.page {
		.header {
			border-top: 2rpx solid #f3f3f3;
			border-bottom: 2rpx solid #f3f3f3;
			padding: 36rpx 30rpx;

			.title {
				margin-bottom: 20rpx;
			}
		}

		.mid {
			padding: 30rpx;
			textarea{
				margin-bottom: 20rpx;
			}
		}

		.btn {
			width: 686rpx;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 50rpx 50rpx 50rpx 50rpx;
			line-height: 98rpx;
			text-align: center;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
			position: absolute;
			bottom: 42rpx;
			left: 30rpx;
		}
	}
</style>