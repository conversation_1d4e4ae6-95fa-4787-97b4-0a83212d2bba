<template>
	<view class="page">
		<view class="" v-if="list.length>0">
			<view class="record_item" v-for="(item,index) in list" :key="index">
				<view class="left"></view>
				<view class="mid">
					<view class="title">{{item.title}}</view>
					<view class="ctx">{{item.brand}}<span></span>{{item.time}}</view>
					<view class="address">
						<u-icon name="map-fill" color="#ADADAD" size="14"></u-icon>
						<text>{{item.address}}</text>
					</view>
				</view>
				<view class="right">
					<image src="../static/images/9362.png" mode=""></image>
				</view>
			</view>
		</view>
		<u-empty
				v-else
		        mode="list"
		        icon="http://cdn.uviewui.com/uview/empty/list.png"
		>
		</u-empty>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list:[],
				page:1
			}
		},
		methods: {
			async getList(){
				const res = await this.$api.mine.getUserBxList({page:this.page,limit:10})
				res.data.forEach(item=>{
					item.time = item.time ==0?'':this.$util.timestampToTime(item.time*1000)
				})
				this.list = res.data
			}
		},
		onLoad() {
			this.getList()
		}
	}
</script>

<style scoped lang="scss">
.page{
	background-color: #F8F8F8;
	height: 100vh;
	overflow: auto;
	padding: 40rpx 30rpx;
	.record_item{
		margin-bottom: 20rpx;
		width: 690rpx;
		height: 196rpx;
		background: #FFFFFF;
		border-radius: 10rpx 18rpx 18rpx 10rpx;
		display: flex;
		overflow: hidden;
		.left{
			width: 10rpx;
			height: 100%;
			background-color: #2E80FE;
		}
		.mid{
			width: 606rpx;
			margin-left: 20rpx;
			padding-top: 20rpx;
			.title{
				max-width: 500rpx;
				font-size: 28rpx;
				font-weight: 500;
				color: #333333;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			.ctx{
				max-width: 500rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				margin-top: 12rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: #333333;
				span{
					// color: #E9E9E9;
					display: inline-block;
					width: 40rpx;
				}
			}
			.address{
				margin-top: 42rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: #ADADAD;
				display: flex;
				align-content: center;
				text{
					margin-left: 8rpx;
				}
			}
		}
		.right{
			height: 100%;
			display: flex;
			align-items: center;
			image{
				width: 40rpx;
				height: 40rpx;
			}
		}
	}
}
</style>
