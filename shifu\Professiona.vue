<template>
	<view class="container">
		<!-- 列表菜单 -->
		<view class="menu-item" v-for="(item, index) in menuItems" :key="index" @click="handleItemClick(item)">
			<text class="item-text">{{ item.name }}</text>
			<!-- 显示已上传的图片标志 -->
			<view class="image-status">
				<text v-if="form[item.imgField]" class="uploaded">已上传</text>
				<text class="arrow">></text>
			</view>
		</view>

		<!-- 弹窗 -->
		<u-modal
			:show="showModal"
			:title="'开通' + selectedItem.name"
			:showCancelButton="true"
			confirmText="确定"
			cancelText="取消"
			@confirm="confirmUpload"
			@cancel="closeModal"
		>
			<view class="modal-content">
				<view class="upload-box">
					<!-- 只在没有图片时显示上传按钮 -->
					<view v-if="!getImage()" class="upload-button" @click="chooseImage">
						<view class="upload-icon">
							<text class="icon">+</text>
						</view>
						<text class="upload-text">上传证件图片</text>
					</view>
					
					<!-- 显示上传的图片 -->
					<view v-if="getImage()" class="image-preview">
						<image :src="getImage()" mode="aspectFit" style="width: 200px; height: 200px;"></image>
						<view class="delete-button" @click="deleteImage">删除</view>
					</view>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
export default {
	data() {
		return {
			menuItems: [
				{ name: '电工证', imgField: 'electricianImg' },
				{ name: '驾驶证', imgField: 'driverLicenseImg' },
				{ name: '制冷与空调作业证', imgField: 'workPermitImg' },
				{ name: '燃气具安装维修资质', imgField: 'maintenanceCertificateImg' },
				{ name: '高空作业A类证', imgField: 'gkzyAImg' },
				{ name: '高空作业B类证', imgField: 'gkzyBImg' },
				{ name: '开锁备案', imgField: 'ksbaImg' },
				{ name: '焊工证', imgField: 'hgzImg' },
				{ name: '弱电A类证', imgField: 'rdAImg' },
				{ name: '弱电B类证', imgField: 'rdBImg' },
				{ name: '特种行业许可证', imgField: 'tzhyhkzImg' },
				{ name: '其他', imgField: 'otherImg' }
			],
			showModal: false,
			Info: {},
			selectedItem: {},
			form: {
				electricianImg: '',
				driverLicenseImg: '',
				workPermitImg: '',
				maintenanceCertificateImg: '',
				gkzyAImg: '',
				gkzyBImg: '',
				ksbaImg: '',
				hgzImg: '',
				rdAImg: '',
				rdBImg: '',
				tzhyhkzImg: '',
				otherImg: '',
			},
		}
	},
	
	onLoad() {
		this.fetchMasterData();
	},
	methods: {
		async fetchMasterData() {
			try {
				const res = await this.$api.shifu.getCertList();
				if (res && res.data) {
					console.log('API Response:', res.data);
					
					// Set Info with the complete response data
					this.Info = {...res.data};
					
					// Initialize form with all image fields
					const formData = {};
					this.menuItems.forEach(item => {
						const imageValue = res.data[item.imgField] && res.data[item.imgField] !== 'null' 
							? res.data[item.imgField] 
							: '';
						formData[item.imgField] = imageValue;
					});
					
					this.form = {...this.form, ...formData};
				} else {
					console.error('No data received from getCertList');
					this.$util.showToast({
						icon: 'error',
						title: '获取证件信息失败',
					});
				}
			} catch (error) {
				console.error('Error fetching certificate data:', error);
				this.$util.showToast({
					icon: 'error',
					title: '网络错误，请重试',
				});
			}
		},
		handleItemClick(item) {
			this.selectedItem = item;
			this.showModal = true;
		},
		closeModal() {
			this.showModal = false;
		},
		getImage() {
			return this.form[this.selectedItem.imgField] || '';
		},
		chooseImage() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: async (res) => {
					await this.uploadImage(res.tempFilePaths[0]);
				},
				fail: (err) => {
					console.error('选择图片失败:', err);
				}
			});
		},
		async uploadImage(tempFilePath) {
			this.$util.showLoading({
				title: '上传中'
			});
			
			try {
				const response = await this.$api.base.uploadFile({
					filePath: tempFilePath,
					name: 'multipartFile',
					formData: {
						type: 'picture',
					},
				});

				if (response) {
					this.$set(this.form, this.selectedItem.imgField, response);
					this.$util.hideAll();
					uni.showToast({
						title: '上传成功',
						icon: 'success'
					});
				} else {
					throw new Error(response?.msg || '上传失败');
				}
			} catch (error) {
				this.$util.hideAll();
				console.error('上传失败:', error);
				uni.showToast({
					title: error.message || '上传失败，请重试',
					icon: 'none'
				});
			}
		},
	async deleteImage() {
	  try {
	    this.$util.showLoading({ title: '删除中...' });
	    
	    // Prepare data for deletion
	    const deleteData = {
	      ...this.Info,
	      [this.selectedItem.imgField]: null // Set the image field to empty
	    };
	    
	    // Call delete API
	    const res = await this.$api.shifu.delCertList(deleteData);
	    
	    // Handle successful deletion (even if response is just a string)
	    if (res.code ==='200' ) {
	      // Update local state
	      this.$set(this.form, this.selectedItem.imgField, null);
	      this.Info[this.selectedItem.imgField] = null;
	      
	      // Show success message
	      this.$util.showToast({
	        title: '删除成功',
	        icon: 'success'
	      });
	      
	      // Close the modal immediately
	      this.showModal = false;
	      
	      // Optional: refresh data from server
	      await this.fetchMasterData();
	    } else {
	      // If response doesn't indicate success
	      throw new Error(res?.msg || '删除失败');
	    }
	  } catch (error) {
	    console.error('删除操作:', error);
	    // Special handling for "删除成功" message that comes as error
	    if (error.message.includes('删除成功')) {
	      this.$set(this.form, this.selectedItem.imgField, null);
	      this.Info[this.selectedItem.imgField] = null;
	      this.showModal = false;
	      this.$util.showToast({
	        title: '删除成功',
	        icon: 'success'
	      });
	    } else {
	      this.$util.showToast({
	        icon: 'error',
	        title: error.message || '删除失败，请重试',
	      });
	    }
	  } finally {
	    this.$util.hideAll();
	  }
	},
		async confirmUpload() {
			const currentImage = this.form[this.selectedItem.imgField];
			if (!currentImage) {
				this.$util.showToast({
					icon: 'none',
					title: '请上传证件图片',
				});
				return;
			}

			try {
				const updatedInfo = {
					...this.Info,
					[this.selectedItem.imgField]: currentImage,
				};
				
				this.$set(this, 'Info', updatedInfo);
				
				const res = await this.$api.shifu.postCertList(this.Info);
				if(res === "信息修改成功，请等待审核"){
					this.$util.showToast({
						title: '信息修改成功，请等待审核',
						icon: 'success',
						duration: 1500,
					});
				}
				
				this.showModal = false;
			} catch (error) {
				console.error('Upload error:', error);
				this.$util.showToast({
					icon: 'error',
					title: '上传失败，请重试',
				});
			}
		},
	}
}
</script>

<style scoped lang="scss">
.header {
	width: 750rpx;
	height: 58rpx;
	background: #fff7f1;
	line-height: 58rpx;
	text-align: center;
	font-size: 28rpx;
	font-weight: 400;
}

.container {
	background-color: #f7f9fc;
	min-height: 100vh;
	padding: 10px;
}

.menu-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px 16px;
	background-color: #ffffff;
	border-radius: 8px;
	margin-bottom: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.item-text {
	font-size: 16px;
	color: #2c3e50;
	font-weight: 500;
}

.image-status {
	display: flex;
	align-items: center;
}

.uploaded {
	color: #28a745;
	font-size: 14px;
	margin-right: 8px;
}

.arrow {
	color: #7f8c8d;
	font-size: 16px;
}

.modal-content {
	padding: 20px;
	text-align: center;
}

.upload-box {
	margin: 15px 0;
}

.upload-button {
	width: 200px;
	height: 200px;
	border: 1px dashed #cccccc;
	border-radius: 8px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin: 0 auto;
	cursor: pointer;
}

.upload-icon {
	width: 60px;
	height: 60px;
	border-radius: 30px;
	background-color: #f2f2f2;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 10px;
}

.icon {
	font-size: 36px;
	color: #999999;
}

.upload-text {
	font-size: 14px;
	color: #666666;
}

.image-preview {
	position: relative;
	width: 200px;
	margin: 0 auto;
}

.delete-button {
	position: absolute;
	bottom: -30px;
	left: 50%;
	transform: translateX(-50%);
	background-color: #ff4d4f;
	color: white;
	padding: 5px 15px;
	border-radius: 4px;
	font-size: 14px;
	cursor: pointer;
}
</style>