<template>
  <view class="page">
    <view class="main">
      <!-- 左侧父类分类列表 -->
      <view class="left">
        <scroll-view scroll-y="true" class="scrollL">
          <view v-if="loading" class="loading">
            <text>加载中...</text>
          </view>
          <view v-else-if="error" class="error">
            <text>{{ error }}</text>
          </view>
          <view v-else-if="!categories.length" class="no-content">
            <text>暂无分类数据</text>
          </view>
          <view
            v-else
            class="left_item"
            v-for="category in categories"
            :key="category.id"
            @tap="selectCategory(category.id)"
            :class="{ active: selectedCategoryId === category.id }"
          >
            <view class="category_name">{{ category.name }}</view>
          </view>
        </scroll-view>
      </view>

      <!-- 右侧子类和服务列表 -->
      <view class="right">
        <scroll-view scroll-y="true" class="scrollR">
          <view v-if="loading" class="loading">
            <text>加载中...</text>
          </view>
          <view v-else-if="error" class="error">
            <text>{{ error }}</text>
          </view>
          <view v-else-if="currentCategory && currentCategory.children && currentCategory.children.length">
            <!-- 全选功能 -->
            <view class="select_all_section">
              <view class="total_count">总数: {{ getTotalSubCategoriesCount() }}</view>
              <view class="select_all_btn" @click="selectAllSubCategories()">
                {{ isAllSubCategoriesSelected() ? '取消全选' : '全选' }}
              </view>
            </view>

            <!-- 循环子类分类 -->
            <view
              class="subcategory_section"
              v-for="subCategory in currentCategory.children"
              :key="subCategory.id"
            >
              <!-- 子类名称，点击可选中/取消选中 -->
              <view
                class="subcategory_item"
                @click="toggleSelectSubCategory(subCategory.id)"
                :class="{ active: isSubCategorySelected(subCategory.id) }"
              >
                {{ subCategory.name }}
              </view>
            </view>
          </view>
          <view v-else class="no-content">
            <text>暂无子分类</text>
          </view>
        </scroll-view>
      </view>
    </view>
    <view class="footer">
      <button class="save_btn" @click="saveSettings">保存设置</button>
    </view>
  </view>
</template>

<script>
import $api from "@/api/index.js";

export default {
  data() {
    return {
      keyword: "",
      categories: [],
	  shifuId:'',
      selectedCategoryId: null,
      selectedSubCategories: [], // 存储选中的子类ID
      loading: false,
      shifuInfo: { serviceIds: [] }, // Initialize with default serviceIds
      error: null,
      dataBox: {}, // 存储用户选择的服务项目，按子类ID分组
    };
  },
  computed: {
    currentCategory() {
      if (!this.selectedCategoryId) return null;
      const category = this.categories.find(cat => cat.id === this.selectedCategoryId);
      console.log("Current category:", category);
      return category;
    }
  },
  methods: {
    // 选择父类分类
    selectCategory(id) {
      console.log("选择父类分类:", id);
      this.selectedCategoryId = id;
      this.$forceUpdate();
    },

    // 切换子类选择状态
    toggleSelectSubCategory(subCategoryId) {
      console.log("切换子类选择状态:", subCategoryId);
      const index = this.selectedSubCategories.indexOf(subCategoryId);

      if (index === -1) {
        // 选中子类
        this.selectedSubCategories.push(subCategoryId);
        this.selectAllServicesInSubCategory(subCategoryId);
      } else {
        // 取消选中子类
        this.selectedSubCategories.splice(index, 1);
        this.deselectAllServicesInSubCategory(subCategoryId);
      }
      this.$forceUpdate();
    },

    // 检查子类是否被选中
    isSubCategorySelected(subCategoryId) {
      return this.selectedSubCategories.includes(subCategoryId);
    },

    // 选中子类下的所有服务
    selectAllServicesInSubCategory(subCategoryId) {
      const subCategory = this.currentCategory.children.find(sub => sub.id === subCategoryId);
      if (!subCategory || !subCategory.serviceList || !subCategory.serviceList.length) return;

      // 确保dataBox中有该子类的数据结构
      if (!this.dataBox[subCategoryId]) {
        this.$set(this.dataBox, subCategoryId, {
          selectedItems: [],
          count: 0
        });
      }

      const allServiceIds = subCategory.serviceList.map(service => service.id);

      // 将所有服务ID添加到选中列表
      allServiceIds.forEach(serviceId => {
        if (!this.dataBox[subCategoryId].selectedItems.includes(serviceId)) {
          this.dataBox[subCategoryId].selectedItems.push(serviceId);
        }
        if (!this.shifuInfo.serviceIds.includes(serviceId)) {
          this.shifuInfo.serviceIds.push(serviceId);
        }
      });

      // 更新计数
      this.dataBox[subCategoryId].count = this.dataBox[subCategoryId].selectedItems.length;
    },

    // 取消选中子类下的所有服务
    deselectAllServicesInSubCategory(subCategoryId) {
      const subCategory = this.currentCategory.children.find(sub => sub.id === subCategoryId);
      if (!subCategory || !subCategory.serviceList || !subCategory.serviceList.length) return;

      if (!this.dataBox[subCategoryId]) return;

      const allServiceIds = subCategory.serviceList.map(service => service.id);

      // 从选中列表中移除所有服务ID
      this.dataBox[subCategoryId].selectedItems = [];
      this.shifuInfo.serviceIds = this.shifuInfo.serviceIds.filter(id => !allServiceIds.includes(id));

      // 更新计数
      this.dataBox[subCategoryId].count = 0;
    },

    // 获取当前分类下子类的总数
    getTotalSubCategoriesCount() {
      if (!this.currentCategory || !this.currentCategory.children) return 0;
      return this.currentCategory.children.length;
    },

    // 检查是否所有子类都被选中
    isAllSubCategoriesSelected() {
      if (!this.currentCategory || !this.currentCategory.children) return false;
      const allSubCategoryIds = this.currentCategory.children.map(sub => sub.id);
      return allSubCategoryIds.length > 0 && allSubCategoryIds.every(id => this.selectedSubCategories.includes(id));
    },

    // 全选/取消全选所有子类
    selectAllSubCategories() {
      if (!this.currentCategory || !this.currentCategory.children) return;

      const allSubCategoryIds = this.currentCategory.children.map(sub => sub.id);
      const isAllCurrentlySelected = this.isAllSubCategoriesSelected();

      if (isAllCurrentlySelected) {
        // 取消全选
        this.selectedSubCategories = this.selectedSubCategories.filter(id => !allSubCategoryIds.includes(id));
        allSubCategoryIds.forEach(subCategoryId => {
          this.deselectAllServicesInSubCategory(subCategoryId);
        });
      } else {
        // 全选
        allSubCategoryIds.forEach(subCategoryId => {
          if (!this.selectedSubCategories.includes(subCategoryId)) {
            this.selectedSubCategories.push(subCategoryId);
          }
          this.selectAllServicesInSubCategory(subCategoryId);
        });
      }
      this.$forceUpdate();
    },


    
    // 保存设置
    async saveSettings() {
      try {
        // Save shifuInfo.serviceIds as a comma-separated string
        const serviceIdsString = this.shifuInfo.serviceIds.join(',');
        uni.setStorageSync('selectedServices', serviceIdsString);
        console.log("Saved selectedServices:", serviceIdsString);
        
        // Ensure shifuInfo is initialized
        if (!this.shifuInfo) {
          this.shifuInfo = { serviceIds: [] };
        }
        let userId = this.shifuId;
        let mobile = uni.getStorageSync('phone') || '';
        // Prepare shifuInfo with serviceIds as a comma-separated string
        const shifuInfoToSend = {
          ...this.shifuInfo,
          userId: userId,
          mobile: mobile,
          serviceIds: this.shifuInfo.serviceIds.join(',')
        };
        
        console.log("Saving shifuInfo:", shifuInfoToSend);
        const res = await this.$api.shifu.updataSkill(JSON.stringify(shifuInfoToSend));
        console.log("API Response from updataInfoSF:", res);
        if(res.code==='-1'){
	  
			    uni.showToast({
			      title: res.msg,
			      icon: 'none'
			    });
					  setTimeout(() => {
					    uni.navigateBack({ delta: 1 });
					  }, 2000);
			 
			  } else {
				  uni.showToast({
				    title: res.msg,
				    icon: 'none',
				    duration: 2000,
				    success: () => {
				      setTimeout(() => {
				        uni.navigateBack({ delta: 1 });
				      }, 2000);
				    }
				  });
				  
			
			  }
			} catch (e) {
			  uni.showToast({
			    title: '保存失败',
			    icon: 'none'
			  });
			  console.error('保存失败:', e);
			}
			
			
			
		
    
    },
    goUrl(url) {
      uni.navigateTo({ url });
    },
    
    // 获取分类列表
    async getList() {
      this.loading = true;
      this.error = null;
      try {
        const response = await $api.shifu.getSkill();
        console.log("API Response:", response);

        // 处理响应数据
        let categoriesData = [];
        if (Array.isArray(response)) {
          categoriesData = response;
        } else if (response.data && Array.isArray(response.data)) {
          categoriesData = response.data;
        } else {
          throw new Error("无效或空的数据");
        }
        
        // 确保children和serviceList存在，并初始化dataBox
        categoriesData.forEach(category => {
          if (!category.children) category.children = [];
          category.children.forEach(subCategory => {
            if (!subCategory.serviceList) subCategory.serviceList = [];
            if (!this.dataBox[subCategory.id]) {
              this.$set(this.dataBox, subCategory.id, {
                selectedItems: [],
                count: 0
              });
            }
          });
        });
        
        this.categories = categoriesData;
        console.log("Categories processed:", this.categories);
        
        if (this.categories.length > 0) {
          this.selectedCategoryId = this.categories[0].id;
        } else {
          this.error = "分类数据为空";
          uni.showToast({
            title: "分类数据为空",
            icon: "none"
          });
        }
      } catch (err) {
        this.error = "数据加载失败: " + err.message;
        console.error("Error in getList:", err);
        uni.showToast({
          title: this.error,
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    
    // 加载保存的选择（仅作为API失败的备用）
    loadSavedSelections() {
      try {
        const savedData = uni.getStorageSync('selectedServices');
        if (savedData && savedData.trim()) {
          this.shifuInfo.serviceIds = savedData
            .split(',')
            .map(id => parseInt(id.trim(), 10))
            .filter(id => !isNaN(id));
        } else {
          this.shifuInfo.serviceIds = [];
        }
        
        // Reconstruct dataBox and selectedSubCategories from serviceIds
        this.selectedSubCategories = [];
        this.categories.forEach(category => {
          if (category.children && category.children.length) {
            category.children.forEach(subCategory => {
              if (subCategory.serviceList && subCategory.serviceList.length) {
                if (!this.dataBox[subCategory.id]) {
                  this.$set(this.dataBox, subCategory.id, {
                    selectedItems: [],
                    count: 0
                  });
                }
                const matchingServiceIds = this.shifuInfo.serviceIds.filter(serviceId =>
                  subCategory.serviceList.some(service => service.id === serviceId)
                );
                this.dataBox[subCategory.id].selectedItems = matchingServiceIds;
                this.dataBox[subCategory.id].count = matchingServiceIds.length;

                // 如果该子类下有选中的服务，则将子类标记为选中
                if (matchingServiceIds.length > 0) {
                  this.selectedSubCategories.push(subCategory.id);
                }
              }
            });
          }
        });
        
        console.log("Loaded shifuInfo.serviceIds from storage:", this.shifuInfo.serviceIds);
        console.log("Reconstructed dataBox:", this.dataBox);
        this.$forceUpdate();
      } catch (e) {
        console.error('加载已保存选择失败:', e);
        this.shifuInfo.serviceIds = [];
      }
    },
    
    // 获取并初始化服务ID
    async getInfoS() {
      try {
        const res = await $api.shifu.getSInfo();
        console.log("getSInfo Response:", res);
        
        // Initialize shifuInfo
        this.shifuInfo = res && typeof res === 'object' ? res : { serviceIds: [] };
        this.shifuId=res.data.id
        // Always use API serviceIds if available
        let serviceIdsArray = [];
        if (typeof res.serviceIds === 'string' && res.serviceIds.trim() !== '') {
          serviceIdsArray = res.serviceIds
            .split(',')
            .map(id => parseInt(id.trim(), 10))
            .filter(id => !isNaN(id));
        } else if (Array.isArray(res.serviceIds)) {
          serviceIdsArray = res.serviceIds.map(id => parseInt(id, 10)).filter(id => !isNaN(id));
        }
        this.shifuInfo.serviceIds = serviceIdsArray;
        
        // If API provides no valid serviceIds, try local storage
        if (!this.shifuInfo.serviceIds.length) {
          this.loadSavedSelections();
        }
        
        console.log("Processed Service IDs:", this.shifuInfo.serviceIds);

        // Update dataBox and selectedSubCategories based on shifuInfo.serviceIds
        this.dataBox = {};
        this.selectedSubCategories = [];
        this.categories.forEach(category => {
          if (category.children && category.children.length) {
            category.children.forEach(subCategory => {
              if (subCategory.serviceList && subCategory.serviceList.length) {
                this.$set(this.dataBox, subCategory.id, {
                  selectedItems: [],
                  count: 0
                });
                const matchingServiceIds = this.shifuInfo.serviceIds.filter(serviceId =>
                  subCategory.serviceList.some(service => service.id === serviceId)
                );
                this.dataBox[subCategory.id].selectedItems = matchingServiceIds;
                this.dataBox[subCategory.id].count = matchingServiceIds.length;

                // 如果该子类下有选中的服务，则将子类标记为选中
                if (matchingServiceIds.length > 0) {
                  this.selectedSubCategories.push(subCategory.id);
                }
              }
            });
          }
        });

        console.log("Updated dataBox:", this.dataBox);
        console.log("Updated shifuInfo.serviceIds:", this.shifuInfo.serviceIds);
        this.$forceUpdate();
      } catch (err) {
        console.error("Error in getInfoS:", err);
        this.shifuInfo = { serviceIds: [] };
        this.loadSavedSelections(); // Fallback to local storage on API failure
        uni.showToast({
          title: "加载服务信息失败",
          icon: "none"
        });
      }
    }
  },
  async onLoad() {
    try {
      const city = uni.getStorageSync("city");
      console.log("City:", city);
      // Clear selectedServices to start fresh
      uni.setStorageSync('selectedServices', '');
      await this.getList();
      await this.getInfoS();
    } catch (err) {
      console.error("Error in onLoad:", err);
      uni.showToast({
        title: "页面加载失败",
        icon: "none"
      });
    }
  },
};
</script>

<style scoped>
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为固定footer预留空间 */
}

.main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left {
  width: 190rpx;
  background-color: #f8f8f8;
}

.scrollL {
  height: 100%;
  overflow-y: auto;
}

.left_item {
  padding: 0 20rpx;
  min-height: 100rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  border-left: 6rpx solid transparent;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
}

.left_item.active {
  color: #2e80fe;
  font-size: 30rpx;
  border-left-color: #2e80fe;
  background-color: #fff;
}

.category_name {
  height: 100rpx;
  width: 100%;
  display: flex;
  align-items: center;
}

.right {
  flex: 1;
  background-color: #fff;
  border-radius: 12rpx 12rpx 0 0;
  margin-left: 10rpx;
}

.scrollR {
  height: 100%;
  overflow-y: auto;
}

.select_all_section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f0f8ff;
  border-bottom: 1rpx solid #e0e0e0;
  margin-bottom: 10rpx;
}

.total_count {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.select_all_btn {
  font-size: 28rpx;
  color: #2e80fe;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border: 1rpx solid #2e80fe;
  border-radius: 6rpx;
  background-color: #fff;
}

.subcategory_section {
  margin-bottom: 10rpx;
}

.subcategory_item {
  padding: 25rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 30rpx;
  color: #333;
  transition: all 0.3s;
  cursor: pointer;
}

.subcategory_item.active {
  background-color: #e6f0ff;
  color: #2e80fe;
  font-weight: 500;
  border-left: 4rpx solid #2e80fe;
}

.no-services,
.no-content,
.loading,
.error {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx;
}

.error {
  color: #ff4d4f;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  padding: 15rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.save_btn {
  width: 90%;
  height: 90rpx;
  background-color: #2e80fe;
  color: white;
  border-radius: 45rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>