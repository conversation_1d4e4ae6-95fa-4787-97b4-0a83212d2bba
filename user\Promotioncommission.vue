<template>
	<view class="page">
		<view class="list_item" v-for="(item, index) in list" :key="index">
			<view class="left">
				<image src="../static/images/8957.png" mode=""></image>
			</view>
			<view class="mid">
				<view class="right">{{item.type === 1 ? "推广" : "提现"}}</view>
				<view class="time">{{$util.timestampToTime(item.createTime * 1000)}}</view>
			</view>
			<view class="right">{{item.type === 1 ? "+" : "-"}}{{item.price}}</view>
		</view>
		<!-- <u-loadmore :status="status" /> -->
	</view>
</template>

<script>
export default {
	data() {
		return {
			list: [],
			status: 'loadmore',
			page: 1,
			limit: 15,
			total: 0, // Initialize total count
			loading: false // Prevent multiple simultaneous requests
		}
	},
	onPullDownRefresh() {
		console.log('refresh');
		uni.showLoading({
			title: '刷新中...'
		});
		// Reset state
		this.page = 1;
		this.list = [];
		this.status = 'loadmore';
		this.total = 0;
		
		// Fetch new data with minimum delay for better UX
		Promise.all([
			this.getList(),
			new Promise(resolve => setTimeout(resolve, 500))
		]).then(() => {
			uni.hideLoading();
			uni.stopPullDownRefresh();
		}).catch(() => {
			uni.hideLoading();
			uni.showToast({
				title: '刷新失败，请重试',
				icon: 'none'
			});
			uni.stopPullDownRefresh();
		});
	},
	onReachBottom() {
		// Triggered when user scrolls to the bottom
		if (this.list.length < this.total && !this.loading) {
			this.status = 'loading';
			this.page++;
			this.getList();
		}
	},
	onLoad() {
		this.getList();
	},
	methods: {
		getList() {
			if (this.loading) return; // Prevent multiple requests
			this.loading = true;
			return this.$api.service.userWater({
				pageNum: this.page,
				pageSize: this.limit
			}).then(res => {
				console.log(res);
				this.total = res.totalCount; // Update total count from API response
				// Append new items to the list
				this.list = this.page === 1 ? res.list : [...this.list, ...res.list];
				if (this.list.length >= this.total) {
					this.status = 'nomore';
				} else {
					this.status = 'loadmore';
				}
			}).catch(err => {
				uni.showToast({
					title: '加载失败，请重试',
					icon: 'none'
				});
				this.status = 'nomore'; // Prevent further loading on error
				throw err; // Re-throw to handle in refresh
			}).finally(() => {
				this.loading = false;
			});
		}
	}
}
</script>

<style scoped lang="scss">
.page {
	padding: 44rpx 30rpx;

	.list_item {
		height: 102rpx;
		display: flex;

		.left {
			width: 78rpx;
			height: 78rpx;
			border-radius: 50%;
			background: #F9F9F9;
			display: flex;
			justify-content: center;
			align-items: center;

			image {
				width: 33rpx;
				height: 31rpx;
			}
		}

		.mid {
			margin-left: 20rpx;
			width: 520rpx;

			.name1 {
				font-size: 28rpx;
				font-weight: 500;
				color: #171717;
				max-width: 500rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			.time {
				margin-top: 12rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: #999999;
			}
		}

		.right {
			width: 92rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			font-weight: 500;
			color: #171717;
		}
	}
}
</style>