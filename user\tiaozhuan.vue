<template>
	<view class="loading-container">
		<u-loading-icon text="加载中" size="28"></u-loading-icon>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		onLoad() {
			// 跳转逻辑保持不变
			uni.navigateTo({
			      url: '/user/order_list?from=tiaozhuan'
			});
		},
		methods: {
			
		}
	}
</script>

<style>
	/* 关键样式：让加载动画垂直水平居中 */
	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100vw;
		height: 100vh;
	}
</style>