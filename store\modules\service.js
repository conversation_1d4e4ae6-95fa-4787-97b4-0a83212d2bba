import $api from "@/api/index.js";

export default {
  state: {
	  // 师傅
	  //地理位置
	  regeocode: {},
    orderInfo: {},
    type: 0,
    position: "", // 已存在，用于存储城市名称，如 '北京市'
    pageActive: false,
	refreshReceiving: false,
    activeIndex: 0,
    tabList: [
      {
        title: "全部",
        sort: "top desc",
      },
      {
        title: "价格",
        sort: "price",
        sign: 0,
        is_sign: 1,
      },
      {
        title: "销量",
        sort: "total_sale",
        sign: 0,
        is_sign: 1,
      },
      {
        title: "好评度",
        sort: "star",
        sign: 0,
        is_sign: 1,
      },
    ],
    param: {
      page: 1,
      sort: "",
    },
    list: {
      data: [],
      last_page: 1,
      current_page: 1,
    },
    banner: [],
    
  },
  mutations: {
	  
	  
	  //地理位置
	  SET_REGEOCODE(state, regeocode) {
	      state.regeocode = regeocode;
	    },
	  setRefreshReceiving(state, value) {
	        state.refreshReceiving = value;
	      },
  updatePosition(state, { key, val }) {
	  console.log(key, val)
        state[key] = val; // Synchronous mutation
      },
    changeType(state, data) {
      state.type = data;
    },
    changeOrderInfo(state, data) {
      state.orderInfo = data;
    },
  },
  actions: {
	  setRegeocode({ commit }, regeocode) {
	      commit('SET_REGEOCODE', regeocode);
	    },
	  
	  
	  
	  triggerRefreshReceiving({ commit }, value) {
	        commit('setRefreshReceiving', value);
	      },
    // 设置 position

    // 调用 serviceCate 接口

    // 已有方法保持不变
    async getServiceIndex({ commit, state }, param) {
      let d = await $api.service.index(param);
      let { banner } = d;
      commit("updateServiceItem", {
        key: "banner",
        val: banner,
      });
    },
    async getServiceList({ commit, state }, param) {
      let d = await $api.service.serviceList(param);
      let oldList = state.list;
      let newList = d;
      let list = {};
      if (param.page == 1) {
        list = newList;
      } else {
        newList.data = oldList.data.concat(newList.data);
        list = newList;
      }
      commit("updateServiceItem", {
        key: "list",
        val: list,
      });
    },
  },
};