<template>
	<view class="page">
		<view class="ok">
			<u-icon name="checkbox-mark" color="#fff" size="48"></u-icon>
		</view>
		<view class="title">申请成功</view>
		<view class="ctx">服务人员将尽快与您联系</view>
		<view class="btn" @click="goUrl">查看订单</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id:''
			}
		},
		methods: {
			goUrl() {
				uni.navigateTo({
					url: `/pages/order_details?id=${this.id}`
				})
			}
		},
		onUnload() {
			uni.navigateBack({
				delta: 9
			});
		},
		onLoad(options){
			this.id = options.id
		}
	}
</script>

<style scoped lang="scss">
	.page {
		padding-top: 200rpx;

		.ok {
			width: 180rpx;
			height: 180rpx;
			margin: 0 auto;
			background-color: #2e80fe;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.title {
			margin-top: 40rpx;
			font-size: 40rpx;
			font-weight: 500;
			color: #333333;
			text-align: center;
		}

		.ctx {
			margin: 0 auto;
			margin-top: 40rpx;
			width: 420rpx;
			height: 80rpx;
			font-size: 28rpx;
			font-weight: 400;
			color: #333;
			text-align: center;
		}

		.btn {
			margin: 0 auto;
			margin-top: 52rpx;
			width: 690rpx;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 50rpx 50rpx 50rpx 50rpx;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 98rpx;
			text-align: center;
		}
	}
</style>