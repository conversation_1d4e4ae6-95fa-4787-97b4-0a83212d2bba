<template>
  <view class="page" v-if="ready">
    <view class="box">
      <!-- Title Section -->
      <view class="title">
        <image src="../static/images/8957.png" mode="aspectFill" class="title-icon"></image>
        <span>订单信息</span>
      </view>

      <!-- Order Info Section -->
      <view class="info-box">
        <view class="info-item" v-for="(info, index) in orderDetails" :key="index">
          <text class="label">{{ info.label }}</text>
          <text 
            :class="['value', info.alignRight ? 'align-right' : '']"
            @click="info.label === '联系方式' ? phoneDLD(info.value) : null"
          >{{ info.value }}</text>
        </view>
        <view class="navigation-btn" @click="godh()">
          <image src="../static/images/9349.png" mode="aspectFill" class="nav-icon"></image>
          <text>导航</text>
        </view>
      </view>
	
      <!-- Dynamic Info Section (Images/Text) -->
      <view class="dynamic-section" v-if="Info.settingOrderList && Info.settingOrderList.length > 0" v-for="(item, index) in Info.settingOrderList" :key="index">
        <view class="title">{{ item.problemDesc || '详情信息' }}</view>
        <view class="img-box" v-if="item.val && isValidImageUrl(item.val)">
          <image
            v-for="(url, imgIndex) in splitImageUrls(item.val)"
            :key="imgIndex"
            :src="url.trim()"
            mode="aspectFill"
            @error="onImageError(url, index, imgIndex)"
            @click="previewImage(url)"
            class="dynamic-image"
          ></image>
        </view>
        <view v-else-if="item.val && item.val.toString().trim() !== ''" class="text-box">
          <text>{{ item.val }}</text>
        </view>
        <view v-else class="text-box">
          <text>无</text>
        </view>
      </view>

      <!-- 如果没有动态内容，显示提示 -->
      <view v-if="!Info.settingOrderList || Info.settingOrderList.length === 0" class="dynamic-section">
        <view class="title">详情信息</view>
        <view class="text-box">
          <text>暂无详情信息</text>
        </view>
      </view>

      <!-- Notes Section -->
      <view class="title">备注</view>
      <view class="notes-box">
        <text>{{ Info.text && Info.text.trim() !== '' ? Info.text : '无' }}</text>
      </view>
	  
      <!-- 售后信息 -->
      <view v-if="Info.aftermarket && Info.aftermarket.remark && Info.aftermarket.remark.trim() !== ''" class="">	
        <view class="title">售后</view>
        <view class="notes-box">
          <text>{{ Info.aftermarket.remark ? Info.aftermarket.remark : '无' }}</text>
        </view>
      </view>

      <!-- Image Preview Modal -->
      <view class="image-modal" v-if="showImageModal" @click="closeImageModal">
        <view class="modal-content" @click.stop>
          <image :src="currentImage" mode="aspectFit" class="modal-image"></image>
          <view class="close-btn" @click="closeImageModal">关闭</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      ready: false,
      id: '',
      selectedItem: {},
      payType: {},
      Info: {
        settingOrderList: [], // 初始化为空数组
        aftermarket: { remark: '' } // 初始化售后信息
      },
      imageErrors: [], // Track images that failed to load
      fallbackImage: '../static/images/placeholder.png', // Placeholder image path
      showImageModal: false, // Control image modal visibility
      currentImage: '' // Store the currently displayed image URL
    };
  },
  computed: {
    orderDetails() {
      return [
        { label: '订单单号', value: this.formatOrderCode(this.selectedItem.orderCode), alignRight: true },
        { label: '服务内容', value: this.selectedItem.goodsName || '' },
        { label: '下单时间', value: this.selectedItem.createTime|| '' },
        { label: '联系方式', value: this.formatMobile(this.selectedItem.mobile) },
        { label: '服务定位', value: this.formatAddress(this.selectedItem.addressInfo), alignRight: true },
        { label: '服务地址', value: this.formatAddress(this.selectedItem.address), alignRight: true },
        { label: '门牌号', value: this.formatHouseNumber(this.selectedItem.houseNumber), alignRight: true }
      ];
    }
  },
  methods: {
    // Format mobile number with ***** in the middle, unless payType.payType is 7, 3, 5, or 6
    formatMobile(mobile) {
      if (!mobile) return '';
      if (this.payType.payType) return mobile;
      
      return mobile;
    },
    // Format address to hide last 7 characters, unless payType.payType is 7, 3, 5, or 6
    formatAddress(address) {
      if (!address) return '';
      if (this.payType.payType) return address;
      
      return address;
    },
    // Format house number with ***** in the middle, unless payType.payType is 7, 3, 5, or 6
    formatHouseNumber(houseNumber) {
      if (!houseNumber) return '';
      if (this.payType.payType) return houseNumber;
  
      return houseNumber;
    },
    // Format orderCode (keeping original format but removing '无')
    formatOrderCode(orderCode) {
      return orderCode || '';
    },
    // Validate if the URL(s) contain valid image URLs
    isValidImageUrl(val) {
      if (!val || (typeof val !== 'string' && typeof val !== 'number')) return false;
      const valStr = val.toString();
      const urls = valStr.split(',').map(url => url.trim());
      const imageRegex = /^(https?:\/\/).*\.(png|jpg|jpeg|gif|bmp|webp)(\?.*)?$/i;
      return urls.some(url => imageRegex.test(url));
    },
    // Split comma-separated image URLs
    splitImageUrls(val) {
      if (!val) return [];
      const valStr = val.toString();
      return valStr.split(',').map(url => url.trim()).filter(url => url !== '');
    },
    // Handle navigation
    godh() {
      uni.openLocation({
        latitude: Number(this.selectedItem.lat) || 0,
        longitude: Number(this.selectedItem.lng) || 0,
        scale: 18,
        name: this.selectedItem.address || '未知地址',
        address: this.selectedItem.addressInfo || '未知地址信息',
        success: () => console.log('Navigation opened'),
        fail: err => console.error('Navigation error:', err)
      });
    },
    // Make phone call
    phoneDLD(phoneNumber) {
	if (!phoneNumber || phoneNumber.includes('*')) {
	    uni.showToast({
	      title: '无法拨打电话，号码不可用',
	      icon: 'none'
	    });
	    return;
	  }
      uni.makePhoneCall({
        phoneNumber: phoneNumber,
      });
    },
    // Fetch order details
    async getDetail() {
      try {
        const res = await this.$api.shifu.orderdetM(this.id);
        console.log('API Response:', res);
        console.log('coachStatus:', res.data.coachStatus);
        console.log('settingOrderList:', res.data.settingOrderList); // 添加调试日志
        
        if (res.data.coachStatus === 1) {
          uni.showToast({
            title: '师傅状态在审核中',
            icon: 'none'
          });
        }
        if (res.data.coachStatus === -1 || res.data.coachStatus === 4) {
          uni.showToast({
            title: '你还不是师傅',
            icon: 'none'
          });
        }
        
        // 确保数据结构完整
        this.Info = {
          settingOrderList: res.data.settingOrderList || [],
          aftermarket: res.data.aftermarket || { remark: '' },
          text: res.data.text || '',
          ...res.data
        };
        
        this.selectedItem = {
          orderCode: res.data.orderCode || '',
          goodsName: res.data.goodsName || '',
          createTime: res.data.createTime || 0,
          mobile: res.data.mobile || '',
          addressInfo: res.data.addressInfo || '',
          address: res.data.address || '',
          houseNumber: res.data.houseNumber || '',
          lat: res.data.lat || 0,
          lng: res.data.lng || 0
        };
        
        // 调试信息
        console.log('Final Info object:', this.Info);
        console.log('settingOrderList length:', this.Info.settingOrderList?.length);
        
      } catch (err) {
        console.error('API Error:', err);
        uni.showToast({
          title: '获取订单详情失败',
          icon: 'none'
        });
      }
    },
    // Handle image load errors
    onImageError(url, index, imgIndex) {
      console.error(`Failed to load image: ${url}`);
      this.imageErrors.push(`${index}-${imgIndex}`);
      if (this.Info.settingOrderList[index]) {
        const urls = this.splitImageUrls(this.Info.settingOrderList[index].val);
        urls[imgIndex] = this.fallbackImage;
        this.$set(this.Info.settingOrderList[index], 'val', urls.join(','));
      }
    },
    // Show image using uni.previewImage
    previewImage(url) {
      const currentItem = this.Info.settingOrderList.find(item => item.val && item.val.toString().includes(url));
      if (currentItem) {
        uni.previewImage({
          urls: this.splitImageUrls(currentItem.val),
          current: url,
        });
      }
    },
    // Close image modal
    closeImageModal() {
      this.showImageModal = false;
      this.currentImage = '';
    }
  },
  async onLoad(options) {
    this.id = options.id || '';
    this.payType = uni.getStorageSync('orderdetails') || {};
    console.log('payType:', this.payType);
    console.log('payType.payType:', this.payType.payType);
    console.log('Page options:', options);
    
    if (!this.id) {
      console.error('No order ID provided');
      uni.showToast({
        title: '订单ID缺失',
        icon: 'none'
      });
      return;
    }
    
    await this.getDetail();
    this.ready = true;
  },
  onUnload() {
    uni.removeStorageSync('orderdetails');
    console.log('Removed orderdetails from local storage');
  }
};
</script>

<style scoped lang="scss">
.page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f5f7fa 0%, #e4e7ed 100%);
  padding: 32rpx 24rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.box {
  margin: 0 auto;
  max-width: 690rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  padding: 32rpx;
}

/* Title Styling */
.title {
  display: flex;
  align-items: center;
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 24rpx;

  .title-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 16rpx;
  }
}

/* Info Box Styling */
.info-box {
  background: #fafafa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #e5e7eb;

  &:last-child {
    border-bottom: none;
  }

  .label {
    font-size: 28rpx;
    font-weight: 500;
    color: #6b7280;
  }

  .value {
    font-size: 30rpx;
    font-weight: 600;
    color: #1a1a1a;
    max-width: 420rpx;
    white-space: normal;
    word-break: break-all;
    line-height: 1.4;

    &.align-right {
      text-align: right;
    }
  }
}

.navigation-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 180rpx;
  height: 64rpx;
  background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
  border-radius: 32rpx;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
  margin-top: 24rpx;
  margin-left: auto;

  .nav-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 12rpx;
  }
}

/* Dynamic Section Styling */
.dynamic-section {
  margin-bottom: 32rpx;
}

.img-box {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 24rpx;
}

.dynamic-image {
  width: 196rpx;
  height: 196rpx;
  border-radius: 16rpx;
  border: 1rpx solid #e5e7eb;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* Image Modal Styling */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100vw;
  height: 100vh;
  position: relative;
}

.modal-image {
  width: 100vw;
  height: 100vh;
  object-fit: contain; /* Ensures image scales to fit without distortion */
  border-radius: 0; /* Remove border-radius for full-screen effect */
}

.close-btn {
  position: absolute;
  bottom: 20rpx;
  padding: 16rpx 32rpx;
  background: #ffffff;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: 500;
  z-index: 1100; /* Ensure button is above image */
}

/* Text and Notes Styling */
.text-box {
  margin-top: 24rpx;
  background: #f3f4f6;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  color: #4b5563;
}

.notes-box {
  margin-top: 24rpx;
  background: #f9fafb;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  color: #374151;
  line-height: 1.5;
  word-break: break-all;
}
</style>