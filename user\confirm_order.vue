<template>
	<view class="page">
		<u-modal :show="showChoose || showYh" :content='content'></u-modal>

		<!-- 优惠券弹出框 -->
		<view class="choose_yh" :style="showYh?'':'height:0'">
			<view class="head">优惠券</view>
			<view class="close" @click="showYh = false">
				<image src="../static/images/9397.png" mode=""></image>
			</view>
			<u-empty mode="coupon" icon="http://cdn.uviewui.com/uview/empty/coupon.png" v-if="couponlist.length == 0 && nocouponlist.length == 0">
			</u-empty>
			<scroll-view scroll-y="true" style="height: 832rpx;" v-else>
				<view class="cou_item" v-for="(item,index) in couponlist" :key="index">
					<view class="top">
						<view class="box1" v-if="item.type == 0">
							<span>满</span>{{item.full}}<span>减</span>{{item.discount}}
						</view>
						<view class="box1" v-else><span>￥</span>{{item.discount}}</view>
						<view class="box2">
							<text>{{item.title}}</text>
							<span v-if="item.start_time == 0">有效期：自领券日起{{item.day}}天</span>
							<span v-else>有效期：{{item.start_time}}</span>
						</view>
						<view class="box3" :style="item.choose?'background:#2E80FE;border:2rpx solid #2E80FE':''"
							@click="chooseItemyh(item)">
							<u-icon name="checkbox-mark" color="#fff" size="16"></u-icon>
						</view>
					</view>
					<view class="bottom">
						{{item.rule}}
					</view>
				</view>
				<view class="noYh">
					<view class="left">不使用优惠券</view>
					<view class="right" :style="notYh?'background:#2E80FE;border:2rpx solid #2E80FE':''"
						@click="chooseNotyh()"><u-icon name="checkbox-mark" color="#fff" size="16"></u-icon></view>
				</view>
				<view class="notcan">
					不可使用优惠券
				</view>
				<view class="cou_item" style="border: 2rpx solid #ADADAD;background: #fff;"
					v-for="(item,index) in nocouponlist" :key="index">
					<view class="top" style="border-bottom: 2rpx dashed #ADADAD;">
						<view class="box1" v-if="item.type == 0" style="color: #ADADAD ;">
							<span>满</span>{{item.full}}<span>减</span>{{item.discount}}
						</view>
						<view class="box1" style="color: #ADADAD ;"><span>￥</span>{{item.discount}}</view>
						<view class="box2">
							<text style="color: #ADADAD ;">{{item.title}}</text>
							<span v-if="item.start_time == 0">有效期：自领券日起{{item.day}}天</span>
							<span v-else>生效时间：{{item.start_time}}</span>
						</view>
					</view>
					<view class="bottom">
						{{item.rule}}
					</view>
				</view>
			</scroll-view>
		</view>

		<view class="choose_time" :style="showChoose?'':'height:0'">
			<view class="head">请选择时间</view>
			<view class="close" @click="showChoose = false">
				<image src="../static/images/9397.png" mode=""></image>
			</view>
			<view class="date">
				<view class="date_item" v-for="(item,index) in dateArr" :key="index"
					:style="currentDate == index?'color:#2E80FE;':''" @tap="tapDate(item,index)">
					<view class="">{{item.str}}</view>
					<view class="">{{item.date}}</view>
					<view class="hk" :style="currentDate == index?'':'display:none;'"></view>
				</view>
			</view>
			<scroll-view scroll-y="true" class="time_all">
				<view class="time_columns">
					<view class="time_column">
						<view class="time_item" v-for="(item, index) in timeArr.slice(0, 6)" :key="index"
							v-if="item.time && item.time1 && item.time2"
							:style="item.disabled ? 'background-color:#adadad;color:#fff;' : currentTime === index ? 'background-color:#2E80FE;color:#fff;' : ''"
							@tap="tapTime(item, index)">
							{{item.time}}
						</view>
					</view>
					<view class="time_column">
						<view class="time_item" v-for="(item, index) in timeArr.slice(6)" :key="index + 6"
							v-if="item.time && item.time1 && item.time2"
							:style="item.disabled ? 'background-color:#adadad;color:#fff;' : currentTime === index + 6 ? 'background-color:#2E80FE;color:#fff;' : ''"
							@tap="tapTime(item, index + 6)">
							{{item.time}}
						</view>
					</view>
				</view>
			</scroll-view>
			<view class="btn" @tap="confirmTime">确定预约时间</view>
		</view>

		<view class="address" @click="goUrl">
			<view class="left">
				<view class="top">
					<image src="../static/images/position.png" mode=""></image>
					<text style="color: #599eff;">{{mrAddress.address?mrAddress.address:'请先添加地址哦'}}</text>
				</view>
				<view class="bottom">{{mrAddress.address?mrAddress.userName+mrAddress.mobile:''}}</view>
			</view>
			<u-icon name="arrow-right" color="#333333" size="14"></u-icon>
		</view>
		<view class="time" @click="showChoose = true">
			<view class="left">
				<image src="../static/images/clock.png" mode=""></image>
				<text>{{conDate + (conTime ? ' ' + conTime : '')}}</text>
			</view>
			<u-icon name="arrow-right" color="#333333" size="14"></u-icon>
		</view>
		<view class="fg"></view>
		<view class="main">
			<view class="main_item" v-for="(item,index) in newItemArr" :key="index">
				<image :src="item.cover" mode=""></image>
				<view class="right">
					<view class="title">{{item.title}}</view>
					<view class="price" v-if="type == 0">
						<text>￥{{item.price}}/台</text>
						<u-number-box v-model="item.num" :min="1"></u-number-box>
					</view>
				</view>
			</view>
			<view class="expand" @click="expandAll" v-if="needShow">
				{{showEx?'展开详情':'收起'}}
				<view class="icon_box">
					<u-icon :name="showEx?'arrow-down':'arrow-up'" color="#ADADAD" size="14"></u-icon>
				</view>
			</view>
		</view>
		<view class="fg"></view>
		<view class="notes">
			<view class="title">服务备注</view>
			<textarea cols="25" rows="5" placeholder="想要额外嘱咐工作人员的可以备注哦~" v-model="notes"></textarea>
		</view>
		<view class="fg"></view>
		<view class="footer">
			<view class="left">总计￥{{allMoney}}</view>
			<view class="mid" :class="{'disabled': isSubmitting}" @click="addcar">
				{{isSubmitting ? '加入中...' : '加入购物车'}}
			</view>
			<view class="right" :class="{'disabled': isSubmitting}" @click="submit">
				{{isSubmitting ? '提交中...' : '立即下单'}}
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			couType: false, // 是否选择优惠券
			confirmCou: null, // 当前选中的优惠券
			type: '',
			id: '',
			tmplIds: [
				'qt5Q70oqYNTB2RaTzl0peEHpjK-iinWr3ZfxGvs4wtg',
				'DxiqXzK4yxCTYAqmeK9lEsU0A5-XCF9Fy7kSyX2vmnk',
				'_2z7Bbw8oq4av-yqP31fZLaI82Z_N52wNM_1ihXQv6I',
				'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
			],
			pid: 0,
			notYh: false,
			showYh: false,
			value: '',
			showEx: false,
			itemArr: [],
			newItemArr: [],
			needShow: false,
			notes: '',
			showChoose: false,
			content: '',
			currentDate: 0,
			timeArr: [
				{ disabled: false, time: '00:00-02:00', time1: '00:00:00', time2: '02:00:00' },
				{ disabled: false, time: '02:00-04:00', time1: '02:00:00', time2: '04:00:00' },
				{ disabled: false, time: '04:00-06:00', time1: '04:00:00', time2: '06:00:00' },
				{ disabled: false, time: '06:00-08:00', time1: '06:00:00', time2: '08:00:00' },
				{ disabled: false, time: '08:00-10:00', time1: '08:00:00', time2: '10:00:00' },
				{ disabled: false, time: '10:00-12:00', time1: '10:00:00', time2: '12:00:00' },
				{ disabled: false, time: '12:00-14:00', time1: '12:00:00', time2: '14:00:00' },
				{ disabled: false, time: '14:00-16:00', time1: '14:00:00', time2: '16:00:00' },
				{ disabled: false, time: '16:00-18:00', time1: '16:00:00', time2: '18:00:00' },
				{ disabled: false, time: '18:00-20:00', time1: '18:00:00', time2: '20:00:00' },
				{ disabled: false, time: '20:00-22:00', time1: '20:00:00', time2: '22:00:00' },
				{ disabled: false, time: '22:00-24:00', time1: '22:00:00', time2: '24:00:00' }
			],
			currentTime: -1,
			conDate: '选择可上门时间',
			conTime: '',
			dateArr: [],
			week: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
			mrAddress: {},
			couponlist: [], // 可用优惠券列表
			nocouponlist: [], // 不可用优惠券列表
			couponNum: '',
			form: {},
			isSubmitting: false // 添加提交状态标识，防止重复提交
		}
	},
	computed: {
		allMoney() {
			let num = 0;
			this.itemArr.forEach(item => {
				num += item.num * item.price;
			});
			if (this.confirmCou == null) {
				return num;
			} else {
				return (num * 1 - this.confirmCou.discount * 1) < 0 ? 0 : (num * 1 - this.confirmCou.discount * 1);
			}
		}
	},
	methods: {
		// Navigate to address page
		goUrl() {
			if (this.isSubmitting) return; // 防止在提交过程中跳转
			
			console.log('goUrl triggered');
			uni.navigateTo({
				url: '../user/address', // Ensure this path is correct
				success: () => {
					console.log('Navigation to address page successful');
				},
				fail: (err) => {
					console.error('Navigation failed:', err);
					uni.showToast({
						icon: 'none',
						title: '导航失败，请检查页面路径',
						duration: 2000
					});
				}
			});
		},
		
		addcar() {
			// 防止重复提交
			if (this.isSubmitting) {
				return;
			}

			// 设置提交状态
			this.isSubmitting = true;

			this.$api.service.addtocar({
				serviceId: this.id,
				num: this.newItemArr[0].num
			}).then(res => {
				uni.showToast({
					icon: 'success',
					title: '加入成功'
				});
				setTimeout(() => {
					// 重置提交状态
					this.isSubmitting = false;
					uni.redirectTo({
						url: '../user/order'
					});
				}, 1000);
			}).catch(err => {
				// 请求失败时重置提交状态
				this.isSubmitting = false;
				uni.showToast({
					icon: 'none',
					title: '加入购物车失败，请重试',
					duration: 2000
				});
				console.error('Add to cart failed:', err);
			});
		},
		
		submit() {
			// 防止重复提交
			if (this.isSubmitting) {
				return;
			}

			if (this.conDate == '选择可上门时间') {
				uni.showToast({
					icon: 'none',
					title: '请选择时间',
					duration: 1000
				});
				return;
			}

			if (!this.mrAddress.id) {
				uni.showToast({
					icon: 'none',
					title: '请先选择地址',
					duration: 1000
				});
				return;
			}

			if (this.currentTime === -1) {
				uni.showToast({
					icon: 'none',
					title: '请选择具体时间段',
					duration: 1000
				});
				return;
			}

			// 设置提交状态
			this.isSubmitting = true;

			// 修复时间格式化逻辑
			const currentYear = new Date().getFullYear();
			const selectedDateObj = this.dateArr[this.currentDate];
			const selectedTimeObj = this.timeArr[this.currentTime];
			
			// 构建完整的日期时间字符串
			let dateStr = selectedDateObj.fullDate; // 使用完整日期格式
			let startTimeStr = `${dateStr} ${selectedTimeObj.time1}`;
			let endTimeStr = `${dateStr} ${selectedTimeObj.time2}`;
			
			console.log('构建的时间字符串:', {
				startTimeStr,
				endTimeStr,
				selectedDate: selectedDateObj,
				selectedTime: selectedTimeObj
			});

			// 转换为时间戳
			let startTimestamp = new Date(startTimeStr).getTime() / 1000;
			let endTimestamp = new Date(endTimeStr).getTime() / 1000;
			
			// 验证时间戳是否有效
			if (isNaN(startTimestamp) || isNaN(endTimestamp)) {
				this.isSubmitting = false;
				uni.showToast({
					icon: 'none',
					title: '时间格式错误，请重新选择',
					duration: 2000
				});
				return;
			}

			let subForm = {
				type: this.type,
				pid: this.pid,
				addressId: this.mrAddress.id,
				serviceId: this.id,
				num: this.newItemArr[0].num || 1,
				startTime: startTimestamp,
				endTime: endTimestamp,
				text: this.notes,
				couponId: this.confirmCou == null ? '' : this.confirmCou.id,
			};
			
			console.log('提交表单数据:', subForm);
			 this.dingyue()
			//  uni.navigateTo({
			//  		url: `../user/wait_price?id=${this.id}`
			//  		// url: `../pages/mine`
			//  	});
			
			

			
			
			this.$api.service.subOrder(subForm).then(res => {
				uni.showToast({
					icon: 'success',
					title: '提交成功',
					duration: 500
				});
				
				setTimeout(() => {
					// 重置提交状态
					this.isSubmitting = false;
					uni.navigateTo({
						url: `../user/wait_price?id=${this.id}`
						// url: `../pages/mine`
					});
				}, 500);
			}).catch(err => {
				// 请求失败时重置提交状态
				this.isSubmitting = false;
				uni.showToast({
					icon: 'none',
					title: '提交失败，请重试',
					duration: 2000
				});
				console.error('Submit order failed:', err);
			});
		},
		dingyue() {
			const allTmplIds = this.tmplIds;
			if (allTmplIds.length < 3) {
				console.error("Not enough template IDs available:", allTmplIds);
				return;
			}
			const shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());
			const selectedTmplIds = shuffled.slice(0, 3);
			console.log("Selected template IDs:", selectedTmplIds);
			uni.requestSubscribeMessage({
				tmplIds: selectedTmplIds,
				success: (res) => {
					console.log('requestSubscribeMessage success:', res, 'with tmplIds:', selectedTmplIds);
				},
				fail: (err) => {
					console.error('requestSubscribeMessage failed:', err, 'with tmplIds:', selectedTmplIds);
				}
			});
		},
		chooseNotyh() {
			if (this.isSubmitting) return; // 防止在提交过程中操作
			
			this.notYh = !this.notYh;
			if (this.notYh) {
				this.couponlist.forEach(item => {
					item.choose = false;
				});
				this.confirmCou = null;
			}
		},
		
		chooseItemyh(item) {
			if (this.isSubmitting) return; // 防止在提交过程中操作
			
			if (item.choose == false) {
				if (item.type == 0) {
					if (item.full * 1 > this.allMoney * 1) {
						uni.showToast({
							icon: 'none',
							title: '当前金额未满足使用条件'
						});
						return;
					}
				}
				this.couponlist.forEach(e => {
					e.choose = false;
				});
				item.choose = true;
				this.couType = true;
				this.confirmCou = item;
				this.notYh = false; // 选择优惠券时取消"不使用优惠券"
			} else {
				item.choose = false;
				this.couType = false;
				this.confirmCou = null;
			}
		},
		
		chooseYh() {
			if (this.isSubmitting) return; // 防止在提交过程中操作
			this.showYh = true;
		},
		
		expandAll() {
			if (this.showEx) {
				this.newItemArr = this.itemArr;
				this.showEx = false;
			} else {
				this.newItemArr = this.itemArr.slice(0, 2);
				this.showEx = true;
			}
		},
		
		confirmTime() {
			if (this.currentTime === -1) {
				uni.showToast({
					icon: 'none',
					title: '请选择预约时间',
					duration: 1000
				});
				return;
			}
			const selectedTime = this.timeArr[this.currentTime];
			if (selectedTime.disabled) {
				uni.showToast({
					icon: 'none',
					title: '该时间段不可用',
					duration: 1000
				});
				return;
			}
			this.conDate = this.dateArr[this.currentDate].date + '(' + this.dateArr[this.currentDate].str + ')';
			this.conTime = selectedTime.time;
			this.showChoose = false;
		},
		
		addLeadingZero(number) {
			return number < 10 ? '0' + number : number;
		},
		
		tapTime(item, index) {
			if (!item || !item.time || item.disabled) {
				uni.showToast({
					icon: 'none',
					title: '该时间段不可选择',
					duration: 1000
				});
				return;
			}
			// 单选逻辑：选择新时间段时，取消其他时间段的选中状态
			this.currentTime = index;
		},
		
		tapDate(item, index) {
			this.currentDate = index;
			this.currentTime = -1; // 重置时间选择
			this.conTime = ''; // 重置时间显示
			this.updateTimeAvailability(index);
		},
		
		// 更新时间段可用性
		updateTimeAvailability(dateIndex) {
			console.log('Updating time availability for dateIndex:', dateIndex);
			if (dateIndex === 0) { // 今天
				const now = new Date();
				const currentHour = now.getHours();
				const currentMinute = now.getMinutes();
				
				this.timeArr.forEach((item, index) => {
					if (!item.time1) {
						console.warn(`Invalid time slot at index ${index}:`, item);
						item.disabled = true;
						return;
					}
					const timeStart = parseInt(item.time1.split(':')[0]);
					const timeStartMinutes = parseInt(item.time1.split(':')[1]);
					// 如果当前时间已经超过了该时间段的开始时间，则禁用
					if (currentHour > timeStart || (currentHour === timeStart && currentMinute >= timeStartMinutes)) {
						item.disabled = true;
					} else {
						item.disabled = false;
					}
				});
			} else {
				// 其他日期，所有时间段都可用
				this.timeArr.forEach(item => {
					if (item.time1) {
						item.disabled = false;
					}
				});
			}
			console.log('Updated timeArr:', this.timeArr);
		},
		
		getTime() {
			const now = new Date();
			let currentDate = new Date(now);
			
			for (let i = 0; i < 4; i++) {
				const month = this.addLeadingZero(currentDate.getMonth() + 1);
				const date = this.addLeadingZero(currentDate.getDate());
				const day = currentDate.getDay();
				const year = currentDate.getFullYear();
				
				this.dateArr.push({
					str: i === 0 ? '今天' : this.week[day],
					date: month + '-' + date,
					fullDate: `${year}-${month}-${date}` // 添加完整日期格式
				});
				
				// 移动到下一天
				currentDate.setDate(currentDate.getDate() + 1);
			}
			
			// 初始化今天的时间可用性
			this.updateTimeAvailability(0);
		},
		
		async getdefultaddress() {
			try {
				let res = await this.$api.mine.getDefultAddress();
				this.mrAddress = res;
			} catch (err) {
				console.error('Get default address failed:', err);
			}
		},
		
		async getservice() {
			try {
				let res = await this.$api.service.serviceInfo(this.id);
				console.log(res);
				res.num = 1;
				this.itemArr.push(res);
			} catch (err) {
				console.error('Get service info failed:', err);
			}
		},
		
		async getList() {
			try {
				let status = 1;
				let res = await this.$api.service.myWelfare(status);
				console.log(res.data);
				console.log(res);
				let arr = [];
				let arr1 = [];
				res.list.forEach(item => {
					item.choose = false;
					if (item.service.length > 0) {
						item.service.forEach(e => {
							if (e.id == this.id) {
								arr.push(item);
							} else {
								arr1.push(item);
							}
						});
					} else {
						arr1.push(item);
					}
				});
				arr = [...new Set(arr)];
				arr1 = [...new Set(arr1)];
				this.couponlist = arr;
				this.nocouponlist = arr1;
				this.couponNum = arr.length;
			} catch (err) {
				console.error('Get coupon list failed:', err);
			}
		}
	},
	
	onLoad(options) {
		this.type = this.$store.state.service.type; // 获取当前用户选择的模式 0 一口价 1 报价
		console.log('======', this.type);
		console.log('======', options);
		this.id = options.id;
		this.pid = uni.getStorageSync('pid');
		console.log(this.pid);
		this.getdefultaddress();
		this.getservice();
		this.getTime();
		// this.getList();
	},
	
	onShow() {
		let that = this;
		if (this.itemArr.length && this.itemArr.length > 2) {
			this.showEx = true;
			this.needShow = true;
			this.newItemArr = this.itemArr.slice(0, 2);
		} else {
			this.newItemArr = this.itemArr;
		}
		uni.$once('chooseAddress', function(e) {
			that.mrAddress = e;
		});
	},
	
	// 页面隐藏时重置提交状态（可选）
	onHide() {
		// 如果页面隐藏时有正在进行的请求，可以选择是否重置状态
		// this.isSubmitting = false;
	},
	
	// 页面卸载时重置提交状态
	onUnload() {
		this.isSubmitting = false;
	},
	
	watch: {
		currentDate: {
			handler(nval) {
				this.updateTimeAvailability(nval);
			},
			immediate: true
		}
	}
}
</script>

<style scoped lang="scss">
.page {
	padding-bottom: 200rpx;

	::v-deep .u-popup__content {
		display: none;
	}

	::v-deep .u-number-box__plus {
		border-radius: 50%;
		width: 36rpx;
		height: 36rpx !important;
		background-color: #fff !important;
		border: 1px solid #000;

		text {
			font-size: 24rpx !important;
			line-height: 36rpx !important;
		}
	}

	::v-deep .u-number-box__minus {
		border-radius: 50%;
		width: 36rpx;
		height: 36rpx !important;
		background-color: #fff !important;
		border: 1px solid #000;

		text {
			font-size: 24rpx !important;
			line-height: 36rpx !important;
		}
	}

	::v-deep .u-number-box__minus--disabled {
		border: 1px solid #ADADAD;
	}

	::v-deep .u-number-box__input {
		background-color: #fff !important;
	}

	.choose_yh {
		padding-top: 40rpx;
		width: 750rpx;
		height: 1106rpx;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 0rpx 0rpx;
		opacity: 1;
		position: fixed;
		bottom: 0;
		z-index: 10088;
		transition: all 0.5s;

		.head {
			font-size: 32rpx;
			font-weight: 500;
			color: #171717;
			text-align: center;
			margin-bottom: 44rpx;
		}

		.close {
			position: absolute;
			top: 44rpx;
			right: 32rpx;

			image {
				width: 37rpx;
				height: 37rpx;
			}
		}

		.cou_item {
			margin: 0 auto;
			width: 690rpx;
			height: 202rpx;
			background: #DCEAFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			margin-bottom: 20rpx;
			border: 2rpx solid #2E80FE;

			.top {
				height: 150rpx;
				display: flex;
				align-items: center;
				padding-top: 26rpx;
				padding-left: 24rpx;
				padding-right: 14rpx;
				position: relative;
				border-bottom: 2rpx dashed #2E80FE;

				.box1 {
					text-align: center;
					width: 180rpx;
					font-size: 40rpx;
					font-weight: 500;
					color: #E72427;

					span {
						font-size: 20rpx;
					}
				}

				.box2 {
					margin-left: 28rpx;

					text {
						display: block;
						font-size: 32rpx;
						font-weight: 500;
						color: #171717;
						max-width: 450rpx;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis,

;
					}

					span {
						margin-top: 10rpx;
						font-size: 24rpx;
						font-weight: 400;
						color: #B2B2B2;
					}
				}

				.box3 {
					position: absolute;
					right: 22rpx;
					top: 40rpx;
					width: 40rpx;
					height: 40rpx;
					background: #fff;
					border: 2rpx solid #B2B2B2;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

			.bottom {
				padding: 0 24rpx;
				height: 50rpx;
				max-width: 500rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				line-height: 50rpx;
				font-size: 20rpx;
				font-weight: 400;
				color: #B2B2B2;
			}
		}

		.noYh {
			width: 690rpx;
			margin: 0 auto;
			margin-top: 52rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 22rpx;

			.left {
				font-size: 32rpx;
				font-weight: 500;
				color: #171717;
			}

			.right {
				width: 40rpx;
				height: 40rpx;
				background: #fff;
				border: 2rpx solid #B2B2B2;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.notcan {
			margin-top: 52rpx;
			margin-bottom: 20rpx;
			font-size: 24rpx;
			font-weight: 400;
			color: #B2B2B2;
			padding: 0 30rpx;
		}
	}

	.choose_time {
		padding-top: 40rpx;
		width: 750rpx;
		height: 920rpx;
		background: #FFFFFF;
		border-radius: 40rpx 40rpx 0rpx 0rpx;
		opacity: 1;
		position: fixed;
		bottom: 0;
		z-index: 10088;
		transition: all 0.5s;

		.head {
			font-size: 32rpx;
			font-weight: 500;
			color: #171717;
			text-align: center;
		}

		.close {
			position: absolute;
			top: 44rpx;
			right: 32rpx;

			image {
				width: 37rpx;
				height: 37rpx;
			}
		}

		.date {
			margin-top: 40rpx;
			display: flex;
			justify-content: space-around;
			align-items: center;

			.date_item {
				text-align: center;
				font-size: 28rpx;
				font-weight: 400;
				color: #171717;

				.hk {
					margin-top: 8rpx;
					width: 100%;
					height: 6rpx;
					background: #2E80FE;
					border-radius: 4rpx 4rpx 4rpx 4rpx;
					opacity: 1;
				}
			}
		}

		.time_all {
			margin-top: 10rpx;
			width: 750rpx;
			height: 520rpx; /* 调整高度以适应垂直滚动 */
			background: #F7F7F7;
			padding: 20rpx 10rpx;

			.time_columns {
				display: flex;
				justify-content: space-around;

				.time_column {
					width: 330rpx; /* 每列宽度，留出间距 */
					display: flex;
					flex-direction: column;
					gap: 10rpx; /* 垂直间距 */

					.time_item {
						width: 100%;
						height: 80rpx;
						background: #FFFFFF;
						border-radius: 16rpx;
						font-size: 24rpx;
						font-weight: 500;
						color: #333333;
						text-align: center;
						line-height: 80rpx;
					}
				}
			}
		}

		.btn {
			margin: 0 auto;
			margin-top: 28rpx;
			width: 686rpx;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			opacity: 1;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
			text-align: center;
			line-height: 98rpx;
		}
	}

	.footer {
		position: fixed;
		bottom: 0;
		width: 100%;
		height: 202rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		background-color: #fff;

		.left {
			font-size: 40rpx;
			font-weight: 600;
			color: #E72427
		}

		.mid {
			width: fit-content;
			height: 98rpx;
			border-radius: 40rpx;
			font-size: 26rpx;
			color: #2E80FE;
			line-height: 98rpx;
			text-align: center;
			font-weight: 700;
			border: 2rpx solid #2E80FE;
			padding: 0 15rpx;
		}

		.right {
			width: 294rpx;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 50rpx 50rpx 50rpx 50rpx;
			opacity: 1;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 98rpx;
			text-align: center;
		}
	}

	.fg {
		height: 20rpx;
		background-color: #F3F4F5;
	}

	.address {
		height: 164rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx;

		.left {
			.top {
				display: flex;
				align-items: center;

				image {
					width: 36rpx;
					height: 36rpx;
					margin-right: 20rpx;
				}

				text {
					font-size: 28rpx;
					font-weight: 500;
					color: #171717;
					max-width: 400rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}

			.bottom {
				font-size: 24rpx;
				font-weight: 400;
				color: #ADADAD;
				padding-left: 56rpx;
				margin-top: 12rpx;
			}
		}
	}

	.time {
		border-top: 2rpx solid #F0F0F0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 120rpx;
		padding: 0 32rpx;

		.left {
			display: flex;
			align-items: center;

			image {
				width: 36rpx;
				height: 36rpx;
				margin-right: 20rpx;
			}

			text {
				font-size: 28rpx;
				font-weight: 500;
				color: #2E80FE;
			}
		}
	}

	.main {
		padding: 40rpx 32rpx;
		position: relative;
		padding-bottom: 70rpx;

		.expand {
			width: 690rpx;
			margin: 0 auto;
			font-size: 28rpx;
			font-weight: 400;
			color: #ADADAD;
			text-align: center;
			position: absolute;
			bottom: 0;

			.icon_box {
				display: flex;
				justify-content: center;
			}
		}

		.main_item {
			display: flex;

			image {
				width: 160rpx;
				height: 160rpx;
				margin-right: 20rpx;
			}

			.right {
				flex: 1;

				.title {
					font-size: 28rpx;
					font-weight: 500;
					color: #171717;
					max-width: 450rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.price {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-top: 80rpx;

					text {
						font-size: 28rpx;
						font-weight: 500;
						color: #2E80FE;
					}
				}
			}
		}
	}

	.notes {
		padding: 40rpx 32rpx;

		.title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333333;
		}

		textarea {
			margin-top: 40rpx;
			padding: 40rpx 30rpx;
			box-sizing: border-box;
			width: 686rpx;
			height: 242rpx;
			background: #F7F7F7;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			opacity: 1;
		}
	}

	.preferential {
		display: flex;
		justify-content: space-between;
		padding: 40rpx 32rpx;
		align-items: center;

		.left {
			font-size: 24rpx;
			font-weight: 400;
			color: #333333;
		}

		.right {
			display: flex;
			align-items: center;

			text {
				font-size: 24rpx;
				font-weight: 400;
				color: #E72427;
			}
		}
	}
}
</style>