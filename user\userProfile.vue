<template>
  <view class="container">
    <view class="user-info">
      <view class="avatar-wrapper">
        <button
          class="choose-avatar-button"
          open-type="chooseAvatar"
          @chooseavatar="onChooseAvatar"
        >
          <image
            class="avatar"
            :src="userInfo.avatarUrl || '/static/mine/default_user.png'"
            mode="aspectFill"
          />
        </button>
      </view>
      <view class="nickname-section">
        <text class="label">昵称：</text>
        <input
          class="nickname-input"
          type="nickname"
          placeholder="请输入昵称"
          v-model="localNickName"
          @blur="onNickNameBlur"
        />
      </view>
      <button class="save-button" @click="saveUserInfo">保存</button>
      <button class="save-button" @click="set">系统设置</button>
      <button class="save-button" @click="userOut">退出</button>
    </view>
  </view>
</template>

<script>
import { mapMutations } from 'vuex';

export default {
  data() {
    return {
      userInfo: {
        avatarUrl: '', // Store the temporary or uploaded avatar URL
      },
      localNickName: '微信用户', // Store the temporary nickname
      originalUserInfo: {
        avatarUrl: '',
        nickName: '',
      },
    };
  },
  onLoad() {
    this.loadUserInfo();
  },
  methods: {
    ...mapMutations(['updateUserItem']),
    set() {
      uni.openSetting({
        success(res) {
          console.log(res);
        },
      });
    },
    loadUserInfo() {
      const cachedUserInfo = uni.getStorageSync('userInfo') || {};
      this.userInfo.avatarUrl = cachedUserInfo.avatarUrl || '';
      this.localNickName = cachedUserInfo.nickName || '微信用户';
      this.originalUserInfo.avatarUrl = this.userInfo.avatarUrl;
      this.originalUserInfo.nickName = this.localNickName;
      console.log('Loaded user info:', cachedUserInfo);
    },
    onChooseAvatar(e) {
      console.log('onChooseAvatar event:', e);
      const { avatarUrl } = e.detail;
      if (avatarUrl) {
        this.userInfo.avatarUrl = avatarUrl;
        console.log('Selected avatar:', avatarUrl);
      } else {
        console.error('Failed to get avatarUrl from event detail.');
        uni.showToast({ title: '选择头像失败', icon: 'error' });
      }
    },
    onNickNameBlur(e) {
      this.localNickName = e.detail.value;
      console.log('Nickname input:', this.localNickName);
    },
    async uploadAvatarFile(tempFilePath) {
      uni.showLoading({ title: '上传中' });
      try {
        const response = await this.$api.base.uploadFile({
          filePath: tempFilePath,
          name: 'multipartFile',
          formData: {
            type: 'picture',
          },
        });

        if (response) {
          const imageUrl = response; // Assume response is the direct image URL, consistent with second code
          console.log('Avatar uploaded successfully:', imageUrl);
          uni.hideLoading();
          uni.showToast({ title: '上传成功', icon: 'success' });
          return imageUrl;
        } else {
          throw new Error('上传失败');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('Upload failed:', error);
        uni.showToast({
          title: error.message || '上传失败，请重试',
          icon: 'none',
        });
        throw error;
      }
    },
    async saveUserInfo() {
      uni.showLoading({ title: '保存中...' });
      try {
        let avatarUrl = this.userInfo.avatarUrl;
        // If avatar has changed, upload it first
        if (avatarUrl && avatarUrl !== this.originalUserInfo.avatarUrl) {
          avatarUrl = await this.uploadAvatarFile(avatarUrl);
          this.userInfo.avatarUrl = avatarUrl; // Update with uploaded URL
        }

        // Prepare updated user info
        const updatedUserInfo = {
          avatarUrl: avatarUrl || '',
          nickName: this.localNickName,
        };

        // Update backend
        console.log('Updating user info with:', updatedUserInfo);
        const res = await this.$api.user.updataInfo({
          nickName: updatedUserInfo.nickName,
          avatarUrl: updatedUserInfo.avatarUrl,
        });
        console.log('Update info response:', res);

        // Save to local storage
        uni.setStorageSync('userInfo', updatedUserInfo);
        console.log('Saved to local storage:', updatedUserInfo);

        // Update original info
        this.originalUserInfo.avatarUrl = updatedUserInfo.avatarUrl;
        this.originalUserInfo.nickName = updatedUserInfo.nickName;

        uni.hideLoading();
        uni.showToast({ title: '保存成功', icon: 'success' });

        // Navigate back or redirect
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
            success: () => console.log('Navigation back successful'),
            fail: (err) => {
              console.error('Navigation back failed:', err);
              uni.redirectTo({
                url: '/pages/mine/mine',
                success: () => console.log('Redirected to mine page'),
                fail: (redirectErr) => {
                  console.error('Redirect failed:', redirectErr);
                  uni.showToast({ title: '返回失败，请手动返回', icon: 'error' });
                },
              });
            },
          });
        }, 1000);
      } catch (err) {
        uni.hideLoading();
        console.error('Failed to save user info:', err);
        uni.showToast({
          title: '保存失败: ' + (err.message || '未知错误'),
          icon: 'error',
        });
      }
    },
    userOut() {
      uni.removeStorageSync('token');
      uni.removeStorageSync('userInfo');
      uni.removeStorageSync('phone');
      uni.removeStorageSync('avatarUrl');
      uni.removeStorageSync('nickName');
      // uni.removeStorageSync('shiInfo');
      uni.removeStorageSync('userId');

      this.updateUserItem({ key: 'autograph', val: '' });
      this.updateUserItem({ key: 'userInfo', val: {} });

      uni.showToast({ title: '已退出登录', icon: 'success', duration: 2000 });

      setTimeout(() => {
        uni.navigateBack({
          delta: 1,
          success: () => console.log('Navigation back successful'),
          fail: (err) => {
            console.error('Navigation back failed:', err);
            uni.redirectTo({
              url: '/pages/mine/mine',
              success: () => console.log('Redirected to mine page'),
              fail: (redirectErr) => {
                console.error('Redirect failed:', redirectErr);
                uni.showToast({ title: '返回失败，请手动返回', icon: 'error' });
              },
            });
          },
        });
      }, 1000);
    },
  },
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-top: 40rpx;

  .user-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #fff;
    width: 100%;
    padding: 40rpx;
    border-radius: 20rpx;
    margin-top: 40rpx;

    .avatar-wrapper {
      position: relative;
      width: 160rpx;
      height: 160rpx;
      margin-bottom: 40rpx;

      .choose-avatar-button {
        width: 100%;
        height: 100%;
        display: block;
        margin: 0;
        border-radius: 50%;
        border: none;
        background-color: transparent;
        overflow: hidden;
        line-height: normal;

        &::after {
          border: none;
        }

        .avatar {
          width: 100%;
          height: 100%;
          display: block;
          border: 1px solid #ddd;
          box-sizing: border-box;
        }
      }
    }

    .nickname-section {
      display: flex;
      align-items: center;
      width: 100%;
      margin-bottom: 40rpx;

      .label {
        font-size: 32rpx;
        color: #333;
        margin-right: 20rpx;
        white-space: nowrap;
      }

      .nickname-input {
        flex: 1;
        height: 80rpx;
        background-color: #f5f5f5;
        border-radius: 10rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        color: #333;
        border: 1px solid #ddd;
        box-sizing: border-box;
        min-width: 0;
      }
    }

    .save-button {
      width: 100%;
      height: 90rpx;
      background-color: #599eff;
      border-radius: 45rpx;
      color: #fff;
      font-size: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20rpx;
    }
  }
}
</style>