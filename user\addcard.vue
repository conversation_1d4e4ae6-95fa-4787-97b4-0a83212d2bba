<template>
	<view class="page">
		<view class="item">
			<view class="left">
				姓名
			</view>
			<view class="right">
				<input type="text" placeholder="请输入持卡人姓名" v-model="form.name">
			</view>
		</view>
		<view class="item">
			<view class="left">
				身份证
			</view>
			<view class="right">
				<input type="text" placeholder="请输入身份证号码" v-model="form.idCard">
			</view>
		</view>
		<view class="item">
			<view class="left">
				银行卡号
			</view>
			<view class="right">
				<input type="text" placeholder="请输入银行卡号" v-model="form.cardNo">
			</view>
		</view>
		<view class="item">
			<view class="left">
				开户银行
			</view>
			<view class="right">
				<input type="text" placeholder="请输入开户银行" v-model="form.bankName">
			</view>
		</view>
		<view class="item">
			<view class="left">
				银行预留手机号
			</view>
			<view class="right">
				<input type="text" placeholder="请输入手机号码" v-model="form.bankMobile">
			</view>
		</view>
		<view class="btn" @click="submit">提交信息</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				form: {
					name: '',
					idCard: '',
					cardNo: '',
					bankName: '',
					bankMobile: '',
					userId:'',
					id:''
				}
			}
		},
		methods: {
			submit() {
				let b = /^([1-9]{1})(\d{15}|\d{18})$/;
				if (!b.test(this.form.cardNo)) {
					uni.showToast({
						icon: 'none',
						title: '请输入正确的银行卡号'
					})
					return
				}
				let p = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
				if (p.test(this.form.idCard) == false) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的身份证号'
					})
					return
				}
				let phoneReg = /^1[3456789]\d{9}$/
				if (!phoneReg.test(this.form.bankMobile)) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的手机号',
					})
					return
				}
				this.$api.service.addcard(this.form).then(res=>{
					uni.showToast({
						icon:'success',
						title:'添加成功'
					})
					uni.redirectTo({
						url:'../user/bankCard'
					})
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		padding: 0 32rpx;

		.item {
			width: 686rpx;
			height: 122rpx;
			border-bottom: 2rpx solid #E9E9E9;
			display: flex;
			align-items: center;

			.left {
				font-size: 28rpx;
				font-weight: 500;
				color: #333333;
				width: 196rpx;
			}
		}

		.btn {
			position: fixed;
			bottom: 68rpx;
			width: 690rpx;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 50rpx 50rpx 50rpx 50rpx;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 98rpx;
			text-align: center;
		}
	}
</style>