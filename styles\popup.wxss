.common-popup-content {
	width: 620rpx;
	height: auto;
	padding: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;

	.title {
		font-size: 40rpx;
		font-family: Microsoft YaHei;
		font-weight: bold;
		color: #333;
	}

	.desc,
	.name {
		font-size: 24rpx;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #666;
		margin-top: 5rpx;
	}

	.name {
		color: #999;
		margin-top: 15rpx;
	}

	.image {
		width: 200rpx;
		height: 200rpx;
		border-radius: 15rpx;
		margin-top: 40rpx;
	}

	.image.middle {
		width: 300rpx;
		height: 300rpx;
	}

	.input {
		width: 480rpx;
		height: 110rpx;
		background: #F7F7F7;
	}
	.textarea {
		width: 480rpx;
		height: 300rpx;
		background: #F7F7F7;
	}

	.button {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 50rpx;

		.item-child {
			width: 240rpx;
			height: 90rpx;
			color: #666;
			background: #EEEEEE;
			border-radius: 45rpx;
			margin: 0 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}
