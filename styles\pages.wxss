/* 分享按钮 */
.common-share-btn {
	right: 30rpx;
	bottom: 140rpx;
	width: 90rpx;
	height: 90rpx;
	margin-bottom: calc(env(safe-area-inset-bottom) / 2);

	.iconfont {
		font-size: 40rpx;
	}
}

.common-share-btn.detail {
	bottom: 170rpx;
}

.map-info {
	.iconjuli {
		font-size: 42rpx;
	}

	.icon-down {
		transform: scale(0.5);
	}
}


/* 订单相关页面 */
.order-pages {

	.address-info {
		.address-icon {
			width: 64rpx;
			height: 64rpx;

			.iconfont {
				font-size: 38rpx;
			}
		}

		.username {
			font-size: 30rpx;
		}
	}


	.item-child {
		.grayscale {

			.c-title,
			.c-warning {
				color: #999;
			}
		}

		.cover {
			width: 155rpx;
			height: 155rpx;
		}

		/* .goods-title {
			max-width: 435rpx;
		} */

		.copy-btn {
			width: 60rpx;
			height: 32rpx;
			background: #EEEEEE;
			text-align: center;
		}

		.refund-img {
			width: 196rpx;
			height: 196rpx;
		}

		.refund-img:nth-child(3n) {
			margin-right: 0
		}
	}

	.item-textarea {
		width: 570rpx;
		height: 300rpx;
	}

	.menu-list {
		margin-top: -30rpx;

		.menu-title {
			height: 90rpx;

			.iconfont {
				font-size: 24rpx;
			}
		}

		.menu-line {
			width: 80%;
			top: 76rpx;
			left: 10%;
		}

		.item-child {
			width: 20%;
			margin: 10rpx 0;

			.item-img {
				width: 72rpx;
				height: 72rpx;
				z-index: 9;
				border: 1rpx solid #666;

				.iconfont {
					font-size: 40rpx;
				}
			}
		}

	}

	.footer-info {

		bottom: 0;

		.item-btn {
			width: 150rpx;
			height: 64rpx;
			background: #EEEEEE;
		}
	}

}


/* 申请表单相关页面 */
.apply-pages {
	.apply-form {

		.item-text {
			width: 200rpx;
			height: 30rpx;
			line-height: 30rpx;
			font-size: 30rpx;
			color: #1F1F1F;
		}

		.item-input {
			min-height: 30rpx;
			line-height: 30rpx;
			padding: 25rpx 0;
			font-size: 26rpx;
			color: #A9A9A9;
		}

		.item-input.text {
			padding: 30rpx 0;
		}

		.item-textarea {
			width: 630rpx;
			height: 400rpx;
			color: #A9A9A9;
		}

		.icon-switch,
		.icon-switch-on {
			font-size: 90rpx;
			line-height: 46rpx;
		}
	}
}

/* 储值明细记录 */
.stored-record-pages {
	.list-time {
		z-index: 99999;

		.item-child {
			width: 50%;
			height: 95rpx;

			.iconfont {
				font-size: 28rpx;
			}
		}
	}

	movable-area,
	movable-view {
		width: 686rpx;
		height: 148rpx;
		overflow: hidden;
	}

	.touch-item {
		font-size: 14px;
		display: flex;
		justify-content: space-between;
		width: 686rpx;
		height: 100%;
		overflow: hidden
	}

	.content {
		width: 100%;
		-webkit-transition: all 0.4s;
		transition: all 0.4s;
		-webkit-transform: translateX(270rpx);
		transform: translateX(270rpx);
		margin-left: -270rpx;
	}

	.delete-btn {
		width: 120rpx;
		height: 100%;
		color: #fff;
		border-radius: 0 15rpx 15rpx 0;
		-webkit-transform: translateX(270rpx);
		transform: translateX(270rpx);
		-webkit-transition: all 0.4s;
		transition: all 0.4s;
	}

	.touch-move-active .content,
	.touch-move-active .delete-btn {
		-webkit-transform: translateX(0);
		transform: translateX(0);
	}

	.popup-choose-time {
		.item-child {
			width: 50%;
		}
	}
}

.container {
	width: 100%;
}
