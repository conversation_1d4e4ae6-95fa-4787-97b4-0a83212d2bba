<template>
	<view class="page">
		<view style="color: #E7130E;" class="header">限时活动</view>
		<view class="box">
			<view class="name">{{info.name || ''}}</view>
			<view class="desc">今师傅限时特惠，空调清洗仅{{pricedata}}元，快来预约吧！杀菌除螨、去污除异味，给家人一份清新的呼吸！</view>
			<image :src="imgUrl" mode=""></image>
			<!-- <image src="http://*************:80/group1/M00/00/0B/CgAQCmhWKQaAQXsoAAAgedw8pn4344.jpg" mode=""></image> -->
			<view class="invite-code-container">
				<!-- <view class="invite-code">邀请码: {{ inviteCode|| '无'}}</view> -->
			</view>
		</view>
		<view class="button-container">
		<!-- 	<view class="btn save-btn" @click="saveImageWithPermission">保存图片</view> -->
			<button class="btn share-btn" open-type="share" :disabled="!imgUrl">分享</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			imgUrl: '',
			infos:'',
			inviteCode:'',
			pricedata:'',
			info: {}
		}
	},
	created() {
		//#ifdef MP-WEIXIN
		wx.showShareMenu({
			withShareTicket: true,
			menus: ['shareAppMessage', 'shareTimeline'],
			success: () => {
				console.log('Share menu enabled');
			},
			fail: (e) => {
				console.error('Failed to enable share menu:', e);
			}
		});
		//#endif
	},
	onShareAppMessage(res) {
		const inviteCode = this.inviteCode || '';
		const pricedatas=this.pricedata
		console.log(pricedatas)
		const shareData = {
			title: `今师傅限时特惠，空调清洗仅${pricedatas}元，快来预约吧！杀菌除螨、去污除异味，给家人一份清新的呼吸！`,
			path: `/pages/service`,
			imageUrl: this.imgUrl || ''
		};
		console.log('Sharing with:', shareData);
		return shareData;
	},
	methods: {
	
		
		getImg() {
			this.$api.service.huodongselectActivityConfig().then(res => {
				console.log('QR code fetched:', res);
				this.imgUrl = res.data.sharePictures;
				this.pricedata=res.data.payPrice
				// this.inviteCode = res.qrCode;
			}).catch(e => {
				console.error('Failed to fetch QR code:', e);
				uni.showToast({
					title: '获取二维码失败',
					icon: 'none'
				});
			});
		},
		
		copyInviteCode() {
			if (!this.info.inviteCode) {
				uni.showToast({
					title: '无邀请码',
					icon: 'none'
				});
				return;
			}
			uni.setClipboardData({
				data: this.info.inviteCode,
				success: () => {
					uni.showToast({
						title: '复制成功',
						icon: 'none'
					});
				},
				fail: (e) => {
					console.error('Copy failed:', e);
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					});
				}
			});
		}
	},
	onLoad() {
		console.log('User info loaded:', this.info);
		this.getImg();
	}
}
</script>

<style scoped lang="scss">
	.page {
		background: #f8f8f8;
		height: 100vh;
		padding: 40rpx 30rpx;

		.header {
			text-align: center;
			font-size: 52rpx;
			font-weight: 600;
			color: #000000;
		}

		.box {
			margin-top: 40rpx;
			width: 690rpx;
			height: 748rpx;
			background: #FFFFFF;
			border-radius: 32rpx;
			padding: 40rpx 0;
			display: flex;
			flex-direction: column;
			align-items: center;

			.name {
				font-size: 32rpx;
				font-weight: 600;
				color: #000000;
				text-align: center;
			}

			.desc {
				margin-top: 20rpx;
				font-size: 24rpx;
				text-align: center;
				font-weight: 400;
				color: #000000;
				padding: 0 30rpx;
			}

			image {
				width: 444rpx;
				height: 444rpx;
				margin: 18rpx auto 0;
			}

			.invite-code-container {
				display: flex;
				align-items: center;
				justify-content: center;
				margin-top: 20rpx;
				gap: 20rpx;
			}

			.invite-code {
				font-size: 28rpx;
				font-weight: 400;
				color: #000000;
			}

			.copy-btn {
				width: 120rpx;
				height: 60rpx;
				line-height: 60rpx;
				text-align: center;
				background: #2E80FE;
				color: #FFFFFF;
				font-size: 24rpx;
				border-radius: 30rpx;
			}
		}

		.button-container {
			display: flex;
			gap: 20rpx;
			position: absolute;
			bottom: 42rpx;
			width: 690rpx;
		}

		.btn {
			flex: 1;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 50rpx;
			line-height: 98rpx;
			text-align: center;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
			cursor: pointer;
			transition: all 0.3s ease;
			
			&:active {
				background: #1E70EE;
				transform: scale(0.98);
			}
		}

		.save-btn {
			/* Specific styles for save button if needed */
		}

		.share-btn {
			/* Ensure button inherits same styles */
			border: none;
			padding: 0;
			margin: 0;
			background: #2E80FE;
			/* Remove default button styles */
			&:after {
				border: none;
			}
		}

		.share-btn[disabled] {
			background: #cccccc;
			pointer-events: none;
		}
	}
</style>