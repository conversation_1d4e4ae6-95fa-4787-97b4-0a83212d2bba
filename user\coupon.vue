<template>
	<view class="page">
		<view class="header">
			<view
				class="header-item"
				v-for="(item, index) in list"
				:key="index"
				:style="currentIndex === index ? 'color:#2E80FE;' : ''"
				@tap="changeTab(index)"
			>
				{{ item.name }}
				<view
					class="tag"
					:style="currentIndex === index ? '' : 'background-color:#fff;'"
				></view>
			</view>
		</view>
		<scroll-view
			class="main"
			scroll-y
			:style="{ height: mainHeight }"
			@scrolltolower="onReachBottom"
		>
			<view
				class="main_item"
				v-for="(item, index) in couponlist"
				:key="index"
			>
				<view class="top">
					<view class="box1" v-if="item.type === 0">
						<span>满</span>{{ item.full }}<span>减</span>{{ item.discount }}
					</view>
					<view class="box1" v-else><span>￥</span>{{ item.discount }}</view>
					<view class="box2">
						<text>{{ item.title }}</text>
						<span v-if="item.startTime === 0"
							>有效期：自领券日起{{ item.day }}天</span
						>
						<span v-else>有效期：{{ item.startTime }}</span>
					</view>
					<view
						class="box3"
						@tap="goUrl"
						:style="
							currentIndex === 0
								? ''
								: 'background-color:#f2f3f4;color:#999;'
						"
					>
						{{ arr[currentIndex] }}
					</view>
				</view>
				<view class="bottom">
					{{ item.rule }}
				</view>
			</view>
			<!-- 加载状态提示 -->
			<view class="loading-status" v-if="loadingStatus">
				<text>{{ loadingText }}</text>
			</view>
			<!-- 空数据提示 -->
			<view
				class="empty-data"
				v-if="couponlist.length === 0 && !loadingStatus"
			>
				<text>暂无数据</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentIndex: 0,
			list: [
				{ name: '待使用' },
				{ name: '已使用' },
				{ name: '已过期' },
			],
			arr: ['去使用', '已使用', '已过期'],
			couponlist: [],
			page: 1,
			pageSize: 50,
			total: 0,
			loadingStatus: false,
			loadingText: '',
			hasMore: true,
			isRefreshing: false,
			isLoadingMore: false,
			mainHeight: '0px', // Dynamic height for scroll-view
		};
	},
	onLoad() {
		// Set scroll-view height dynamically
		uni.getSystemInfo({
			success: (res) => {
				const windowHeight = res.windowHeight;
				const headerHeight = 50; // Approximate header height in pixels
				this.mainHeight = `${windowHeight - headerHeight}px`;
			},
		});
		this.loadInitialData();
	},
	onPullDownRefresh() {
		console.log('Pull down refresh triggered');
		this.refreshData();
	},
	onReachBottom() {
		console.log('Reached bottom, loading more...');
		if (this.couponlist.length < this.total && !this.isLoadingMore) {
			this.page += 1;
			this.loadMoreData();
		}
	},
	methods: {
		changeTab(index) {
			if (this.currentIndex !== index) {
				this.currentIndex = index;
			}
		},
		goUrl() {
			if (this.currentIndex !== 0) return;
			uni.redirectTo({
				url: '/pages/technician',
			});
		},
		loadInitialData() {
			this.loadingStatus = true;
			this.loadingText = '正在加载...';
			this.getList().finally(() => {
				this.loadingStatus = false;
			});
		},
		refreshData() {
			if (this.isRefreshing) return;
			this.isRefreshing = true;
			this.page = 1;
			this.couponlist = [];
			this.hasMore = true;
			this.loadingStatus = true;
			this.loadingText = '正在刷新...';
			this.getList(false).finally(() => {
				this.isRefreshing = false;
				this.loadingStatus = false;
				uni.stopPullDownRefresh();
			});
		},
		loadMoreData() {
			if (this.isLoadingMore || !this.hasMore) return;
			this.isLoadingMore = true;
			this.loadingStatus = true;
			this.loadingText = '正在加载更多...';
			this.getList(true).finally(() => {
				this.isLoadingMore = false;
				this.loadingStatus = false;
			});
		},
		getList(append = false) {
			return new Promise((resolve, reject) => {
				const status = this.currentIndex + 1;
				// Mock API call for demonstration (replace with your actual API)
				this.$api.service
					.myWelfare({
						status: status,
						serviceId: -1,
						pageNum: this.page,
						pageSize: this.pageSize,
					})
					.then((res) => {
						console.log('API Response:', res);
						// Ensure response structure is as expected
						this.total = res.totalCount || 0;
						const newList = (res.list || []).map((item) => ({
							...item,
							// Ensure all required fields are present
							type: item.type || 0,
							full: item.full || 0,
							discount: item.discount || 0,
							title: item.title || '未知优惠券',
							startTime: item.startTime || 0,
							day: item.day || 0,
							rule: item.rule || '无使用规则',
						}));
						if (append) {
							this.couponlist = [...this.couponlist, ...newList];
						} else {
							this.couponlist = newList;
						}
						// Update hasMore based on data received
						this.hasMore =
							this.couponlist.length < this.total &&
							newList.length === this.pageSize;
						if (!this.hasMore && this.couponlist.length > 0) {
							this.loadingText = '没有更多数据了';
							this.loadingStatus = true;
							setTimeout(() => {
								this.loadingStatus = false;
							}, 1500);
						}
						resolve(res);
					})
					.catch((e) => {
						console.error('Failed to fetch coupon list:', e);
						if (append && this.page > 1) {
							this.page -= 1; // Roll back page number on failure
						}
						const errorMsg =
							typeof e === 'string' ? e : e.message || '获取优惠券失败';
						uni.showToast({
							icon: 'none',
							title: errorMsg,
						});
						reject(e);
					});
			});
		},
	},
	watch: {
		currentIndex(newVal) {
			this.page = 1;
			this.couponlist = [];
			this.total = 0;
			this.hasMore = true;
			this.loadingStatus = true;
			this.loadingText = '正在加载...';
			this.getList().finally(() => {
				this.loadingStatus = false;
			});
		},
	},
};
</script>

<style scoped lang="scss">
.page {
	height: 100vh;
	background-color: #f8f8f8;
	display: flex;
	flex-direction: column;
}

.header {
	background-color: #fff;
	height: 100rpx;
	display: flex;
	justify-content: space-around;
	align-items: center;
	border-top: 1px solid #f6f6f6;

	.header-item {
		.tag {
			width: 38rpx;
			height: 6rpx;
			background: #2e80fe;
			border-radius: 4rpx;
			margin: auto;
			margin-top: 10rpx;
		}
	}
}

.main {
	flex: 1;
	padding: 40rpx 30rpx;
	overflow-y: auto;

	.main_item {
		width: 690rpx;
		height: 202rpx;
		background: #ffffff;
		border-radius: 20rpx;
		margin-bottom: 20rpx;

		.top {
			height: 150rpx;
			display: flex;
			align-items: center;
			padding-top: 26rpx;
			padding-left: 24rpx;
			padding-right: 14rpx;
			position: relative;
			border-bottom: 2rpx solid #e9e9e9;

			.box1 {
				text-align: center;
				width: 185rpx;
				font-size: 40rpx;
				font-weight: 500;
				color: #e72427;

				span {
					font-size: 15rpx;
				}
			}

			.box2 {
				margin-left: 28rpx;

				text {
					display: block;
					font-size: 32rpx;
					font-weight: 500;
					color: #171717;
					max-width: 450rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				span {
					margin-top: 10rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #b2b2b2;
				}
			}

			.box3 {
				position: absolute;
				right: 24rpx;
				top: 24rpx;
				width: 100rpx;
				height: 42rpx;
				background: #2e80fe;
				border-radius: 22rpx;
				font-size: 20rpx;
				font-weight: 500;
				color: #ffffff;
				line-height: 42rpx;
				text-align: center;
			}
		}

		.bottom {
			padding: 0 24rpx;
			max-width: 500rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			line-height: 50rpx;
			font-size: 20rpx;
			font-weight: 400;
			color: #b2b2b2;
		}
	}

	.loading-status {
		text-align: center;
		padding: 40rpx 0;
		color: #999999;
		font-size: 28rpx;
	}

	.empty-data {
		text-align: center;
		padding: 200rpx 0;
		color: #999999;
		font-size: 32rpx;
	}
}
</style>