<template>
  <view class="page">
    <!-- 使用view替代scroll-view，避免滚动冲突 -->
    <view class="header">
      <image :src="serviceInfo.cover" mode="scaleToFill"></image>
    </view>
    <view class="content">
      <view class="card">
        <view class="top">
          <view class="title">{{serviceInfo.title}}</view>
          <view class="price" v-if="serviceInfo.servicePriceType !=1">￥{{serviceInfo.price}}</view>
        </view>
        <view class="bottom">
          <view class="left">已选：</view>
          <view class="right">
            <view class="tag" v-for="(item,index) in chooseArr" :key="index">{{item.name}}</view>
          </view>
        </view>
      </view>
      <view class="chol" v-for="(item,index) in list" :key="index">
        <view class="choose">
          <view class="title"><span v-if="item.isRequired == 1">*</span>{{item.problemDesc}}</view>
          <view class="desc">{{item.problemContent}}</view>
          <view class="cho_box">
            <view class="box_item" v-for="(newItem,newIndex) in item.options" :key="newIndex"
              :style="newItem.choose?'border:2rpx solid #2E80FE;color: #2E80FE;':''"
              @click="chooseOne(index,newIndex,item.inputType)">
              {{newItem.name}}
              <view class="ok" :style="newItem.choose? '' : 'display:none;'">
                <uni-icons type="checkmarkempty" size="8" color="#fff"></uni-icons>
              </view>
            </view>
          </view>
        </view>
        <view class="fg"></view>
      </view>
      
      <!-- 输入框部分 - 关键优化 -->
      <view class="chol" v-for="(item, index) in list2" :key="item.id">
        <view class="choose">
          <view class="title"><span v-if="item.isRequired == 1">*</span>{{item.problemDesc}}</view>
          <view class="desc">{{item.problemContent}}</view>
          <view class="input-container" :id="'input-container-' + index">
            <input  
              type="text" 
              v-model="form.data[index + list.length].val" 
              :placeholder="'请输入' + item.problemDesc"
              @focus="handleInputFocus(index)"
              @blur="handleInputBlur"
              @input="handleInput"
              class="form-input"
              cursor-spacing="10"
              confirm-type="done"
              :adjust-position="false"
              :auto-height="false"
            />
          </view>
        </view>
        <view class="fg"></view>
      </view>
      
      <view class="chol" v-for="(item,index) in list3" :key="index">
        <view class="choose">
          <view class="title"><span v-if="item.isRequired == 1">*</span>{{item.problemDesc}}</view>
          <view class="desc up">{{item.problemContent}}</view>
          <upload @upload="imgUpload" @del="imgUpload"
            :imagelist="form.data[form.data.findIndex(e=>e.serviceId == item.id)].val"
            :imgtype="form.data.findIndex(e=>e.serviceId == item.id)" text="上传图片" :imgsize="3">
          </upload>
        </view>
        <view class="fg"></view>
      </view>
      <view style="height: 300rpx;"></view> 
    </view>
    
    <!-- 底部按钮 -->
    <view class="footer" :style="footerStyle">
      <view class="righ" 
            :class="{ 'submitting': isSubmitting }" 
            @click="submit">
        {{ isSubmitting ? '提交中...' : '立即下单' }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      id: '',
      type: '',
      chooseArr: [],
      list: [], // 单选多选框
      list2: [], // 输入框
      list3: [], // 上传图片
      serviceInfo: {},
      form: {
        data: [],
        id: ''
      }, 	tmplIds: [
				'qt5Q70oqYNTB2RaTzl0peEHpjK-iinWr3ZfxGvs4wtg',
				'_2z7Bbw8oq4av-yqP31fZLaI82Z_N52wNM_1ihXQv6I',
				'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
			],
      btArr: [], // 必填项
      focusedInputIndex: -1,
      keyboardHeight: 0,
      windowHeight: 0,
      isKeyboardShow: false,
      systemInfo: {},
      scrollTimer: null,
      isSubmitting: false // 新增：提交状态标记
    }
  },
  
  computed: {
    footerStyle() {
      return {
        bottom: this.isKeyboardShow ? this.keyboardHeight + 'px' : '0px'
      }
    }
  },
  
  methods: {
			dingyue() {
					console.log('dingyue called');
					const allTmplIds = this.tmplIds;
					if (allTmplIds.length < 3) {
						console.error("Not enough template IDs available:", allTmplIds);
						// uni.showToast({
						// 	icon: 'none',
						// 	title: '模板ID不足'
						// });
						return;
					}
					const shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());
					const selectedTmplIds = shuffled.slice(0, 3);
					console.log("Selected template IDs:", selectedTmplIds);
					const templateData = selectedTmplIds.map((id, index) => ({
						templateId: id,
						templateCategoryId: index === 0 ? 10 : 5
					}));
					uni.requestSubscribeMessage({
						tmplIds: selectedTmplIds,
						success: (res) => {
							console.log('requestSubscribeMessage success:', res, 'with tmplIds:', this.tmplIds);
							// Check if any of the template IDs were rejected
							const hasRejection = this.tmplIds.some(tmplId => res[tmplId] === 'reject');
							const hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');
							if (hasRejection && !hasShownModal) {
								uni.showModal({
									title: '提示',
									content: '您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收师傅的报价通知。',
									cancelText: '取消',
									confirmText: '去开启',
									confirmColor: '#007AFF',
									success: (modalRes) => {
										uni.setStorageSync('hasShownSubscriptionModal', true);
										if (modalRes.confirm) {
											uni.openSetting({
												withSubscriptions: true
											});
										} else if (modalRes.cancel) {
											uni.setStorageSync('hasCanceledSubscription', true);
										}
									}
								});
							}
							this.templateCategoryIds = [];
							selectedTmplIds.forEach((templId, index) => {
								console.log(`Template ${templId} status: ${res[templId]}`);
								if (res[templId] === 'accept') {
									const templateCategoryId = templateData[index].templateCategoryId;
									if (templateCategoryId === 10) {
										for (let i = 0; i < 15; i++) {
											this.templateCategoryIds.push(templateCategoryId);
										}
									} else {
										this.templateCategoryIds.push(templateCategoryId);
									}
									console.log('Accepted message push for template:', templId);
								}
							});
							console.log('Updated templateCategoryIds:', this.templateCategoryIds);
						},
						fail: (err) => {
							console.error('requestSubscribeMessage failed:', err);
						}
					});
				},
    handleInputFocus(index) {
      console.log('输入框获得焦点:', index);
      this.focusedInputIndex = index;
      this.isKeyboardShow = true;
      
      // 清除之前的定时器
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }
      
      // 多次尝试滚动，确保定位准确
      this.scrollToInput(index);
      
      this.scrollTimer = setTimeout(() => {
        this.scrollToInput(index);
      }, 200);
      
      this.scrollTimer = setTimeout(() => {
        this.scrollToInput(index);
      }, 400);
      
      this.scrollTimer = setTimeout(() => {
        this.scrollToInput(index);
      }, 600);
    },
    
    handleInputBlur() {
      console.log('输入框失去焦点');
      this.focusedInputIndex = -1;
      this.isKeyboardShow = false;
      this.keyboardHeight = 0;
      
      // 清除定时器
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
        this.scrollTimer = null;
      }
    },
    
    handleInput(e) {
      console.log('输入内容:', e.detail.value);
    },
    
    scrollToInput(index) {
      const query = uni.createSelectorQuery().in(this);
      
      // 同时获取输入框和页面信息
      query.select(`#input-container-${index}`).boundingClientRect();
      query.selectViewport().scrollOffset();
      
      query.exec((res) => {
        if (res && res[0] && res[1]) {
          const inputRect = res[0];
          const pageScrollInfo = res[1];
          
          console.log('输入框位置信息:', {
            inputRect,
            pageScrollInfo,
            systemInfo: this.systemInfo
          });
          
          // 输入框距离页面顶部的绝对位置
          const inputAbsoluteTop = inputRect.top + pageScrollInfo.scrollTop;
          
          // 获取当前系统信息
          const systemInfo = uni.getSystemInfoSync();
          const windowHeight = systemInfo.windowHeight;
          const statusBarHeight = systemInfo.statusBarHeight || 0;
          
          // 预估键盘高度（一般占屏幕高度的40-50%）
          let keyboardHeight = this.keyboardHeight;
          if (!keyboardHeight || keyboardHeight < 100) {
            keyboardHeight = windowHeight * 0.45; // 预估键盘高度
          }
          
          // 计算可视区域高度（窗口高度 - 键盘高度）
          const visibleHeight = windowHeight - keyboardHeight;
          
          // 计算安全位置：让输入框显示在可视区域的上1/3处
          const safePosition = visibleHeight * 0.3;
          
          // 计算目标滚动位置
          const targetScrollTop = inputAbsoluteTop - safePosition - statusBarHeight;
          
          console.log('滚动计算详情:', {
            inputAbsoluteTop,
            windowHeight,
            keyboardHeight,
            visibleHeight,
            safePosition,
            targetScrollTop,
            currentScrollTop: pageScrollInfo.scrollTop
          });
          
          // 只有当需要滚动的距离超过50px时才执行滚动
          if (targetScrollTop > 0 && Math.abs(targetScrollTop - pageScrollInfo.scrollTop) > 50) {
            uni.pageScrollTo({
              scrollTop: Math.max(0, targetScrollTop), // 确保不会滚动到负数位置
              duration: 300,
              success: () => {
                console.log('页面滚动成功到:', targetScrollTop);
              },
              fail: (err) => {
                console.error('页面滚动失败:', err);
              }
            });
          } else {
            console.log('无需滚动或滚动距离太小');
          }
        } else {
          console.error('获取元素位置信息失败:', res);
        }
      });
    },
    
    imgUpload(e) {
      let { imagelist, imgtype } = e;
      let newFormData = [...this.form.data];
      newFormData[imgtype] = {
        ...newFormData[imgtype],
        val: [...imagelist]
      };
      this.$set(this.form, 'data', newFormData);
    },
    
    async getpzinfo() {
      await this.$api.service.getPz({
        id: this.id,
        type: this.type
      }).then(res => {
        res.forEach(item => {
          if (item.isRequired == 1) {
            this.btArr.push(item.id)
          }
          item.options = JSON.parse(item.options)
          item.options = item.options.map(e => {
            return {
              serviceId: item.id,
              name: e,
              choose: false
            }
          })
        })
        // this.list = res.filter(item => item.inputType == 3 || item.inputType == 4)
         this.list = res
                // 1. First, get all single-choice or multiple-choice questions
                .filter(item => item.inputType == 3 || item.inputType == 4)
                // 2. Then, for each question, create a new object
                .map(item => ({
                  ...item, // Copy all original properties of the question
                  // 3. Overwrite the 'options' array with a filtered version
                  options: item.options.filter(opt => opt.name === '挂式空调')
                }))
        this.list.forEach((newItem, newIndex) => {
          this.form.data.push({
            "serviceId": newItem.id,
            "settingId": this.id,
            "val": []
          })
        })
		console.log(this.list)
        this.list2 = res.filter(item => item.inputType == 1)
        this.list2.forEach((newItem, newindex) => {
          this.form.data.push({
            "serviceId": newItem.id,
            "settingId": this.id,
            "val": ''
          })
        })
		console.log(this.list2)
        this.list3 = res.filter(item => item.inputType == 2)
		console.log(this.list3)
        this.list3.forEach((newItem, newindex) => {
          this.form.data.push({
            "serviceId": newItem.id,
            "settingId": this.id,
            "val": []
          })
        })
      })
    },
    getcount(){
	this.$api.service.huodongcount().then(res=>{
		console.log(res)
		if(res.code==="-1"){
			uni.showToast({
			  icon: 'none',
			  title: res.msg,
			  duration: 3000
			})
		}
	})	
	},
    submit() {
      // 防止重复提交
      if (this.isSubmitting) {
        return;
      }
      
      this.isSubmitting = true; // 设置提交状态
      
      let copy_form = JSON.parse(JSON.stringify(this.form))
      console.log(copy_form)
      this.chooseArr.forEach(item => {
        copy_form.data[copy_form.data.findIndex(e => e.serviceId == item.serviceId)].val.push(item.name)
      })
      let open = true
      copy_form.data.forEach(item => {
        let index = this.btArr.findIndex(e => e == item.serviceId)
        if (index != -1 && (item.val == '' || (Array.isArray(item.val) && item.val.length === 0))) {
          uni.showToast({
            icon: 'none',
            title: '请填写完整后提交',
            duration: 1500
          })
          open = false
          return
        }
        // Fill empty val with "无"
        if (item.val == '' || (Array.isArray(item.val) && item.val.length === 0)) {
          item.val = "无"
        }
      })
      
      if (!open) {
        this.isSubmitting = false; // 验证失败时重置状态
        return;
      }
      
      if (open) {
        copy_form.data = copy_form.data.map(item => ({
          ...item,
          serviceId: item.settingId,
          settingId: item.serviceId
        }))
        
        copy_form.data.forEach(item => {
          let type = typeof item.val
          if (type != 'string') {
            if (Array.isArray(item.val) && item.val.length > 0 && typeof item.val[0] != 'string') {
              item.val = item.val.map(e => {
                return e.path
              }).join(',')
            } else if (Array.isArray(item.val)) {
              item.val = item.val.join(',')
            }
          }
        })  
	
        console.log(copy_form)
        this.$api.service.postHuoDong(copy_form).then(res => {
         if(res.code==="200"){
			 	// this.dingyue()
           uni.navigateTo({
             url: `/user/huodong_order?id=${this.id}`
           })
		   
         }else{
           uni.showToast({
             icon: 'none',
             title: res.msg,
             duration: 1000
           });
		   return
         }
        }).catch(err => {
          console.error('提交失败:', err);
          uni.showToast({
            icon: 'error',
            title: '网络错误，请重试',
            duration: 1000
          });
        }).finally(() => {
          // 无论成功失败都重置提交状态
          this.isSubmitting = false;
        })
      }
    },
    
    chooseOne(i, j, inputType) {
      this.list[i].options[j].choose = !this.list[i].options[j].choose
      if (inputType == 3) {
        this.list[i].options.forEach((item, index) => {
          if (index == j) return
          item.choose = false
        })
        this.chooseArr = []
        this.list.forEach(item => {
          item.options.forEach(tem => {
            if (tem.choose) {
              this.chooseArr.push(tem)
            }
          })
        })
      } else if (inputType == 4) {
        this.chooseArr = []
        this.list.forEach(item => {
          item.options.forEach(tem => {
            if (tem.choose) {
              this.chooseArr.push(tem)
            }
          })
        })
      }
    },
    
    async getInfo() {
      await this.$api.service.serviceInfo(this.id).then(res => {
        this.serviceInfo = res
      })
    }
  },
  
  onLoad(options) {
	  this.getcount()
    this.id = options.id
    this.type = options.type
    this.form.id = options.id
    this.getInfo()
    this.getpzinfo()
    
    // 获取系统信息
    uni.getSystemInfo({
      success: (res) => {
        this.systemInfo = res;
        this.windowHeight = res.windowHeight;
        console.log('获取到系统信息:', res);
      }
    });
  },
  
  // 监听键盘高度变化
  onKeyboardHeightChange(res) {
    console.log('键盘高度变化事件:', res);
    this.keyboardHeight = res.height;
    this.isKeyboardShow = res.height > 0;
    
    // 键盘高度变化时，如果有聚焦的输入框，重新调整位置
    if (this.focusedInputIndex >= 0) {
      setTimeout(() => {
        this.scrollToInput(this.focusedInputIndex);
      }, 50);
    }
  },
  
  // 页面显示时重置状态
  onShow() {
    this.focusedInputIndex = -1;
    this.isKeyboardShow = false;
    this.keyboardHeight = 0;
    this.isSubmitting = false; // 重置提交状态
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }
  },
  
  // 页面隐藏时清理
  onHide() {
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }
  },
  
  // 页面卸载时清理
  onUnload() {
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }
  },
  
  watch: {}
}
</script>

<style scoped lang="scss">
.page {
  min-height: 100vh;
  position: relative;
  padding-bottom: 200rpx;
}

.header {
  width: 750rpx;
  height: 376rpx;
  position: absolute;
  top: -300rpx;
  left: 0;
  z-index: -999;
}

.header image {
  width: 100%;
  height: 100%;
}

.content {
  margin-top: 280rpx;
}

.card {
  margin-left: 32rpx;
  width: 686rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 8rpx 2rpx rgba(0, 0, 0, 0.16);
  border-radius: 16rpx;
  padding: 40rpx;
}

.card .top {
  padding-bottom: 40rpx;
  border-bottom: 2rpx solid #F2F3F6;
}

.card .top .title {
  font-size: 36rpx;
  font-weight: 500;
  color: #171717;
  letter-spacing: 2rpx;
}

.card .top .price {
  margin-top: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #E72427;
}

.card .bottom {
  padding-top: 24rpx;
  display: flex;
}

.card .bottom .left {
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  padding-top: 10rpx;
}

.card .bottom .right {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.card .bottom .right .tag {
  width: fit-content;
  height: 44rpx;
  padding: 0 12rpx;
  background: #DCEAFF;
  border-radius: 4rpx;
  font-size: 16rpx;
  font-weight: 400;
  color: #2E80FE;
  line-height: 44rpx;
  text-align: center;
  margin: 10rpx;
}

.chol .choose {
  padding: 40rpx 32rpx;
}

.chol .choose .title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.chol .choose .title span {
  color: #E72427;
}

.chol .choose .input-container {
  margin-top: 40rpx;
  position: relative;
  width: 100%;
  min-height: 88rpx;
}

.chol .choose .form-input {
  box-sizing: border-box;
  width: 100%;
  height: 88rpx;
  background: #F7F7F7;
  border-radius: 12rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  line-height: 88rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}

.chol .choose .form-input:focus {
  background: #fff;
  border-color: #2E80FE;
  box-shadow: 0 0 0 4rpx rgba(46, 128, 254, 0.1);
  outline: none;
}

.chol .choose .desc {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
}

.chol .choose .up {
  margin-bottom: 40rpx;
}

.chol .choose .cho_box {
  margin-top: 20rpx;
  display: flex;
  flex-wrap: wrap;
}

.chol .choose .cho_box .box_item {
  width: fit-content;
  padding: 0 20rpx;
  height: 60rpx;
  background: #FFFFFF;
  border-radius: 4rpx;
  border: 2rpx solid #D8D8D8;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
  line-height: 60rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.chol .choose .cho_box .box_item .ok {
  width: 20rpx;
  height: 20rpx;
  position: absolute;
  right: 0;
  bottom: 0;
  background-color: #2E80FE;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chol .fg {
  width: 750rpx;
  height: 20rpx;
  background: #F3F4F5;
}

.footer {
  padding: 38rpx 32rpx;
  width: 750rpx;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  transition: bottom 0.25s ease;

  .righ {
    width: 690rpx;
    height: 88rpx;
    background: #2e80fe;
    border-radius: 44rpx;
    font-size: 28rpx;
    font-weight: 400;
    color: #ffffff;
    line-height: 88rpx;
    text-align: center;
    transition: all 0.2s ease;
    
    &.submitting {
      background: #8bb8ff;
      opacity: 0.7;
      pointer-events: none;
    }
  }
}

/* iOS安全区域适配 */
@supports (bottom: env(safe-area-inset-bottom)) {
  .footer {
    padding-bottom: calc(38rpx + env(safe-area-inset-bottom));
  }
}
</style>