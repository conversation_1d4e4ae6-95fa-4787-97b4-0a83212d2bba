<template>
	<view>
		<view class="">
		<rich-text :nodes="info"></rich-text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				info:''
			}
		},
		methods: {
			
		},
		onLoad(options) {
			console.log(options)
			if(options.type==="privacy"){
				this.$api.base.getConfig().then(res=>{
					console.log(res)
					this.info=res.content
					// console.log(this.info)
				})
			}
			this.$api.base.getConfig().then(res=>{
				console.log(res)
				this.info=res.loginProtocol
				// console.log(this.info)
			})
		}
	}
</script>

<style>

</style>
