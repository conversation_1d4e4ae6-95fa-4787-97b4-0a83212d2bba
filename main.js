import Vue from 'vue'
import App from './App'
// import './index.css'
import api from "api/index.js"
import util from "@/utils/index.js"
// #ifdef H5
import jweixin from "@/utils/jweixin.js"
// #endif
import store from "@/store/index.js"
import upload from '@/components/upload.vue'
import uView from "uview-ui";

Vue.use(uView);
Vue.component('upload', upload)
// 挂载全局跳转方法
Vue.prototype.$navigateTo = (url) => {
  uni.navigateTo({
    url,
    fail: (err) => {
      console.error("跳转失败:", err);
      uni.showToast({
        title: "跳转失败: " + err.errMsg,
        icon: "none"
      });
    }
  });
};
Vue.prototype.$api = api
Vue.prototype.$util = util
// #ifdef H5
Vue.prototype.$jweixin = jweixin
// #endif
Vue.config.productionTip = false

App.mpType = 'app'

const app = new Vue({
	...App,
	store,
})
app.$mount()